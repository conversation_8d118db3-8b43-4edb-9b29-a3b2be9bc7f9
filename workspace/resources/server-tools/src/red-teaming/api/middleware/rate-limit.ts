import { Request, Response, NextFunction } from 'express';

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

const store: RateLimitStore = {};

/**
 * Simple rate limiting middleware
 */
export function rateLimit(req: Request, res: Response, next: NextFunction): void {
  const clientId = req.user?.clientId || req.ip;
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute
  const maxRequests = 60; // 60 requests per minute

  // Clean up expired entries
  Object.keys(store).forEach(key => {
    if (store[key].resetTime < now) {
      delete store[key];
    }
  });

  // Initialize or get current count
  if (!store[clientId] || store[clientId].resetTime < now) {
    store[clientId] = {
      count: 1,
      resetTime: now + windowMs
    };
  } else {
    store[clientId].count++;
  }

  // Check if limit exceeded
  if (store[clientId].count > maxRequests) {
    res.status(429).json({
      success: false,
      error: 'Rate limit exceeded',
      retryAfter: Math.ceil((store[clientId].resetTime - now) / 1000)
    });
    return;
  }

  // Add rate limit headers
  res.setHeader('X-RateLimit-Limit', maxRequests);
  res.setHeader('X-RateLimit-Remaining', Math.max(0, maxRequests - store[clientId].count));
  res.setHeader('X-RateLimit-Reset', Math.ceil(store[clientId].resetTime / 1000));

  next();
}
