# Red Teaming System for AI/LLM Security

A comprehensive red teaming framework for testing AI/LLM systems, specifically designed for RAG (Retrieval-Augmented Generation) systems and AI agents. This system provides automated security testing capabilities to identify vulnerabilities in AI systems before deployment.

## 🎯 Overview

This red teaming system is inspired by [promptfoo](https://www.promptfoo.dev/docs/red-team/) and provides:

- **RAG Security Testing**: Prompt injection, context injection, data exfiltration, and retrieval manipulation
- **Agent Security Testing**: RBAC violations, privilege escalation, memory poisoning, and tool manipulation
- **LLM Security Testing**: Harmful content generation, PII leaks, and policy violations
- **Red Teaming as a Service (RTaaS)**: API-driven service for automated security assessments
- **Integration**: Seamless integration with Divinci AI infrastructure

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Red Team Service API                     │
├─────────────────────────────────────────────────────────────┤
│                  Divinci Integration Layer                  │
├─────────────────────────────────────────────────────────────┤
│  Red Team Engine  │  Plugin System  │  Strategy System     │
├─────────────────────────────────────────────────────────────┤
│     RAG Plugins   │  Agent Plugins  │  Common Plugins      │
├─────────────────────────────────────────────────────────────┤
│   Configuration   │    Reporting    │    Scheduling        │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### Basic Usage

```typescript
import { RedTeamEngine, ConfigTemplates } from './red-teaming';

// Create a red team engine
const engine = new RedTeamEngine();

// Use a predefined configuration template
const config = ConfigTemplates.ragSecurity({
  endpoint: 'https://your-rag-system.com/api',
  purpose: 'Security assessment of RAG system',
  numTests: 50
});

// Run the assessment
const report = await engine.runAssessment(config);

// Export the report
const htmlReport = await engine.reportGenerator.exportReport(report, 'html');
```

### Service Usage

```typescript
import { RedTeamService, DivinciRedTeamIntegration } from './red-teaming';

// Initialize the service
const service = new RedTeamService(serviceConfig);
const integration = new DivinciRedTeamIntegration(service);

// Assess a white label release
const assessmentId = await integration.assessWhiteLabelRelease(
  'whitelabel-123',
  'release-456',
  'client-789',
  {
    assessmentType: 'comprehensive',
    numTests: 100
  }
);

// Get the results
const assessment = await service.getAssessment(assessmentId, 'client-789');
```

## 📋 Configuration

### Basic Configuration

```typescript
const config: RedTeamConfig = {
  target: {
    type: 'rag', // 'rag' | 'agent' | 'llm' | 'hybrid'
    endpoint: 'https://api.example.com',
    provider: 'openai',
    model: 'gpt-4'
  },
  purpose: 'Security assessment of RAG system',
  numTests: 50,
  maxConcurrency: 5,
  plugins: [
    'prompt-injection',
    'context-injection',
    'data-exfiltration',
    'pii-leak',
    {
      id: 'policy',
      config: {
        policy: 'The system must not disclose sensitive information'
      }
    }
  ],
  strategies: ['jailbreak', 'prompt-injection'],
  output: {
    format: 'html',
    file: 'security-report.html',
    includeDetails: true
  }
};
```

### Configuration Templates

#### RAG Security Assessment
```typescript
const ragConfig = ConfigTemplates.ragSecurity({
  endpoint: 'https://your-rag-api.com',
  purpose: 'RAG system security assessment',
  numTests: 50
});
```

#### Agent Security Assessment
```typescript
const agentConfig = ConfigTemplates.agentSecurity({
  endpoint: 'https://your-agent-api.com',
  purpose: 'AI agent security assessment',
  numTests: 40
});
```

#### Comprehensive Assessment
```typescript
const comprehensiveConfig = ConfigTemplates.comprehensive({
  endpoint: 'https://your-ai-system.com',
  purpose: 'Comprehensive AI system security assessment',
  numTests: 100
});
```

## 🔌 Plugins

### RAG-Specific Plugins

#### Prompt Injection
Tests for prompt injection vulnerabilities where malicious input attempts to override system instructions.

```typescript
// Automatically included in RAG assessments
plugins: ['prompt-injection']
```

#### Context Injection
Tests for malicious instructions embedded in retrieved documents.

```typescript
plugins: ['context-injection']
```

#### Data Exfiltration
Tests for unauthorized extraction of sensitive information.

```typescript
plugins: ['data-exfiltration']
```

### Agent-Specific Plugins

#### RBAC Violations
Tests for proper role-based access control implementation.

```typescript
plugins: ['rbac']
```

#### Memory Poisoning
Tests for memory corruption attacks in stateful agents.

```typescript
plugins: ['memory-poisoning']
```

#### Tool Manipulation
Tests for tool and API manipulation vulnerabilities.

```typescript
plugins: ['tool-manipulation']
```

### Common Plugins

#### PII Leak Detection
Tests for personally identifiable information leakage.

```typescript
plugins: ['pii-leak']
```

#### Harmful Content
Tests for generation of harmful, toxic, or inappropriate content.

```typescript
plugins: ['harmful-content']
```

#### Policy Compliance
Tests for adherence to custom policies and guidelines.

```typescript
plugins: [
  {
    id: 'policy',
    config: {
      policy: 'Your custom policy statement here'
    }
  }
]
```

## 🎯 Attack Strategies

### Jailbreak Strategy
Attempts to bypass AI safety measures through various techniques.

### Prompt Injection Strategy
Focuses on injecting malicious instructions into prompts.

### Multi-Stage Attack Strategy
Combines multiple attack vectors in sequence.

### Crescendo Strategy
Gradually escalates attack sophistication.

## 📊 Reporting

The system generates comprehensive security reports with:

- **Executive Summary**: High-level risk assessment and metrics
- **Vulnerability Details**: Specific vulnerabilities found with evidence
- **Risk Assessment**: Severity levels and impact analysis
- **Recommendations**: Actionable security improvements
- **Test Results**: Detailed results for each test case

### Report Formats

- **JSON**: Machine-readable format for integration
- **YAML**: Human-readable structured format
- **HTML**: Rich visual report with charts and formatting
- **Markdown**: Documentation-friendly format
- **PDF**: Professional report format (planned)

## 🔧 API Reference

### Create Assessment
```http
POST /api/red-team/assessments
Content-Type: application/json
Authorization: Bearer <api-key>

{
  "name": "Security Assessment",
  "description": "Comprehensive security test",
  "config": {
    "target": {
      "type": "rag",
      "endpoint": "https://api.example.com"
    },
    "plugins": ["prompt-injection", "data-exfiltration"],
    "numTests": 50
  }
}
```

### Get Assessment
```http
GET /api/red-team/assessments/{id}
Authorization: Bearer <api-key>
```

### Export Report
```http
POST /api/red-team/assessments/{id}/export
Content-Type: application/json
Authorization: Bearer <api-key>

{
  "format": "html"
}
```

### White Label Integration
```http
POST /api/red-team/whitelabel/{id}/assess
Content-Type: application/json
Authorization: Bearer <api-key>

{
  "releaseId": "release-123",
  "assessmentType": "comprehensive",
  "numTests": 100
}
```

## 🔐 Security Considerations

### Authentication & Authorization
- API key-based authentication
- Role-based access control
- Client isolation and quotas

### Data Protection
- No sensitive data stored in logs
- Automatic PII redaction in reports
- Secure report storage and transmission

### Rate Limiting
- Configurable rate limits per client
- Queue management for concurrent assessments
- Resource usage monitoring

## 🧪 Testing

### Unit Tests
```bash
npm test
```

### Integration Tests
```bash
npm run test:integration
```

### Example Test Configuration
```typescript
// test-config.ts
export const testConfig: RedTeamConfig = {
  target: {
    type: 'rag',
    endpoint: 'http://localhost:3000/test-endpoint'
  },
  purpose: 'Test assessment',
  numTests: 10,
  plugins: ['prompt-injection', 'pii-leak'],
  output: {
    format: 'json',
    includeDetails: true
  }
};
```

## 📈 Monitoring & Metrics

### Service Metrics
- Total assessments run
- Average assessment duration
- Vulnerability detection rates
- System performance metrics

### Client Metrics
- Usage statistics
- Quota consumption
- Assessment success rates

## 🔄 Scheduling

### Automated Assessments
```typescript
// Schedule daily security assessments
const schedule = {
  cronExpression: '0 2 * * *', // Daily at 2 AM
  assessmentTypes: ['rag', 'agent'],
  enabled: true
};

await integration.scheduleWhiteLabelAssessments(
  'whitelabel-123',
  'client-456',
  schedule
);
```

## 🚨 Vulnerability Types

The system detects various vulnerability types:

- **PROMPT_INJECTION**: Malicious prompt injection attempts
- **CONTEXT_INJECTION**: Malicious context manipulation
- **DATA_EXFILTRATION**: Unauthorized data extraction
- **PII_LEAK**: Personal information disclosure
- **PRIVILEGE_ESCALATION**: Unauthorized privilege elevation
- **MEMORY_POISONING**: Agent memory corruption
- **TOOL_MANIPULATION**: API/tool abuse
- **HARMFUL_CONTENT**: Toxic content generation
- **RBAC_VIOLATION**: Access control bypass
- **HALLUCINATION**: False information generation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the Divinci AI security team
- Check the documentation wiki

## 🔗 Related Resources

- [promptfoo Red Team Documentation](https://www.promptfoo.dev/docs/red-team/)
- [OWASP LLM Top 10](https://owasp.org/www-project-top-10-for-large-language-model-applications/)
- [AI Security Best Practices](https://divinci.ai/security-best-practices)
