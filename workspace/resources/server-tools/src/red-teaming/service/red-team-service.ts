import { 
  RedTeamAssessment, 
  AssessmentStatus, 
  AssessmentRequest, 
  AssessmentResponse,
  AssessmentFilter,
  AssessmentSort,
  PaginationOptions,
  AssessmentListResponse,
  ServiceConfig,
  RedTeamClient,
  ClientPermissions,
  ServiceMetrics,
  AssessmentProgress
} from './types';
import { RedTeamEngine } from '../core/engine';
import { RedTeamConfig, RedTeamReport } from '../types';
import { AssessmentStorage } from './storage';
import { AssessmentQueue } from './queue';
import { ClientManager } from './client-manager';
import { NotificationService } from './notifications';
import { SchedulerService } from './scheduler';

/**
 * Red Teaming as a Service - Main Service Class
 */
export class RedTeamService {
  private engine: RedTeamEngine;
  private storage: AssessmentStorage;
  private queue: AssessmentQueue;
  private clientManager: ClientManager;
  private notifications: NotificationService;
  private scheduler: SchedulerService;
  private config: ServiceConfig;
  private runningAssessments: Map<string, Promise<void>> = new Map();

  constructor(config: ServiceConfig) {
    this.config = config;
    this.engine = new RedTeamEngine();
    this.storage = new AssessmentStorage(config.storage);
    this.queue = new AssessmentQueue(config);
    this.clientManager = new ClientManager(this.storage);
    this.notifications = new NotificationService(config);
    this.scheduler = new SchedulerService(this, config);

    this.initializeEventHandlers();
    this.startQueueProcessor();
  }

  /**
   * Initialize event handlers for the red team engine
   */
  private initializeEventHandlers(): void {
    this.engine.addEventHandler(async (event) => {
      try {
        await this.notifications.handleEvent(event);
      } catch (error) {
        console.error('Error handling red team event:', error);
      }
    });
  }

  /**
   * Start the queue processor
   */
  private startQueueProcessor(): void {
    setInterval(async () => {
      try {
        await this.processQueue();
      } catch (error) {
        console.error('Error processing assessment queue:', error);
      }
    }, this.config.queueProcessingInterval);
  }

  /**
   * Create a new red team assessment
   */
  async createAssessment(
    clientId: string, 
    request: AssessmentRequest
  ): Promise<AssessmentResponse> {
    // Validate client permissions
    const client = await this.clientManager.getClient(clientId);
    if (!client) {
      throw new Error('Client not found');
    }

    await this.validateAssessmentRequest(client, request);

    // Create assessment
    const assessment: RedTeamAssessment = {
      id: this.generateAssessmentId(),
      clientId,
      whitelabelId: request.config.target.provider, // If applicable
      name: request.name,
      description: request.description,
      config: request.config,
      status: AssessmentStatus.PENDING,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: request.metadata
    };

    // Save assessment
    await this.storage.saveAssessment(assessment);

    // Add to queue
    await this.queue.enqueue(assessment.id, 1); // Default priority

    // Create schedule if requested
    if (request.schedule) {
      await this.scheduler.scheduleAssessment(
        assessment.id,
        request.schedule.cronExpression,
        request.schedule.enabled
      );
    }

    // Update client usage
    await this.clientManager.updateUsage(clientId);

    const queuePosition = await this.queue.getPosition(assessment.id);
    const estimatedDuration = this.estimateAssessmentDuration(request.config);

    return {
      assessment,
      estimatedDuration,
      queuePosition
    };
  }

  /**
   * Get assessment by ID
   */
  async getAssessment(assessmentId: string, clientId: string): Promise<RedTeamAssessment | null> {
    const assessment = await this.storage.getAssessment(assessmentId);
    
    if (!assessment || assessment.clientId !== clientId) {
      return null;
    }

    return assessment;
  }

  /**
   * List assessments with filtering and pagination
   */
  async listAssessments(
    clientId: string,
    filter?: AssessmentFilter,
    sort?: AssessmentSort,
    pagination?: PaginationOptions
  ): Promise<AssessmentListResponse> {
    const combinedFilter = { ...filter, clientId };
    
    const { assessments, total } = await this.storage.listAssessments(
      combinedFilter,
      sort,
      pagination
    );

    return {
      assessments,
      total,
      page: pagination?.page || 1,
      pageSize: pagination?.pageSize || 20
    };
  }

  /**
   * Cancel a running assessment
   */
  async cancelAssessment(assessmentId: string, clientId: string): Promise<boolean> {
    const assessment = await this.getAssessment(assessmentId, clientId);
    if (!assessment) {
      return false;
    }

    if (assessment.status === AssessmentStatus.RUNNING) {
      // Cancel running assessment
      const runningPromise = this.runningAssessments.get(assessmentId);
      if (runningPromise) {
        // Note: In a real implementation, you'd need a way to cancel the promise
        this.runningAssessments.delete(assessmentId);
      }
    }

    // Update status
    assessment.status = AssessmentStatus.CANCELLED;
    assessment.updatedAt = new Date();
    await this.storage.updateAssessment(assessment);

    // Remove from queue if pending
    await this.queue.remove(assessmentId);

    return true;
  }

  /**
   * Get assessment progress
   */
  async getAssessmentProgress(assessmentId: string, clientId: string): Promise<AssessmentProgress | null> {
    const assessment = await this.getAssessment(assessmentId, clientId);
    if (!assessment) {
      return null;
    }

    // In a real implementation, this would track actual progress
    return {
      assessmentId,
      totalTests: assessment.config.numTests || 0,
      completedTests: assessment.status === AssessmentStatus.COMPLETED ? (assessment.config.numTests || 0) : 0,
      vulnerabilitiesFound: assessment.report?.summary.vulnerabilitiesFound || 0,
      lastUpdate: assessment.updatedAt
    };
  }

  /**
   * Export assessment report
   */
  async exportReport(
    assessmentId: string, 
    clientId: string, 
    format: 'json' | 'yaml' | 'html' | 'markdown'
  ): Promise<string> {
    const assessment = await this.getAssessment(assessmentId, clientId);
    if (!assessment || !assessment.report) {
      throw new Error('Assessment or report not found');
    }

    return await this.engine.reportGenerator.exportReport(assessment.report, format);
  }

  /**
   * Get service metrics
   */
  async getMetrics(): Promise<ServiceMetrics> {
    const stats = await this.storage.getAssessmentStats();
    const queueLength = await this.queue.getLength();
    const activeClients = await this.clientManager.getActiveClientCount();
    const totalClients = await this.clientManager.getTotalClientCount();

    return {
      totalAssessments: stats.total,
      runningAssessments: stats.running,
      completedAssessments: stats.completed,
      failedAssessments: stats.failed,
      averageDuration: stats.averageDuration,
      totalClients,
      activeClients,
      systemLoad: this.calculateSystemLoad(),
      queueLength
    };
  }

  /**
   * Process the assessment queue
   */
  private async processQueue(): Promise<void> {
    const maxConcurrent = this.config.maxConcurrentAssessments;
    const currentRunning = this.runningAssessments.size;

    if (currentRunning >= maxConcurrent) {
      return; // At capacity
    }

    const availableSlots = maxConcurrent - currentRunning;
    const nextAssessments = await this.queue.dequeue(availableSlots);

    for (const assessmentId of nextAssessments) {
      this.runAssessment(assessmentId).catch(error => {
        console.error(`Error running assessment ${assessmentId}:`, error);
      });
    }
  }

  /**
   * Run a single assessment
   */
  private async runAssessment(assessmentId: string): Promise<void> {
    const assessment = await this.storage.getAssessment(assessmentId);
    if (!assessment) {
      console.error(`Assessment ${assessmentId} not found`);
      return;
    }

    // Mark as running
    const runPromise = this.executeAssessment(assessment);
    this.runningAssessments.set(assessmentId, runPromise);

    try {
      await runPromise;
    } finally {
      this.runningAssessments.delete(assessmentId);
    }
  }

  /**
   * Execute the actual assessment
   */
  private async executeAssessment(assessment: RedTeamAssessment): Promise<void> {
    try {
      // Update status to running
      assessment.status = AssessmentStatus.RUNNING;
      assessment.startedAt = new Date();
      assessment.updatedAt = new Date();
      await this.storage.updateAssessment(assessment);

      // Run the assessment
      const report = await this.engine.runAssessment(assessment.config);

      // Update with results
      assessment.status = AssessmentStatus.COMPLETED;
      assessment.completedAt = new Date();
      assessment.updatedAt = new Date();
      assessment.report = report;
      await this.storage.updateAssessment(assessment);

      // Send notifications
      await this.notifications.sendAssessmentCompleted(assessment);

    } catch (error) {
      // Update status to failed
      assessment.status = AssessmentStatus.FAILED;
      assessment.updatedAt = new Date();
      assessment.error = error.message;
      await this.storage.updateAssessment(assessment);

      // Send failure notification
      await this.notifications.sendAssessmentFailed(assessment, error);
    }
  }

  /**
   * Validate assessment request
   */
  private async validateAssessmentRequest(
    client: RedTeamClient, 
    request: AssessmentRequest
  ): Promise<void> {
    const permissions = client.permissions;

    // Check if client can create assessments
    if (!permissions.canCreateAssessments) {
      throw new Error('Client does not have permission to create assessments');
    }

    // Check target type permissions
    if (!permissions.allowedTargetTypes.includes(request.config.target.type)) {
      throw new Error(`Client not authorized for target type: ${request.config.target.type}`);
    }

    // Check plugin permissions
    for (const plugin of request.config.plugins) {
      const pluginId = typeof plugin === 'string' ? plugin : plugin.id;
      if (!permissions.allowedPlugins.includes(pluginId)) {
        throw new Error(`Client not authorized for plugin: ${pluginId}`);
      }
    }

    // Check quotas
    const usage = await this.clientManager.getCurrentUsage(client.id);
    if (usage.assessmentsThisMonth >= client.quotas.maxAssessmentsPerMonth) {
      throw new Error('Monthly assessment quota exceeded');
    }

    if (request.config.numTests && request.config.numTests > client.quotas.maxTestsPerAssessment) {
      throw new Error('Test count exceeds maximum allowed per assessment');
    }
  }

  /**
   * Generate unique assessment ID
   */
  private generateAssessmentId(): string {
    return `assessment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Estimate assessment duration
   */
  private estimateAssessmentDuration(config: RedTeamConfig): number {
    const numTests = config.numTests || 50;
    const avgTestDuration = 2000; // 2 seconds per test
    const concurrency = config.maxConcurrency || 5;
    
    return Math.ceil((numTests * avgTestDuration) / concurrency);
  }

  /**
   * Calculate system load
   */
  private calculateSystemLoad(): number {
    const runningCount = this.runningAssessments.size;
    const maxConcurrent = this.config.maxConcurrentAssessments;
    return (runningCount / maxConcurrent) * 100;
  }
}
