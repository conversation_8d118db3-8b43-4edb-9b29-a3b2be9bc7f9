import { RedTeamConfig, RedTeamReport } from '../types';

/**
 * Red Teaming as a Service (RTaaS) Types
 */

export interface RedTeamAssessment {
  id: string;
  clientId: string;
  whitelabelId?: string;
  name: string;
  description?: string;
  config: RedTeamConfig;
  status: AssessmentStatus;
  createdAt: Date;
  updatedAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  report?: RedTeamReport;
  error?: string;
  metadata?: Record<string, any>;
}

export enum AssessmentStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface AssessmentSchedule {
  id: string;
  assessmentId: string;
  clientId: string;
  cronExpression: string;
  enabled: boolean;
  nextRun?: Date;
  lastRun?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface RedTeamClient {
  id: string;
  name: string;
  email: string;
  organization?: string;
  apiKey: string;
  permissions: ClientPermissions;
  quotas: ClientQuotas;
  createdAt: Date;
  updatedAt: Date;
  lastActivity?: Date;
  isActive: boolean;
}

export interface ClientPermissions {
  canCreateAssessments: boolean;
  canScheduleAssessments: boolean;
  canViewReports: boolean;
  canExportReports: boolean;
  allowedPlugins: string[];
  allowedTargetTypes: string[];
  maxConcurrentAssessments: number;
}

export interface ClientQuotas {
  maxAssessmentsPerMonth: number;
  maxTestsPerAssessment: number;
  maxConcurrentAssessments: number;
  currentMonthUsage: number;
  resetDate: Date;
}

export interface AssessmentRequest {
  name: string;
  description?: string;
  config: RedTeamConfig;
  schedule?: {
    cronExpression: string;
    enabled: boolean;
  };
  metadata?: Record<string, any>;
}

export interface AssessmentResponse {
  assessment: RedTeamAssessment;
  estimatedDuration?: number;
  queuePosition?: number;
}

export interface AssessmentListResponse {
  assessments: RedTeamAssessment[];
  total: number;
  page: number;
  pageSize: number;
}

export interface ReportExportRequest {
  assessmentId: string;
  format: 'json' | 'yaml' | 'html' | 'markdown' | 'pdf';
  includeDetails?: boolean;
}

export interface ServiceMetrics {
  totalAssessments: number;
  runningAssessments: number;
  completedAssessments: number;
  failedAssessments: number;
  averageDuration: number;
  totalClients: number;
  activeClients: number;
  systemLoad: number;
  queueLength: number;
}

export interface WebhookConfig {
  url: string;
  events: WebhookEvent[];
  secret?: string;
  enabled: boolean;
}

export enum WebhookEvent {
  ASSESSMENT_STARTED = 'assessment.started',
  ASSESSMENT_COMPLETED = 'assessment.completed',
  ASSESSMENT_FAILED = 'assessment.failed',
  VULNERABILITY_FOUND = 'vulnerability.found',
  REPORT_GENERATED = 'report.generated'
}

export interface WebhookPayload {
  event: WebhookEvent;
  timestamp: Date;
  assessmentId: string;
  clientId: string;
  data: Record<string, any>;
}

export interface AssessmentFilter {
  clientId?: string;
  status?: AssessmentStatus;
  startDate?: Date;
  endDate?: Date;
  targetType?: string;
  hasVulnerabilities?: boolean;
}

export interface AssessmentSort {
  field: 'createdAt' | 'updatedAt' | 'name' | 'status';
  direction: 'asc' | 'desc';
}

export interface PaginationOptions {
  page: number;
  pageSize: number;
}

export interface ServiceConfig {
  maxConcurrentAssessments: number;
  defaultAssessmentTimeout: number;
  queueProcessingInterval: number;
  reportRetentionDays: number;
  enableWebhooks: boolean;
  enableScheduling: boolean;
  enableMetrics: boolean;
  rateLimiting: {
    enabled: boolean;
    requestsPerMinute: number;
    requestsPerHour: number;
  };
  storage: {
    type: 'mongodb' | 'postgresql' | 'memory';
    connectionString?: string;
    options?: Record<string, any>;
  };
}

export interface AssessmentQueue {
  id: string;
  assessmentId: string;
  priority: number;
  queuedAt: Date;
  estimatedStartTime?: Date;
  retryCount: number;
  maxRetries: number;
}

export interface AssessmentProgress {
  assessmentId: string;
  totalTests: number;
  completedTests: number;
  currentTest?: string;
  vulnerabilitiesFound: number;
  estimatedTimeRemaining?: number;
  lastUpdate: Date;
}

export interface ClientUsageStats {
  clientId: string;
  period: 'day' | 'week' | 'month' | 'year';
  assessmentsRun: number;
  testsExecuted: number;
  vulnerabilitiesFound: number;
  averageAssessmentDuration: number;
  quotaUsage: number;
  quotaLimit: number;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime: number;
  version: string;
  components: {
    database: 'healthy' | 'unhealthy';
    queue: 'healthy' | 'unhealthy';
    scheduler: 'healthy' | 'unhealthy';
    storage: 'healthy' | 'unhealthy';
  };
  metrics: ServiceMetrics;
  lastCheck: Date;
}

export interface NotificationConfig {
  email?: {
    enabled: boolean;
    recipients: string[];
    events: WebhookEvent[];
  };
  slack?: {
    enabled: boolean;
    webhookUrl: string;
    channel: string;
    events: WebhookEvent[];
  };
  webhook?: WebhookConfig;
}

export interface AssessmentTemplate {
  id: string;
  name: string;
  description: string;
  config: RedTeamConfig;
  category: 'rag' | 'agent' | 'llm' | 'comprehensive';
  isPublic: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  usageCount: number;
}

export interface BulkAssessmentRequest {
  assessments: AssessmentRequest[];
  schedule?: {
    cronExpression: string;
    enabled: boolean;
  };
  parallelExecution?: boolean;
  stopOnFirstFailure?: boolean;
}

export interface BulkAssessmentResponse {
  batchId: string;
  assessments: AssessmentResponse[];
  totalCount: number;
  successCount: number;
  failureCount: number;
}
