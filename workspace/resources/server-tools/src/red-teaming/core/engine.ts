import { 
  RedTeamConfig, 
  RedTeamReport, 
  TestCase, 
  TestResult, 
  RedTeamEvent, 
  RedTeamEventHandler,
  Plugin,
  Strategy,
  Provider
} from '../types';
import { PluginManager } from '../plugins/manager';
import { StrategyManager } from '../strategies/manager';
import { ProviderManager } from '../providers/manager';
import { ReportGenerator } from './reporting';
import { ConfigValidator } from './config';

/**
 * Main Red Team Engine - Orchestrates security testing
 */
export class RedTeamEngine {
  private pluginManager: PluginManager;
  private strategyManager: StrategyManager;
  private providerManager: ProviderManager;
  private reportGenerator: ReportGenerator;
  private configValidator: ConfigValidator;
  private eventHandlers: RedTeamEventHandler[] = [];

  constructor() {
    this.pluginManager = new PluginManager();
    this.strategyManager = new StrategyManager();
    this.providerManager = new ProviderManager();
    this.reportGenerator = new ReportGenerator();
    this.configValidator = new ConfigValidator();
  }

  /**
   * Add event handler for monitoring red team execution
   */
  addEventHandler(handler: RedTeamEventHandler): void {
    this.eventHandlers.push(handler);
  }

  /**
   * Emit event to all registered handlers
   */
  private emitEvent(event: RedTeamEvent): void {
    this.eventHandlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        console.error('Error in event handler:', error);
      }
    });
  }

  /**
   * Run red team assessment
   */
  async runAssessment(config: RedTeamConfig): Promise<RedTeamReport> {
    const startTime = Date.now();
    
    this.emitEvent({
      type: 'test_started',
      timestamp: new Date(),
      data: { config }
    });

    try {
      // Validate configuration
      await this.configValidator.validate(config);

      // Load plugins and strategies
      const plugins = await this.loadPlugins(config.plugins);
      const strategies = await this.loadStrategies(config.strategies || []);
      const providers = await this.loadProviders(config.providers || []);

      // Generate test cases
      const testCases = await this.generateTestCases(plugins, strategies, config);

      // Execute tests
      const testResults = await this.executeTests(testCases, providers, config);

      // Generate report
      const report = await this.reportGenerator.generate({
        testResults,
        config,
        duration: Date.now() - startTime
      });

      this.emitEvent({
        type: 'test_completed',
        timestamp: new Date(),
        data: { report }
      });

      return report;

    } catch (error) {
      this.emitEvent({
        type: 'error',
        timestamp: new Date(),
        data: { error: error.message, stack: error.stack }
      });
      throw error;
    }
  }

  /**
   * Load and initialize plugins
   */
  private async loadPlugins(pluginConfigs: RedTeamConfig['plugins']): Promise<Plugin[]> {
    const plugins: Plugin[] = [];
    
    for (const pluginConfig of pluginConfigs) {
      const pluginId = typeof pluginConfig === 'string' ? pluginConfig : pluginConfig.id;
      const plugin = await this.pluginManager.getPlugin(pluginId);
      plugins.push(plugin);
    }
    
    return plugins;
  }

  /**
   * Load and initialize strategies
   */
  private async loadStrategies(strategyConfigs: RedTeamConfig['strategies']): Promise<Strategy[]> {
    const strategies: Strategy[] = [];
    
    for (const strategyConfig of strategyConfigs) {
      const strategyId = typeof strategyConfig === 'string' ? strategyConfig : strategyConfig.id;
      const strategy = await this.strategyManager.getStrategy(strategyId);
      strategies.push(strategy);
    }
    
    return strategies;
  }

  /**
   * Load and initialize providers
   */
  private async loadProviders(providerConfigs: RedTeamConfig['providers']): Promise<Provider[]> {
    const providers: Provider[] = [];
    
    for (const providerConfig of providerConfigs) {
      const provider = await this.providerManager.getProvider(providerConfig.id);
      providers.push(provider);
    }
    
    return providers;
  }

  /**
   * Generate test cases from plugins and strategies
   */
  private async generateTestCases(
    plugins: Plugin[], 
    strategies: Strategy[], 
    config: RedTeamConfig
  ): Promise<TestCase[]> {
    const testCases: TestCase[] = [];

    // Generate test cases from plugins
    for (const plugin of plugins) {
      const pluginConfig = config.plugins.find(p => 
        (typeof p === 'string' ? p : p.id) === plugin.id
      );
      const pluginTestCases = await plugin.generateTestCases(
        typeof pluginConfig === 'string' ? { id: pluginConfig } : pluginConfig
      );
      testCases.push(...pluginTestCases);
    }

    // Apply strategies to modify/enhance test cases
    for (const strategy of strategies) {
      const strategyConfig = config.strategies?.find(s => 
        (typeof s === 'string' ? s : s.id) === strategy.id
      );
      const enhancedTestCases = await strategy.combineWithPlugins(plugins);
      testCases.push(...enhancedTestCases);
    }

    // Limit number of tests if specified
    if (config.numTests && testCases.length > config.numTests) {
      return testCases.slice(0, config.numTests);
    }

    return testCases;
  }

  /**
   * Execute test cases against target system
   */
  private async executeTests(
    testCases: TestCase[], 
    providers: Provider[], 
    config: RedTeamConfig
  ): Promise<TestResult[]> {
    const results: TestResult[] = [];
    const concurrency = config.maxConcurrency || 5;
    
    // Execute tests in batches to control concurrency
    for (let i = 0; i < testCases.length; i += concurrency) {
      const batch = testCases.slice(i, i + concurrency);
      const batchPromises = batch.map(testCase => this.executeTestCase(testCase, providers, config));
      const batchResults = await Promise.allSettled(batchPromises);
      
      for (const result of batchResults) {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error('Test execution failed:', result.reason);
          // Create a failed test result
          const failedResult: TestResult = {
            testCase: batch[batchResults.indexOf(result)],
            output: '',
            passed: false,
            vulnerabilities: [],
            duration: 0,
            timestamp: new Date(),
            metadata: { error: result.reason.message }
          };
          results.push(failedResult);
        }
      }
    }

    return results;
  }

  /**
   * Execute a single test case
   */
  private async executeTestCase(
    testCase: TestCase, 
    providers: Provider[], 
    config: RedTeamConfig
  ): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      // Select appropriate provider or use default
      const provider = providers.length > 0 ? providers[0] : await this.getDefaultProvider(config);
      
      // Execute test case
      const output = await provider.execute(testCase.input, {
        id: provider.id,
        type: provider.type,
        config: {}
      });

      // Get plugin for evaluation
      const plugin = await this.pluginManager.getPlugin(testCase.plugin);
      
      // Evaluate result
      const result = await plugin.evaluateResult(testCase, output);
      result.duration = Date.now() - startTime;
      result.timestamp = new Date();

      // Emit vulnerability events
      if (result.vulnerabilities.length > 0) {
        this.emitEvent({
          type: 'vulnerability_found',
          timestamp: new Date(),
          data: { 
            testId: testCase.id,
            vulnerabilities: result.vulnerabilities 
          },
          testId: testCase.id,
          pluginId: testCase.plugin
        });
      }

      return result;

    } catch (error) {
      this.emitEvent({
        type: 'error',
        timestamp: new Date(),
        data: { 
          error: error.message, 
          testId: testCase.id 
        },
        testId: testCase.id
      });

      return {
        testCase,
        output: '',
        passed: false,
        vulnerabilities: [],
        duration: Date.now() - startTime,
        timestamp: new Date(),
        metadata: { error: error.message }
      };
    }
  }

  /**
   * Get default provider based on config
   */
  private async getDefaultProvider(config: RedTeamConfig): Promise<Provider> {
    // Implementation would depend on the target system type
    // For now, return a basic HTTP provider
    return await this.providerManager.getProvider('http');
  }
}
