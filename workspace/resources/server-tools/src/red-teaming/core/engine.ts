import {
  RedTeamConfig,
  RedTeamReport,
  TestCase,
  TestResult,
  RedTeamEvent,
  RedTeamEventHandler,
  Plugin,
  Provider,
} from "../types";
import { PluginManager } from "../plugins/manager";
import { ReportGenerator } from "./reporting";
import { ConfigValidator } from "./config";

/**
 * Main Red Team Engine - Orchestrates security testing
 */
export class RedTeamEngine {
  private pluginManager: PluginManager;
  public reportGenerator: ReportGenerator;
  private configValidator: ConfigValidator;
  private eventHandlers: RedTeamEventHandler[] = [];

  constructor() {
    this.pluginManager = new PluginManager();
    this.reportGenerator = new ReportGenerator();
    this.configValidator = new ConfigValidator();
  }

  /**
   * Add event handler for monitoring red team execution
   */
  addEventHandler(handler: RedTeamEventHandler): void {
    this.eventHandlers.push(handler);
  }

  /**
   * Emit event to all registered handlers
   */
  private emitEvent(event: RedTeamEvent): void {
    this.eventHandlers.forEach((handler) => {
      try {
        handler(event);
      } catch (error) {
        console.error("Error in event handler:", error);
      }
    });
  }

  /**
   * Run red team assessment
   */
  async runAssessment(config: RedTeamConfig): Promise<RedTeamReport> {
    const startTime = Date.now();

    this.emitEvent({
      type: "test_started",
      timestamp: new Date(),
      data: { config },
    });

    try {
      // Validate configuration
      await this.configValidator.validate(config);

      // Load plugins
      const plugins = await this.loadPlugins(config.plugins);

      // Generate test cases
      const testCases = await this.generateTestCases(plugins, config);

      // Execute tests
      const testResults = await this.executeTests(testCases, config);

      // Generate report
      const report = await this.reportGenerator.generate({
        testResults,
        config,
        duration: Date.now() - startTime,
      });

      this.emitEvent({
        type: "test_completed",
        timestamp: new Date(),
        data: { report },
      });

      return report;
    } catch (error) {
      this.emitEvent({
        type: "error",
        timestamp: new Date(),
        data: { error: error.message, stack: error.stack },
      });
      throw error;
    }
  }

  /**
   * Load and initialize plugins
   */
  private async loadPlugins(
    pluginConfigs: RedTeamConfig["plugins"]
  ): Promise<Plugin[]> {
    const plugins: Plugin[] = [];

    for (const pluginConfig of pluginConfigs) {
      const pluginId =
        typeof pluginConfig === "string" ? pluginConfig : pluginConfig.id;
      const plugin = await this.pluginManager.getPlugin(pluginId);
      plugins.push(plugin);
    }

    return plugins;
  }

  /**
   * Generate test cases from plugins
   */
  private async generateTestCases(
    plugins: Plugin[],
    config: RedTeamConfig
  ): Promise<TestCase[]> {
    const testCases: TestCase[] = [];

    // Generate test cases from plugins
    for (const plugin of plugins) {
      const pluginConfig = config.plugins.find(
        (p) => (typeof p === "string" ? p : p.id) === plugin.id
      );
      const pluginTestCases = await plugin.generateTestCases(
        typeof pluginConfig === "string" ? { id: pluginConfig } : pluginConfig!
      );
      testCases.push(...pluginTestCases);
    }

    // Limit number of tests if specified
    if (config.numTests && testCases.length > config.numTests) {
      return testCases.slice(0, config.numTests);
    }

    return testCases;
  }

  /**
   * Execute test cases against target system
   */
  private async executeTests(
    testCases: TestCase[],
    config: RedTeamConfig
  ): Promise<TestResult[]> {
    const results: TestResult[] = [];
    const concurrency = config.maxConcurrency || 5;

    // Execute tests in batches to control concurrency
    for (let i = 0; i < testCases.length; i += concurrency) {
      const batch = testCases.slice(i, i + concurrency);
      const batchPromises = batch.map((testCase) =>
        this.executeTestCase(testCase, config)
      );
      const batchResults = await Promise.allSettled(batchPromises);

      for (const result of batchResults) {
        if (result.status === "fulfilled") {
          results.push(result.value);
        } else {
          console.error("Test execution failed:", result.reason);
          // Create a failed test result
          const failedResult: TestResult = {
            testCase: batch[batchResults.indexOf(result)],
            output: "",
            passed: false,
            vulnerabilities: [],
            duration: 0,
            timestamp: new Date(),
            metadata: { error: (result.reason as Error).message },
          };
          results.push(failedResult);
        }
      }
    }

    return results;
  }

  /**
   * Execute a single test case
   */
  private async executeTestCase(
    testCase: TestCase,
    config: RedTeamConfig
  ): Promise<TestResult> {
    const startTime = Date.now();

    try {
      // For now, simulate test execution
      const output = `Simulated response for: ${testCase.input}`;

      // Get plugin for evaluation
      const plugin = await this.pluginManager.getPlugin(testCase.plugin);

      // Evaluate result
      const result = await plugin.evaluateResult(testCase, output);
      result.duration = Date.now() - startTime;
      result.timestamp = new Date();

      // Emit vulnerability events
      if (result.vulnerabilities.length > 0) {
        this.emitEvent({
          type: "vulnerability_found",
          timestamp: new Date(),
          data: {
            testId: testCase.id,
            vulnerabilities: result.vulnerabilities,
          },
          testId: testCase.id,
          pluginId: testCase.plugin,
        });
      }

      return result;
    } catch (error) {
      this.emitEvent({
        type: "error",
        timestamp: new Date(),
        data: {
          error: (error as Error).message,
          testId: testCase.id,
        },
        testId: testCase.id,
      });

      return {
        testCase,
        output: "",
        passed: false,
        vulnerabilities: [],
        duration: Date.now() - startTime,
        timestamp: new Date(),
        metadata: { error: (error as Error).message },
      };
    }
  }
}
