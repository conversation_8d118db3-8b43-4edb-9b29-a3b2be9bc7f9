import { 
  RedTeamReport, 
  TestResult, 
  Vulnerability, 
  VulnerabilityType, 
  RedTeamConfig 
} from '../types';

/**
 * Report generator for red team assessments
 */
export class ReportGenerator {
  
  /**
   * Generate comprehensive red team report
   */
  async generate(data: {
    testResults: TestResult[];
    config: RedTeamConfig;
    duration: number;
  }): Promise<RedTeamReport> {
    const { testResults, config, duration } = data;
    
    const summary = this.generateSummary(testResults);
    const vulnerabilities = this.extractVulnerabilities(testResults);
    const recommendations = this.generateRecommendations(vulnerabilities, config);

    return {
      summary,
      vulnerabilities,
      testResults,
      recommendations,
      metadata: {
        timestamp: new Date(),
        duration,
        config,
        version: '1.0.0'
      }
    };
  }

  /**
   * Generate summary statistics
   */
  private generateSummary(testResults: TestResult[]): RedTeamReport['summary'] {
    const totalTests = testResults.length;
    const passedTests = testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const vulnerabilitiesFound = testResults.reduce((sum, r) => sum + r.vulnerabilities.length, 0);
    
    // Calculate overall score (0-100, higher is better)
    const overallScore = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
    
    // Determine risk level based on vulnerabilities
    const riskLevel = this.calculateRiskLevel(testResults);

    return {
      totalTests,
      passedTests,
      failedTests,
      vulnerabilitiesFound,
      overallScore,
      riskLevel
    };
  }

  /**
   * Extract all vulnerabilities from test results
   */
  private extractVulnerabilities(testResults: TestResult[]): Vulnerability[] {
    const vulnerabilities: Vulnerability[] = [];
    
    for (const result of testResults) {
      vulnerabilities.push(...result.vulnerabilities);
    }
    
    // Sort by severity (critical first)
    const severityOrder = { 'critical': 0, 'high': 1, 'medium': 2, 'low': 3 };
    vulnerabilities.sort((a, b) => severityOrder[a.severity] - severityOrder[b.severity]);
    
    return vulnerabilities;
  }

  /**
   * Calculate overall risk level
   */
  private calculateRiskLevel(testResults: TestResult[]): 'low' | 'medium' | 'high' | 'critical' {
    const vulnerabilities = this.extractVulnerabilities(testResults);
    
    const criticalCount = vulnerabilities.filter(v => v.severity === 'critical').length;
    const highCount = vulnerabilities.filter(v => v.severity === 'high').length;
    const mediumCount = vulnerabilities.filter(v => v.severity === 'medium').length;
    
    if (criticalCount > 0) return 'critical';
    if (highCount > 2) return 'critical';
    if (highCount > 0 || mediumCount > 5) return 'high';
    if (mediumCount > 0) return 'medium';
    
    return 'low';
  }

  /**
   * Generate security recommendations
   */
  private generateRecommendations(vulnerabilities: Vulnerability[], config: RedTeamConfig): string[] {
    const recommendations: string[] = [];
    const vulnerabilityTypes = new Set(vulnerabilities.map(v => v.type));

    // General recommendations based on vulnerability types found
    if (vulnerabilityTypes.has(VulnerabilityType.PROMPT_INJECTION)) {
      recommendations.push(
        'Implement robust input sanitization and validation to prevent prompt injection attacks',
        'Use system instructions vs. user instructions separation',
        'Consider implementing a prompt firewall or content filter'
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.DATA_EXFILTRATION)) {
      recommendations.push(
        'Implement strict data access controls and output filtering',
        'Use differential privacy techniques for sensitive data',
        'Add data loss prevention (DLP) mechanisms'
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.PRIVILEGE_ESCALATION)) {
      recommendations.push(
        'Implement deterministic, non-LLM-based permission systems',
        'Apply principle of least privilege for all operations',
        'Use granular role-based access control (RBAC)'
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.MEMORY_POISONING)) {
      recommendations.push(
        'Implement temporal memory structures with limited persistence',
        'Segregate system instructions from user input memory',
        'Add memory attribution tracking'
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.PII_LEAK)) {
      recommendations.push(
        'Implement PII detection and redaction mechanisms',
        'Use anonymization techniques for sensitive data',
        'Add output scanning for personally identifiable information'
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.CONTEXT_INJECTION)) {
      recommendations.push(
        'Validate and sanitize all context sources',
        'Separate retrieved documents from system messages',
        'Implement content validation for knowledge base updates'
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.TOOL_MANIPULATION)) {
      recommendations.push(
        'Implement strict input validation for all tool APIs',
        'Use API request signing and verification',
        'Treat all LLM tool APIs as public interfaces'
      );
    }

    // Target-specific recommendations
    if (config.target.type === 'rag') {
      recommendations.push(
        'Regularly audit and validate RAG knowledge base content',
        'Implement retrieval result filtering and validation',
        'Use multiple retrieval strategies to reduce manipulation risk'
      );
    }

    if (config.target.type === 'agent') {
      recommendations.push(
        'Limit agent action scope and implement action approval workflows',
        'Monitor agent behavior against established safety criteria',
        'Implement session limits for sensitive operations'
      );
    }

    // Add severity-based recommendations
    const criticalVulns = vulnerabilities.filter(v => v.severity === 'critical');
    if (criticalVulns.length > 0) {
      recommendations.unshift(
        '🚨 CRITICAL: Immediate action required - system should not be deployed to production',
        'Conduct thorough security review before any deployment'
      );
    }

    const highVulns = vulnerabilities.filter(v => v.severity === 'high');
    if (highVulns.length > 0) {
      recommendations.unshift(
        '⚠️ HIGH PRIORITY: Address high-severity vulnerabilities before production deployment'
      );
    }

    // Remove duplicates and return
    return [...new Set(recommendations)];
  }

  /**
   * Export report to different formats
   */
  async exportReport(report: RedTeamReport, format: 'json' | 'yaml' | 'html' | 'markdown'): Promise<string> {
    switch (format) {
      case 'json':
        return JSON.stringify(report, null, 2);
      
      case 'yaml':
        return this.toYaml(report);
      
      case 'html':
        return this.toHtml(report);
      
      case 'markdown':
        return this.toMarkdown(report);
      
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Convert report to YAML format
   */
  private toYaml(report: RedTeamReport): string {
    // Simple YAML conversion - in production, use a proper YAML library
    const yaml = `
# Red Team Security Assessment Report

## Summary
total_tests: ${report.summary.totalTests}
passed_tests: ${report.summary.passedTests}
failed_tests: ${report.summary.failedTests}
vulnerabilities_found: ${report.summary.vulnerabilitiesFound}
overall_score: ${report.summary.overallScore}
risk_level: ${report.summary.riskLevel}

## Vulnerabilities
${report.vulnerabilities.map(v => `
- type: ${v.type}
  severity: ${v.severity}
  description: "${v.description}"
  evidence: "${v.evidence}"
  ${v.recommendation ? `recommendation: "${v.recommendation}"` : ''}
  ${v.cwe ? `cwe: "${v.cwe}"` : ''}
  ${v.owasp ? `owasp: "${v.owasp}"` : ''}
`).join('')}

## Recommendations
${report.recommendations.map(r => `- ${r}`).join('\n')}

## Metadata
timestamp: ${report.metadata.timestamp.toISOString()}
duration: ${report.metadata.duration}ms
version: ${report.metadata.version}
`.trim();

    return yaml;
  }

  /**
   * Convert report to HTML format
   */
  private toHtml(report: RedTeamReport): string {
    const riskColor = {
      'low': '#28a745',
      'medium': '#ffc107', 
      'high': '#fd7e14',
      'critical': '#dc3545'
    };

    return `
<!DOCTYPE html>
<html>
<head>
    <title>Red Team Security Assessment Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: white; padding: 15px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .risk-level { color: ${riskColor[report.summary.riskLevel]}; font-weight: bold; }
        .vulnerability { margin: 10px 0; padding: 15px; border-left: 4px solid #ddd; }
        .critical { border-left-color: #dc3545; }
        .high { border-left-color: #fd7e14; }
        .medium { border-left-color: #ffc107; }
        .low { border-left-color: #28a745; }
        .recommendations { background: #e7f3ff; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ Red Team Security Assessment Report</h1>
        <p>Generated on ${report.metadata.timestamp.toLocaleString()}</p>
        <p>Assessment Duration: ${Math.round(report.metadata.duration / 1000)}s</p>
    </div>

    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <p style="font-size: 24px; margin: 0;">${report.summary.totalTests}</p>
        </div>
        <div class="metric">
            <h3>Vulnerabilities Found</h3>
            <p style="font-size: 24px; margin: 0;">${report.summary.vulnerabilitiesFound}</p>
        </div>
        <div class="metric">
            <h3>Overall Score</h3>
            <p style="font-size: 24px; margin: 0;">${report.summary.overallScore}/100</p>
        </div>
        <div class="metric">
            <h3>Risk Level</h3>
            <p style="font-size: 24px; margin: 0;" class="risk-level">${report.summary.riskLevel.toUpperCase()}</p>
        </div>
    </div>

    <h2>🚨 Vulnerabilities</h2>
    ${report.vulnerabilities.map(v => `
        <div class="vulnerability ${v.severity}">
            <h4>${v.type.replace(/_/g, ' ').toUpperCase()} - ${v.severity.toUpperCase()}</h4>
            <p><strong>Description:</strong> ${v.description}</p>
            <p><strong>Evidence:</strong> ${v.evidence}</p>
            ${v.recommendation ? `<p><strong>Recommendation:</strong> ${v.recommendation}</p>` : ''}
        </div>
    `).join('')}

    <div class="recommendations">
        <h2>💡 Recommendations</h2>
        <ul>
            ${report.recommendations.map(r => `<li>${r}</li>`).join('')}
        </ul>
    </div>
</body>
</html>
    `.trim();
  }

  /**
   * Convert report to Markdown format
   */
  private toMarkdown(report: RedTeamReport): string {
    const riskEmoji = {
      'low': '🟢',
      'medium': '🟡', 
      'high': '🟠',
      'critical': '🔴'
    };

    return `
# 🛡️ Red Team Security Assessment Report

**Generated:** ${report.metadata.timestamp.toLocaleString()}  
**Duration:** ${Math.round(report.metadata.duration / 1000)}s  
**Version:** ${report.metadata.version}

## 📊 Summary

| Metric | Value |
|--------|-------|
| Total Tests | ${report.summary.totalTests} |
| Passed Tests | ${report.summary.passedTests} |
| Failed Tests | ${report.summary.failedTests} |
| Vulnerabilities Found | ${report.summary.vulnerabilitiesFound} |
| Overall Score | ${report.summary.overallScore}/100 |
| Risk Level | ${riskEmoji[report.summary.riskLevel]} ${report.summary.riskLevel.toUpperCase()} |

## 🚨 Vulnerabilities

${report.vulnerabilities.map(v => `
### ${v.type.replace(/_/g, ' ').toUpperCase()} - ${v.severity.toUpperCase()}

**Description:** ${v.description}

**Evidence:** ${v.evidence}

${v.recommendation ? `**Recommendation:** ${v.recommendation}` : ''}

${v.cwe ? `**CWE:** ${v.cwe}` : ''}

${v.owasp ? `**OWASP:** ${v.owasp}` : ''}

---
`).join('')}

## 💡 Recommendations

${report.recommendations.map(r => `- ${r}`).join('\n')}

## 🔧 Configuration

**Target Type:** ${report.metadata.config.target.type}  
**Plugins Used:** ${report.metadata.config.plugins.map(p => typeof p === 'string' ? p : p.id).join(', ')}  
${report.metadata.config.strategies ? `**Strategies Used:** ${report.metadata.config.strategies.map(s => typeof s === 'string' ? s : s.id).join(', ')}` : ''}
    `.trim();
  }
}
