/**
 * Basic Red Teaming Usage Examples
 */

import { 
  RedTeamEngine, 
  ConfigTemplates, 
  RedTeamConfigBuilder,
  RedTeamConfig 
} from '../index';

/**
 * Example 1: Basic RAG Security Assessment
 */
export async function basicRAGAssessment() {
  console.log('🔍 Running Basic RAG Security Assessment...');
  
  const engine = new RedTeamEngine();
  
  // Use predefined template
  const config = ConfigTemplates.ragSecurity({
    endpoint: 'https://api.example.com/rag',
    purpose: 'Basic RAG security assessment',
    numTests: 25
  });
  
  try {
    const report = await engine.runAssessment(config);
    
    console.log('📊 Assessment Results:');
    console.log(`- Total Tests: ${report.summary.totalTests}`);
    console.log(`- Vulnerabilities Found: ${report.summary.vulnerabilitiesFound}`);
    console.log(`- Risk Level: ${report.summary.riskLevel.toUpperCase()}`);
    console.log(`- Overall Score: ${report.summary.overallScore}/100`);
    
    // Export report
    const htmlReport = await engine.reportGenerator.exportReport(report, 'html');
    console.log('📄 HTML report generated');
    
    return report;
  } catch (error) {
    console.error('❌ Assessment failed:', error.message);
    throw error;
  }
}

/**
 * Example 2: Agent Security Assessment
 */
export async function basicAgentAssessment() {
  console.log('🤖 Running Agent Security Assessment...');
  
  const engine = new RedTeamEngine();
  
  const config = ConfigTemplates.agentSecurity({
    endpoint: 'https://api.example.com/agent',
    purpose: 'AI agent security assessment',
    numTests: 30
  });
  
  const report = await engine.runAssessment(config);
  
  console.log('🛡️ Agent Security Results:');
  console.log(`- RBAC Violations: ${report.vulnerabilities.filter(v => v.type === 'rbac_violation').length}`);
  console.log(`- Privilege Escalations: ${report.vulnerabilities.filter(v => v.type === 'privilege_escalation').length}`);
  console.log(`- Memory Poisoning: ${report.vulnerabilities.filter(v => v.type === 'memory_poisoning').length}`);
  
  return report;
}

/**
 * Example 3: Custom Configuration with Builder
 */
export async function customConfigAssessment() {
  console.log('⚙️ Running Custom Configuration Assessment...');
  
  const engine = new RedTeamEngine();
  
  // Build custom configuration
  const config = new RedTeamConfigBuilder()
    .target('hybrid', {
      endpoint: 'https://api.example.com/ai',
      provider: 'openai',
      model: 'gpt-4'
    })
    .purpose('Custom security assessment with specific policies')
    .numTests(40)
    .maxConcurrency(3)
    .plugin('prompt-injection')
    .plugin('data-exfiltration')
    .plugin('pii-leak')
    .plugin('policy', {
      policy: 'The system must never disclose customer personal information or financial data'
    })
    .strategy('jailbreak')
    .strategy('prompt-injection')
    .output({
      format: 'markdown',
      includeDetails: true
    })
    .build();
  
  const report = await engine.runAssessment(config);
  
  console.log('📋 Custom Assessment Results:');
  report.vulnerabilities.forEach((vuln, index) => {
    console.log(`${index + 1}. ${vuln.type.toUpperCase()} - ${vuln.severity.toUpperCase()}`);
    console.log(`   ${vuln.description}`);
  });
  
  return report;
}

/**
 * Example 4: Policy-Focused Assessment
 */
export async function policyFocusedAssessment() {
  console.log('📜 Running Policy-Focused Assessment...');
  
  const engine = new RedTeamEngine();
  
  const config: RedTeamConfig = {
    target: {
      type: 'llm',
      endpoint: 'https://api.example.com/llm'
    },
    purpose: 'Policy compliance assessment',
    numTests: 20,
    plugins: [
      {
        id: 'policy',
        config: {
          policy: 'The AI assistant must not provide medical advice or diagnoses'
        }
      },
      {
        id: 'policy',
        config: {
          policy: 'The system must not generate content that could be used for illegal activities'
        }
      },
      {
        id: 'policy',
        config: {
          policy: 'All responses must be factual and not contain fabricated information'
        }
      },
      'harmful-content',
      'pii-leak'
    ]
  };
  
  const report = await engine.runAssessment(config);
  
  console.log('📊 Policy Compliance Results:');
  console.log(`- Policy Violations: ${report.vulnerabilities.filter(v => v.type === 'prompt_injection').length}`);
  console.log(`- Harmful Content: ${report.vulnerabilities.filter(v => v.type === 'harmful_content').length}`);
  
  return report;
}

/**
 * Example 5: Comprehensive Assessment with Event Monitoring
 */
export async function comprehensiveAssessmentWithMonitoring() {
  console.log('🔍 Running Comprehensive Assessment with Monitoring...');
  
  const engine = new RedTeamEngine();
  
  // Add event handler for monitoring
  engine.addEventHandler((event) => {
    switch (event.type) {
      case 'test_started':
        console.log('🚀 Assessment started');
        break;
      case 'vulnerability_found':
        console.log(`⚠️ Vulnerability found: ${event.data.vulnerabilities[0]?.type}`);
        break;
      case 'test_completed':
        console.log('✅ Assessment completed');
        break;
      case 'error':
        console.log(`❌ Error: ${event.data.error}`);
        break;
    }
  });
  
  const config = ConfigTemplates.comprehensive({
    endpoint: 'https://api.example.com/comprehensive',
    purpose: 'Full security assessment with monitoring',
    numTests: 75
  });
  
  const report = await engine.runAssessment(config);
  
  console.log('📈 Comprehensive Results:');
  console.log(`- Duration: ${Math.round(report.metadata.duration / 1000)}s`);
  console.log(`- Tests per second: ${(report.summary.totalTests / (report.metadata.duration / 1000)).toFixed(2)}`);
  
  // Generate multiple report formats
  const jsonReport = await engine.reportGenerator.exportReport(report, 'json');
  const markdownReport = await engine.reportGenerator.exportReport(report, 'markdown');
  const htmlReport = await engine.reportGenerator.exportReport(report, 'html');
  
  console.log('📄 Generated reports in JSON, Markdown, and HTML formats');
  
  return report;
}

/**
 * Example 6: Batch Assessment
 */
export async function batchAssessment() {
  console.log('📦 Running Batch Assessment...');
  
  const engine = new RedTeamEngine();
  
  const endpoints = [
    'https://api.example.com/rag-v1',
    'https://api.example.com/rag-v2',
    'https://api.example.com/agent-v1'
  ];
  
  const results = [];
  
  for (const [index, endpoint] of endpoints.entries()) {
    console.log(`🔍 Assessing endpoint ${index + 1}/${endpoints.length}: ${endpoint}`);
    
    const config = ConfigTemplates.ragSecurity({
      endpoint,
      purpose: `Batch assessment of ${endpoint}`,
      numTests: 15
    });
    
    try {
      const report = await engine.runAssessment(config);
      results.push({
        endpoint,
        success: true,
        vulnerabilities: report.summary.vulnerabilitiesFound,
        riskLevel: report.summary.riskLevel,
        score: report.summary.overallScore
      });
    } catch (error) {
      results.push({
        endpoint,
        success: false,
        error: error.message
      });
    }
  }
  
  console.log('📊 Batch Assessment Summary:');
  results.forEach((result, index) => {
    if (result.success) {
      console.log(`${index + 1}. ${result.endpoint}: ${result.vulnerabilities} vulnerabilities, Risk: ${result.riskLevel}, Score: ${result.score}/100`);
    } else {
      console.log(`${index + 1}. ${result.endpoint}: FAILED - ${result.error}`);
    }
  });
  
  return results;
}

/**
 * Example 7: Targeted Plugin Assessment
 */
export async function targetedPluginAssessment() {
  console.log('🎯 Running Targeted Plugin Assessment...');
  
  const engine = new RedTeamEngine();
  
  // Focus on specific vulnerability types
  const config: RedTeamConfig = {
    target: {
      type: 'rag',
      endpoint: 'https://api.example.com/rag'
    },
    purpose: 'Targeted assessment focusing on data exfiltration and PII leaks',
    numTests: 35,
    maxConcurrency: 2,
    plugins: [
      'data-exfiltration',
      'pii-leak',
      'context-injection',
      {
        id: 'policy',
        config: {
          policy: 'The system must never expose user personal information, including names, emails, phone numbers, or addresses'
        }
      }
    ],
    strategies: ['prompt-injection'],
    output: {
      format: 'json',
      includeDetails: true
    }
  };
  
  const report = await engine.runAssessment(config);
  
  console.log('🎯 Targeted Assessment Results:');
  const dataExfiltrationVulns = report.vulnerabilities.filter(v => v.type === 'data_exfiltration');
  const piiLeakVulns = report.vulnerabilities.filter(v => v.type === 'pii_leak');
  
  console.log(`- Data Exfiltration Vulnerabilities: ${dataExfiltrationVulns.length}`);
  console.log(`- PII Leak Vulnerabilities: ${piiLeakVulns.length}`);
  
  if (dataExfiltrationVulns.length > 0) {
    console.log('⚠️ Critical: Data exfiltration vulnerabilities found!');
    dataExfiltrationVulns.forEach(vuln => {
      console.log(`  - ${vuln.description}`);
    });
  }
  
  return report;
}

/**
 * Run all examples
 */
export async function runAllExamples() {
  console.log('🚀 Running All Red Teaming Examples...\n');
  
  try {
    await basicRAGAssessment();
    console.log('\n' + '='.repeat(50) + '\n');
    
    await basicAgentAssessment();
    console.log('\n' + '='.repeat(50) + '\n');
    
    await customConfigAssessment();
    console.log('\n' + '='.repeat(50) + '\n');
    
    await policyFocusedAssessment();
    console.log('\n' + '='.repeat(50) + '\n');
    
    await comprehensiveAssessmentWithMonitoring();
    console.log('\n' + '='.repeat(50) + '\n');
    
    await batchAssessment();
    console.log('\n' + '='.repeat(50) + '\n');
    
    await targetedPluginAssessment();
    
    console.log('\n✅ All examples completed successfully!');
  } catch (error) {
    console.error('\n❌ Example execution failed:', error.message);
    throw error;
  }
}

// Export for use in other files
export {
  basicRAGAssessment,
  basicAgentAssessment,
  customConfigAssessment,
  policyFocusedAssessment,
  comprehensiveAssessmentWithMonitoring,
  batchAssessment,
  targetedPluginAssessment,
  runAllExamples
};
