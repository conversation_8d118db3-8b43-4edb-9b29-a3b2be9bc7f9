
# Embed Consumer

```html
<script src="cdn.divinci.app/embed.js" data-release="0123456789" ></script>
<iframe src="embed.divinci.app?release=0123456789" ></iframe>
```

## Things to Track
- The release that the user will be using
- The user themself
  - We may want to force login
- Whether the frame is fullscreen or will be fixed on the bottom right
  - Styling is clientside


## IFrame Flow
- Display Login Button if user is not logged in
- Make the user login with auth0
- Return back to the Iframe
  - Start new chat
  - Load Chats

