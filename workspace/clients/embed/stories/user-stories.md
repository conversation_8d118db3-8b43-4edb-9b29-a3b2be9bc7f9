
## Hand Up!

## Emojis



## Anonymous => Logged In
- The user interacts with the chat anonymously
- The chat hits message limit
  - We prompt the message to login in order to continue
- The User Logs in
  - We allow them to continue the chat they were doing anonymously

## Logged In => Old Messages
- User Wants old Messages
  - They Login
- User Chooses which chat to continue
- Chat loads and they can continue

## No Anonymous Allowed
- Release doesn't allow anonymous interaction
  - Prompts them to Login on the first page
- They login
  - If have chats, Redirect to chat list
  - Else Are Redirected to new chat

## More Info Release
- They want to find out more information about the release

## Move to Divinci.app for better Experience
- They are enjoying the chat but want to a better chat experience
  - They click "use divinci.app" and it takes them to the chat on divinci.app

## User Isn't getting the responses it Likes
- User is using the chat normally
  - They arent recieving responses they like
- User "Raises Hand"
- Release Staff sees "Raised Hand"
  - Release Staff Talks to User Directly
