
# Login Flow
- go to LOGIN page
- click "Login"
- opens popup out page
  - Our Page redirects to auth0 - auth0client.loginWithRedirect()
  - auth0 flow completes
  - auth0 redirects back to our page - auth0client.handleRedirectCallback();
  - extract the necesary information from the auth0client
  - run parent.postMessage(JSON.stringify(NECESSARY_INFO), "*")
- recieve message from popup
  - close popup
- extract NECESSARY_INFO
- make local auth0client use NECESSARY_INFO
- further fetch requests to our api can use token from auth0.getTokenSilently()
- Show the users information on LOGIN page

# What We can do
- https://www.npmjs.com/package/@auth0/auth0-spa-js
- Setup the client
- Open up popup
- Setup Client there
- run loginWithRedirect()
- allow auth0 flow
- handle handleRedirectCallback()
- message the parent with necessary information
- hack in the user using information