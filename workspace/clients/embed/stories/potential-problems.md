
# Potential Problems

## Anonymous

### Release allows Anonymous Interaction
- Maximum Threads per IP Address?
- Maximum Interactions per thread

### Prevent Too many Fresh Anonymous Threads
- If the release's owner makes the call on their behalf, the owner can identify each user
- Soft Fix: we save the number of threads in local storage
- Track IP Addresses (This would be bad for multiple people using the same router)

# Done

## Anonymous
- toggle if Anonymous Interaction is allowed or not


## Limiting Anonymous Message Length
- They send us a chat completion with
  - Previous Prompt/Response thread
  - Signiture using the Prompt/Response Thread
  - New Prompt
- We check if that signiture exists with that release
  - No - throw
- We make sure the prompt/response length is the same stored signitures
  - No - throw
- We make sure that the prompt/response length is less than the release's maximum
  - No - throw
- We delete the old signiture
- We make a request to the releases model using the new Prompt
- we sign the new Prompt/Response thread
- we save the new signiture along with the length
- we send back
  - Updated Thread (with new prompt and response)
  - new Signature

## Embed

### Chats with multiple users
- filter chats that aren't the user?

### Chats with multiple branches
- Use only the latest branch?
- Allow the user to choose a branch to continue?



