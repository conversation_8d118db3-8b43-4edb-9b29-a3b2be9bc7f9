import { describe, it, expect, vi, beforeEach } from "vitest";
import { RecoveringWebsocket } from "../../src/util/websocket/recovering-websocket";

// Mock the WebSocket class
class MockWebSocket {
  constructor(url, protocols) {
    this.url = url;
    this.protocols = protocols;
    this.readyState = MockWebSocket.CONNECTING;
    this.onopen = null;
    this.onclose = null;
    this.onerror = null;
    this.onmessage = null;
    this.addEventListener = vi.fn();
    this.removeEventListener = vi.fn();
    this.send = vi.fn();
    this.close = vi.fn(() => {
      this.readyState = MockWebSocket.CLOSED;
    });
  }
  
  // Static properties
  static get CONNECTING() { return 0; }
  static get OPEN() { return 1; }
  static get CLOSING() { return 2; }
  static get CLOSED() { return 3; }
  
  // Helper methods for testing
  simulateOpen() {
    this.readyState = MockWebSocket.OPEN;
    const event = new Event("open");
    this.dispatchEvent(event);
  }
  
  simulateClose(wasClean = true, code = 1000, reason = "") {
    this.readyState = MockWebSocket.CLOSED;
    // Create a custom close event
    const event = new Event("close");
    event.wasClean = wasClean;
    event.code = code;
    event.reason = reason;
    this.dispatchEvent(event);
  }
  
  simulateError() {
    const event = new Event("error");
    this.dispatchEvent(event);
  }
  
  simulateMessage(data) {
    const event = new MessageEvent("message", { data });
    this.dispatchEvent(event);
  }
  
  dispatchEvent(event) {
    if (event.type === "open" && this.onopen) this.onopen.call(this, event);
    if (event.type === "close" && this.onclose) this.onclose.call(this, event);
    if (event.type === "error" && this.onerror) this.onerror.call(this, event);
    if (event.type === "message" && this.onmessage) this.onmessage.call(this, event);
    
    this.addEventListener.mock.calls.forEach(([type, listener]) => {
      if (type === event.type) {
        listener.call(this, event);
      }
    });
    
    return true;
  }
}

// Replace the global WebSocket with our mock
global.WebSocket = MockWebSocket;

// Mock the delay function
vi.mock("@divinci-ai/utils", async () => {
  const actual = await vi.importActual("@divinci-ai/utils");
  return {
    ...actual,
    delay: vi.fn().mockResolvedValue(undefined)
  };
});

describe("RecoveringWebsocket Performance", () => {
  let ws;
  let mockWs;
  
  beforeEach(() => {
    vi.clearAllMocks();
    ws = new RecoveringWebsocket(1000, "wss://example.com");
    mockWs = ws.connection;
  });
  
  it("should efficiently handle multiple messages", () => {
    // Simulate WebSocket open
    mockWs.simulateOpen();
    
    // Prepare a large number of messages
    const numMessages = 1000;
    const startTime = performance.now();
    
    // Send messages
    for (let i = 0; i < numMessages; i++) {
      mockWs.simulateMessage(JSON.stringify({ id: i, data: `Message ${i}` }));
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Check that processing time is reasonable (less than 100ms for 1000 messages)
    expect(duration).toBeLessThan(100);
  });
  
  it("should efficiently queue messages when connection is not open", () => {
    // Keep WebSocket in connecting state
    mockWs.readyState = MockWebSocket.CONNECTING;
    
    // Prepare a large number of messages to queue
    const numMessages = 1000;
    const startTime = performance.now();
    
    // Queue messages
    for (let i = 0; i < numMessages; i++) {
      ws.send(JSON.stringify({ id: i, data: `Message ${i}` }));
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Check that queueing time is reasonable (less than 50ms for 1000 messages)
    expect(duration).toBeLessThan(50);
    
    // Verify that messages were queued
    expect(ws.queue.length).toBe(numMessages);
    expect(mockWs.send).not.toHaveBeenCalled();
    
    // Now simulate WebSocket open and measure time to send all queued messages
    const sendStartTime = performance.now();
    mockWs.simulateOpen();
    const sendEndTime = performance.now();
    const sendDuration = sendEndTime - sendStartTime;
    
    // Check that sending all queued messages is efficient
    expect(sendDuration).toBeLessThan(100);
    
    // Verify that all messages were sent
    expect(mockWs.send).toHaveBeenCalledTimes(numMessages);
    expect(ws.queue.length).toBe(0);
  });
  
  it("should efficiently handle reconnection", async () => {
    // Simulate WebSocket open then close
    mockWs.simulateOpen();
    
    // Measure reconnection time
    const startTime = performance.now();
    mockWs.simulateClose(false); // Unclean close to trigger reconnect
    
    // Wait for reconnect delay
    await vi.importMock("@divinci-ai/utils").delay;
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Check that reconnection logic is efficient
    expect(duration).toBeLessThan(50);
  });
});
