# Embed Client Tests

This directory contains unit tests for the Embed Client using Vitest.

## Running Tests

To run all tests:

```bash
cd /path/to/server
npx vitest run --dir workspace/clients/embed/tests
```

To run tests in watch mode (tests will automatically re-run when files change):

```bash
cd /path/to/server
npx vitest --dir workspace/clients/embed/tests
```

To run tests with coverage:

```bash
cd /path/to/server
npx vitest run --coverage --dir workspace/clients/embed/tests
```

## Test Structure

The tests are organized into several categories:

### Source Code Structure

- `components/`: Tests for React components
- `globals/`: Tests for global utilities and configurations
- `util/`: Tests for utility functions

### Test Categories

- `edge-cases/`: Tests that verify behavior under unusual or boundary conditions
- `performance/`: Tests that ensure code performs efficiently under load
- `security/`: Tests that verify security measures and prevent vulnerabilities
- `snapshots/`: Tests that capture expected output to detect unintended changes
- `compatibility/`: Tests that ensure code works with different environments and versions

## Module Resolution

The tests use Vitest for testing and support path aliases for easier imports:

- `@/*`: Resolves to `src/*` (e.g., `@/components/Button` resolves to `src/components/Button`)
- `~/*`: Resolves to `tests/*` (e.g., `~/helpers` resolves to `tests/helpers`)

### Example

```typescript
// Import using path alias
import { Button } from '@/components/Button';

// Import from tests directory
import { renderWithTheme } from '~/helpers/render';
```

## Writing New Tests

When writing new tests:

1. Create a test file with the same name as the file you're testing, but with a `.test.ts` or `.test.tsx` extension
2. Place the test file in the corresponding directory structure under `tests/`
3. Import the functions or components you want to test
4. Use Vitest's `describe`, `it`, and `expect` functions to write your tests

Example:

```typescript
import { describe, it, expect } from 'vitest';
import { myFunction } from '@/path/to/file';

describe('myFunction', () => {
  it('should return the expected result', () => {
    const result = myFunction(input);
    expect(result).toBe(expectedOutput);
  });
});
```

## Mocking

For tests that require mocking:

- Use Vitest's `vi.mock()` to mock modules
- Use `vi.fn()` to create mock functions
- Use `vi.spyOn()` to spy on existing functions

Example:

```typescript
import { describe, it, expect, vi } from 'vitest';
import { functionThatCallsAPI } from '@/path/to/file';

// Mock the API module
vi.mock('@/api', () => ({
  fetchData: vi.fn().mockResolvedValue({ data: 'mocked data' })
}));

describe('functionThatCallsAPI', () => {
  it('should use the API and return processed data', async () => {
    const result = await functionThatCallsAPI();
    expect(result).toBe('processed mocked data');
  });
});
```

## Testing React Components

For React components, use `@testing-library/react` to render and interact with components:

```typescript
import { describe, it, expect } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { MyComponent } from '@/components/MyComponent';

describe('MyComponent', () => {
  it('should render correctly', () => {
    render(<MyComponent />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });

  it('should respond to user interaction', () => {
    render(<MyComponent />);
    fireEvent.click(screen.getByRole('button'));
    expect(screen.getByText('Changed Text')).toBeInTheDocument();
  });
});
```
