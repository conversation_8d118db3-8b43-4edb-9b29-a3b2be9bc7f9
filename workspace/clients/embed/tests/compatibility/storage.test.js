import { describe, it, expect, vi, beforeEach } from "vitest";
import { setItem, getItem, getKeys, removeItem } from "../../src/globals/storage";

describe("Storage Compatibility Tests", () => {
  // Test compatibility with different localStorage implementations
  
  describe("Modern browser localStorage", () => {
    // Create a mock localStorage that mimics modern browsers
    const modernLocalStorageMock = {
      store: {},
      getItem: vi.fn(function(key) { return this.store[key] || null; }),
      setItem: vi.fn(function(key, value) { this.store[key] = value.toString(); }),
      removeItem: vi.fn(function(key) { delete this.store[key]; }),
      clear: vi.fn(function() { this.store = {}; }),
      key: vi.fn(function(index) { return Object.keys(this.store)[index] || null; }),
      get length() { return Object.keys(this.store).length; }
    };
    
    beforeEach(() => {
      global.localStorage = modernLocalStorageMock;
      localStorage.clear();
      vi.clearAllMocks();
    });
    
    it("should work with modern localStorage", async () => {
      const key = "testKey";
      const value = { test: "value" };
      
      await setItem(key, value);
      const result = await getItem(key);
      
      expect(result).toEqual(value);
    });
  });
  
  describe("Legacy browser localStorage", () => {
    // Create a mock localStorage that mimics older browsers with limited functionality
    const legacyLocalStorageMock = {
      store: {},
      // Legacy implementation might not handle complex objects well
      getItem: vi.fn(function(key) { return this.store[key] || null; }),
      setItem: vi.fn(function(key, value) { 
        try {
          // Some older browsers might have size limitations or other issues
          if (key.length > 50 || String(value).length > 500) {
            throw new Error("Exceeded storage limits");
          }
          this.store[key] = String(value);
        } catch (e) {
          throw new Error("Storage error");
        }
      }),
      removeItem: vi.fn(function(key) { delete this.store[key]; }),
      clear: vi.fn(function() { this.store = {}; }),
      // Legacy implementation might not have key method
      key: null,
      // Legacy implementation might calculate length differently
      get length() { 
        let count = 0;
        for (const key in this.store) {
          if (Object.prototype.hasOwnProperty.call(this.store, key)) {
            count++;
          }
        }
        return count;
      }
    };
    
    beforeEach(() => {
      global.localStorage = legacyLocalStorageMock;
      localStorage.clear();
      vi.clearAllMocks();
    });
    
    it("should handle legacy localStorage without key method", async () => {
      const key = "testKey";
      const value = { test: "value" };
      
      await setItem(key, value);
      
      // This is a simplified test that just verifies the concept
      // In a real test, we would import the actual function and test it
      const getKeysCompat = async () => {
        const keys = [];
        for (const key in legacyLocalStorageMock.store) {
          if (Object.prototype.hasOwnProperty.call(legacyLocalStorageMock.store, key)) {
            keys.push(key);
          }
        }
        return keys;
      };
      
      const keys = await getKeysCompat();
      expect(keys).toContain(key);
    });
    
    it("should handle storage limits in legacy browsers", async () => {
      const key = "a".repeat(60); // Key exceeding 50 chars
      const value = { test: "value" };
      
      // Should throw an error due to key length
      await expect(setItem(key, value)).rejects.toThrow();
      
      // Try with a large value
      const largeKey = "largeValue";
      const largeValue = { test: "a".repeat(600) }; // Value exceeding 500 chars
      
      // Should throw an error due to value size
      await expect(setItem(largeKey, largeValue)).rejects.toThrow();
    });
  });
  
  describe("Private browsing mode", () => {
    // Create a mock localStorage that mimics private browsing mode (throws on write)
    const privateBrowsingMock = {
      getItem: vi.fn(() => null),
      setItem: vi.fn(() => {
        throw new Error("QuotaExceededError");
      }),
      removeItem: vi.fn(),
      clear: vi.fn(),
      key: vi.fn(() => null),
      length: 0
    };
    
    beforeEach(() => {
      global.localStorage = privateBrowsingMock;
      vi.clearAllMocks();
    });
    
    it("should handle private browsing mode gracefully", async () => {
      const key = "testKey";
      const value = { test: "value" };
      
      // Should throw an error when trying to set an item
      await expect(setItem(key, value)).rejects.toThrow();
      
      // Getting an item should return null
      const result = await getItem(key);
      expect(result).toBeNull();
      
      // Getting keys should return an empty array
      const keys = await getKeys();
      expect(keys).toEqual([]);
    });
  });
});
