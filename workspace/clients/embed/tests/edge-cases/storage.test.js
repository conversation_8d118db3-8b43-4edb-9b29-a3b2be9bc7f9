import { describe, it, expect, vi, beforeEach } from "vitest";
import { setItem, getItem, getKeys, removeItem } from "../../src/globals/storage";

// Create a mock localStorage with edge case handling
const localStorageMock = {
  store: {},
  getItem: vi.fn(function(key) { return this.store[key] || null; }),
  setItem: vi.fn(function(key, value) { this.store[key] = value.toString(); }),
  removeItem: vi.fn(function(key) { delete this.store[key]; }),
  clear: vi.fn(function() { this.store = {}; }),
  key: vi.fn(function(index) { return Object.keys(this.store)[index] || null; }),
  get length() { return Object.keys(this.store).length; }
};

// Define localStorage globally for this test file
global.localStorage = localStorageMock;

describe("Storage Utilities Edge Cases", () => {
  beforeEach(() => {
    localStorage.clear();
    vi.clearAllMocks();
  });

  describe("setItem edge cases", () => {
    it("should handle null values", async () => {
      const key = "nullKey";
      const value = null;

      const result = await setItem(key, value);

      expect(localStorage.setItem).toHaveBeenCalledWith(key, "null");
      expect(result).toBeNull();
    });

    it("should handle undefined values", async () => {
      const key = "undefinedKey";
      const value = undefined;

      // Update the mock to handle undefined values
      vi.mocked(localStorage.setItem).mockImplementationOnce((key, value) => {
        localStorageMock.store[key] = String(value);
      });

      const result = await setItem(key, value);

      // Check that localStorage.setItem was called with the key
      expect(localStorage.setItem).toHaveBeenCalled();
      expect(result).toBeUndefined();
    });

    it("should handle circular references", async () => {
      const key = "circularKey";
      const value = { name: "circular" };
      // Create circular reference
      value.self = value;

      // This should throw an error due to circular reference
      await expect(setItem(key, value)).rejects.toThrow();
    });
  });

  describe("getItem edge cases", () => {
    it("should handle malformed JSON", async () => {
      const key = "malformedKey";
      const malformedJSON = "{not valid json";

      // Directly set malformed JSON in localStorage
      vi.spyOn(localStorage, "getItem").mockReturnValueOnce(malformedJSON);

      // Should throw an error
      await expect(getItem(key)).rejects.toThrow();
    });

    it("should handle empty string keys", async () => {
      const key = "";
      const value = { test: "empty key" };

      await setItem(key, value);
      const result = await getItem(key);

      expect(result).toEqual(value);
    });
  });

  describe("getKeys edge cases", () => {
    it("should handle localStorage with non-enumerable properties", async () => {
      // Setup localStorage with multiple values
      const keys = ["key1", "key2", "key3"];
      keys.forEach(key => localStorage.setItem(key, JSON.stringify({ test: key })));

      // Add a non-enumerable property
      Object.defineProperty(localStorageMock.store, "nonEnumerable", {
        value: "hidden",
        enumerable: false
      });

      const result = await getKeys();

      // Should only return enumerable keys
      expect(result).toEqual(keys);
      expect(result).not.toContain("nonEnumerable");
    });
  });

  describe("removeItem edge cases", () => {
    it("should handle removing non-existent keys", async () => {
      const key = "nonExistentKey";

      const result = await removeItem(key);

      expect(localStorage.removeItem).toHaveBeenCalledWith(key);
      expect(result).toBeNull();
    });

    it("should handle removing keys with special characters", async () => {
      const key = "key!@#$%^&*()";
      const value = { special: "characters" };

      await setItem(key, value);
      const result = await removeItem(key);

      expect(localStorage.removeItem).toHaveBeenCalledWith(key);
      expect(result).toEqual(value);
    });
  });
});
