import { describe, it, expect } from 'vitest';

describe('DateDisplay Component', () => {
  // Helper function to simulate the DateDisplay component's behavior
  function formatDate(timestamp) {
    const date = new Date(timestamp);
    return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()} ${date.getHours()}:${date.getMinutes()}`;
  }
  
  it('should format date correctly', () => {
    // Create a fixed date for testing (January 15, 2023, 10:30 AM)
    const timestamp = new Date(2023, 0, 15, 10, 30).getTime();
    
    // Format should be MM/DD/YYYY HH:MM
    const expectedText = '1/15/2023 10:30';
    expect(formatDate(timestamp)).toBe(expectedText);
  });

  it('should handle different dates correctly', () => {
    // Test with a different date (December 31, 2022, 23:59)
    const timestamp = new Date(2022, 11, 31, 23, 59).getTime();
    
    const expectedText = '12/31/2022 23:59';
    expect(formatDate(timestamp)).toBe(expectedText);
  });

  it('should handle single-digit months, days, hours, and minutes with no leading zeros', () => {
    // Test with single-digit values (February 5, 2023, 9:5)
    const timestamp = new Date(2023, 1, 5, 9, 5).getTime();
    
    const expectedText = '2/5/2023 9:5';
    expect(formatDate(timestamp)).toBe(expectedText);
  });
});
