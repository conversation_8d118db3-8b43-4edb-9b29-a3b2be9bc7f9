import { describe, it, expect, vi, beforeEach } from 'vitest';
import { waitForEventTarget, eventTargetToPromise } from '../../../src/util/events/event-target';

describe('Event Target Utilities', () => {
  let eventTarget: EventTarget;
  
  beforeEach(() => {
    eventTarget = new EventTarget();
  });
  
  describe('waitForEventTarget', () => {
    it('should resolve when the specified event is fired', async () => {
      const eventName = 'test-event';
      const eventData = { detail: 'test-data' };
      
      // Start waiting for the event
      const eventPromise = waitForEventTarget(eventTarget, eventName);
      
      // Dispatch the event
      eventTarget.dispatchEvent(new CustomEvent(eventName, { detail: eventData.detail }));
      
      // Wait for the promise to resolve
      const result = await eventPromise;
      
      expect(result).toBeInstanceOf(CustomEvent);
      expect((result as CustomEvent).detail).toBe(eventData.detail);
    });
    
    it('should use the filter function to only resolve for matching events', async () => {
      const eventName = 'filtered-event';
      
      // Start waiting for an event that passes the filter
      const eventPromise = waitForEventTarget(eventTarget, eventName, 
        (e: CustomEvent) => e.detail === 'match');
      
      // Dispatch an event that doesn't match the filter
      eventTarget.dispatchEvent(new CustomEvent(eventName, { detail: 'no-match' }));
      
      // Create a timeout promise to test that the event promise doesn't resolve
      const timeoutPromise = new Promise(resolve => setTimeout(() => resolve('timeout'), 100));
      
      // Race the event promise against the timeout
      const raceResult = await Promise.race([eventPromise.then(() => 'event'), timeoutPromise]);
      
      // The timeout should win because the event didn't match the filter
      expect(raceResult).toBe('timeout');
      
      // Now dispatch an event that matches the filter
      eventTarget.dispatchEvent(new CustomEvent(eventName, { detail: 'match' }));
      
      // The event promise should now resolve
      const result = await eventPromise;
      expect((result as CustomEvent).detail).toBe('match');
    });
  });
  
  describe('eventTargetToPromise', () => {
    it('should resolve when the success event is fired', async () => {
      const successEvent = 'success';
      const errorEvent = 'error';
      const eventData = { detail: 'success-data' };
      
      // Create a promise that resolves on success and rejects on error
      const promise = eventTargetToPromise(eventTarget, [successEvent, errorEvent]);
      
      // Dispatch the success event
      eventTarget.dispatchEvent(new CustomEvent(successEvent, { detail: eventData.detail }));
      
      // Wait for the promise to resolve
      const result = await promise;
      
      expect(result).toBeInstanceOf(CustomEvent);
      expect((result as CustomEvent).detail).toBe(eventData.detail);
    });
    
    it('should reject when the error event is fired', async () => {
      const successEvent = 'success';
      const errorEvent = 'error';
      const errorData = { detail: 'error-data' };
      
      // Create a promise that resolves on success and rejects on error
      const promise = eventTargetToPromise(eventTarget, [successEvent, errorEvent]);
      
      // Dispatch the error event
      eventTarget.dispatchEvent(new CustomEvent(errorEvent, { detail: errorData.detail }));
      
      // The promise should reject
      await expect(promise).rejects.toBeInstanceOf(CustomEvent);
      await expect(promise).rejects.toMatchObject({
        detail: errorData.detail
      });
    });
    
    it('should clean up event listeners after resolving or rejecting', async () => {
      const successEvent = 'success';
      const errorEvent = 'error';
      
      // Mock addEventListener and removeEventListener
      const addSpy = vi.spyOn(eventTarget, 'addEventListener');
      const removeSpy = vi.spyOn(eventTarget, 'removeEventListener');
      
      // Create a promise and immediately resolve it
      const promise = eventTargetToPromise(eventTarget, [successEvent, errorEvent]);
      eventTarget.dispatchEvent(new CustomEvent(successEvent));
      
      await promise;
      
      // Should have added 2 event listeners (success and error)
      expect(addSpy).toHaveBeenCalledTimes(2);
      
      // Should have removed both event listeners
      expect(removeSpy).toHaveBeenCalledTimes(2);
    });
  });
});
