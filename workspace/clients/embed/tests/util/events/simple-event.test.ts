import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createSimpleEmitter } from '../../../src/util/events/simple-event';

describe('Simple Event Emitter', () => {
  let emitter: ReturnType<typeof createSimpleEmitter<[string, number]>>;
  
  beforeEach(() => {
    emitter = createSimpleEmitter<[string, number]>();
  });
  
  describe('on/emit', () => {
    it('should register a listener and emit events to it', () => {
      const listener = vi.fn();
      
      emitter.on(listener);
      emitter.emit('test', 123);
      
      expect(listener).toHaveBeenCalledTimes(1);
      expect(listener).toHaveBeenCalledWith('test', 123);
    });
    
    it('should emit to multiple listeners', () => {
      const listener1 = vi.fn();
      const listener2 = vi.fn();
      
      emitter.on(listener1);
      emitter.on(listener2);
      emitter.emit('test', 456);
      
      expect(listener1).toHaveBeenCalledWith('test', 456);
      expect(listener2).toHaveBeenCalledWith('test', 456);
    });
    
    it('should continue emitting to other listeners if one throws an error', () => {
      const errorListener = vi.fn().mockImplementation(() => {
        throw new Error('Test error');
      });
      const normalListener = vi.fn();
      
      emitter.on(errorListener);
      emitter.on(normalListener);
      
      // This should not throw
      emitter.emit('test', 789);
      
      expect(errorListener).toHaveBeenCalled();
      expect(normalListener).toHaveBeenCalled();
    });
  });
  
  describe('off', () => {
    it('should remove a listener', () => {
      const listener = vi.fn();
      
      emitter.on(listener);
      emitter.emit('test', 123);
      expect(listener).toHaveBeenCalledTimes(1);
      
      const result = emitter.off(listener);
      emitter.emit('test', 456);
      
      expect(result).toBe(true);
      expect(listener).toHaveBeenCalledTimes(1); // Still only called once
    });
    
    it('should return false when trying to remove a non-existent listener', () => {
      const listener = vi.fn();
      
      const result = emitter.off(listener);
      
      expect(result).toBe(false);
    });
  });
  
  describe('once', () => {
    it('should call the listener only once', () => {
      const listener = vi.fn();
      
      emitter.once(listener);
      
      emitter.emit('first', 1);
      emitter.emit('second', 2);
      
      expect(listener).toHaveBeenCalledTimes(1);
      expect(listener).toHaveBeenCalledWith('first', 1);
    });
  });
  
  describe('function call syntax', () => {
    it('should register a listener and return an unsubscribe function', () => {
      const listener = vi.fn();
      
      const unsubscribe = emitter(listener);
      
      emitter.emit('test', 123);
      expect(listener).toHaveBeenCalledTimes(1);
      
      unsubscribe();
      emitter.emit('test', 456);
      expect(listener).toHaveBeenCalledTimes(1); // Still only called once
    });
  });
});
