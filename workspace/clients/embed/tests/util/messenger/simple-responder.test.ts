import { describe, it, expect, vi, beforeEach } from 'vitest';
import { simpleRequester } from '../../../src/util/messenger/simple-responder';
import { JSON_Object } from '@divinci-ai/utils';

describe('Simple Requester', () => {
  let listen: ReturnType<typeof vi.fn>;
  let send: ReturnType<typeof vi.fn>;
  let requester: ReturnType<typeof simpleRequester>;
  let capturedListener: ((data: JSON_Object) => boolean) | null = null;
  
  beforeEach(() => {
    // Mock the listen function to capture the listener
    listen = vi.fn((listener) => {
      capturedListener = listener;
      return () => {}; // Return a cleanup function
    });
    
    send = vi.fn();
    requester = simpleRequester(listen, send);
  });
  
  it('should send a request with the correct format', () => {
    const path = '/test/path';
    const body = { key: 'value' };
    
    requester(path, body);
    
    expect(send).toHaveBeenCalledWith(expect.objectContaining({
      path,
      messageType: 'request',
      value: body
    }));
    
    // Should also have an id
    const sentMessage = send.mock.calls[0][0];
    expect(sentMessage).toHaveProperty('id');
    expect(typeof sentMessage.id).toBe('string');
  });
  
  it('should register a listener that filters messages by id and type', () => {
    const path = '/test/path';
    const body = { key: 'value' };
    
    requester(path, body);
    
    expect(listen).toHaveBeenCalled();
    expect(capturedListener).not.toBeNull();
    
    if (!capturedListener) return; // TypeScript guard
    
    // Get the id from the sent message
    const sentMessage = send.mock.calls[0][0];
    const { id } = sentMessage;
    
    // Test with a message that has a different id
    expect(capturedListener({
      id: 'different-id',
      messageType: 'response',
      valueType: 'success',
      value: { result: 'ok' }
    })).toBe(false);
    
    // Test with a message that has a different messageType
    expect(capturedListener({
      id,
      messageType: 'request', // Not 'response'
      valueType: 'success',
      value: { result: 'ok' }
    })).toBe(false);
    
    // Test with a matching message
    expect(capturedListener({
      id,
      messageType: 'response',
      valueType: 'success',
      value: { result: 'ok' }
    })).toBe(true);
  });
  
  // Note: Testing the Promise resolution/rejection is challenging in this case
  // because the Promise is created inside the function and not returned.
  // In a real application, you would want to modify the function to return the Promise.
});
