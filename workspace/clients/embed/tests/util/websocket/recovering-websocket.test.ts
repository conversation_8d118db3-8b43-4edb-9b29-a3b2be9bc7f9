import { describe, it, expect, vi, beforeEach } from 'vitest';
import { RecoveringWebsocket } from '../../../src/util/websocket/recovering-websocket';

// Mock the WebSocket class
class MockWebSocket {
  static CONNECTING = 0;
  static OPEN = 1;
  static CLOSING = 2;
  static CLOSED = 3;
  
  readyState = MockWebSocket.CONNECTING;
  url: string;
  protocols?: string | string[];
  
  onopen: ((this: WebSocket, ev: Event) => any) | null = null;
  onclose: ((this: WebSocket, ev: CloseEvent) => any) | null = null;
  onerror: ((this: WebSocket, ev: Event) => any) | null = null;
  onmessage: ((this: WebSocket, ev: MessageEvent) => any) | null = null;
  
  addEventListener = vi.fn();
  removeEventListener = vi.fn();
  send = vi.fn();
  close = vi.fn(() => {
    this.readyState = MockWebSocket.CLOSED;
  });
  
  constructor(url: string, protocols?: string | string[]) {
    this.url = url;
    this.protocols = protocols;
  }
  
  // Helper methods for testing
  simulateOpen() {
    this.readyState = MockWebSocket.OPEN;
    const event = new Event('open');
    this.dispatchEvent(event);
  }
  
  simulateClose(wasClean = true, code = 1000, reason = '') {
    this.readyState = MockWebSocket.CLOSED;
    const event = new CloseEvent('close', { 
      wasClean, 
      code, 
      reason 
    });
    this.dispatchEvent(event);
  }
  
  simulateError() {
    const event = new Event('error');
    this.dispatchEvent(event);
  }
  
  simulateMessage(data: any) {
    const event = new MessageEvent('message', { data });
    this.dispatchEvent(event);
  }
  
  dispatchEvent(event: Event) {
    if (event.type === 'open' && this.onopen) this.onopen.call(this as any, event);
    if (event.type === 'close' && this.onclose) this.onclose.call(this as any, event as CloseEvent);
    if (event.type === 'error' && this.onerror) this.onerror.call(this as any, event);
    if (event.type === 'message' && this.onmessage) this.onmessage.call(this as any, event as MessageEvent);
    
    this.addEventListener.mock.calls.forEach(([type, listener]) => {
      if (type === event.type) {
        listener.call(this, event);
      }
    });
    
    return true;
  }
}

// Replace the global WebSocket with our mock
vi.stubGlobal('WebSocket', MockWebSocket);

// Mock the delay function
vi.mock('@divinci-ai/utils', async () => {
  const actual = await vi.importActual('@divinci-ai/utils');
  return {
    ...actual as any,
    delay: vi.fn().mockResolvedValue(undefined)
  };
});

describe('RecoveringWebsocket', () => {
  let ws: RecoveringWebsocket;
  let mockWs: MockWebSocket;
  
  beforeEach(() => {
    vi.clearAllMocks();
    ws = new RecoveringWebsocket(1000, 'wss://example.com');
    mockWs = ws.connection as unknown as MockWebSocket;
  });
  
  describe('constructor', () => {
    it('should create a new WebSocket with the provided parameters', () => {
      expect(mockWs.url).toBe('wss://example.com');
      expect(mockWs.addEventListener).toHaveBeenCalledWith('message', expect.any(Function));
      expect(mockWs.addEventListener).toHaveBeenCalledWith('open', expect.any(Function));
      expect(mockWs.addEventListener).toHaveBeenCalledWith('close', expect.any(Function));
      expect(mockWs.addEventListener).toHaveBeenCalledWith('error', expect.any(Function));
    });
  });
  
  describe('readyState', () => {
    it('should return "attempt" when the WebSocket is connecting', () => {
      mockWs.readyState = MockWebSocket.CONNECTING;
      expect(ws.readyState).toBe('attempt');
    });
    
    it('should return "open" when the WebSocket is open', () => {
      mockWs.readyState = MockWebSocket.OPEN;
      expect(ws.readyState).toBe('open');
    });
    
    it('should return "delay" when the WebSocket is closed (and not permanently closed)', () => {
      mockWs.readyState = MockWebSocket.CLOSED;
      expect(ws.readyState).toBe('delay');
    });
    
    it('should return "closing" when the WebSocket is closing and permanently closed', () => {
      ws.permenantlyClose = true;
      mockWs.readyState = MockWebSocket.CLOSING;
      expect(ws.readyState).toBe('closing');
    });
    
    it('should return "closed" when the WebSocket is closed and permanently closed', () => {
      ws.permenantlyClose = true;
      mockWs.readyState = MockWebSocket.CLOSED;
      expect(ws.readyState).toBe('closed');
    });
  });
  
  describe('send', () => {
    it('should send data directly when the WebSocket is open', () => {
      mockWs.readyState = MockWebSocket.OPEN;
      ws.send('test data');
      
      expect(mockWs.send).toHaveBeenCalledWith('test data');
    });
    
    it('should queue data when the WebSocket is not open', () => {
      mockWs.readyState = MockWebSocket.CONNECTING;
      ws.send('test data');
      
      expect(mockWs.send).not.toHaveBeenCalled();
      expect(ws.queue.length).toBe(1);
      expect(ws.queue[0][0]).toBe('test data');
    });
    
    it('should throw an error when the WebSocket is permanently closed', () => {
      ws.permenantlyClose = true;
      
      expect(() => ws.send('test data')).toThrow('WebSocket is Closed');
    });
    
    it('should send queued messages when the WebSocket opens', () => {
      // Queue some messages
      mockWs.readyState = MockWebSocket.CONNECTING;
      ws.send('message 1');
      ws.send('message 2');
      
      expect(ws.queue.length).toBe(2);
      
      // Simulate the WebSocket opening
      mockWs.simulateOpen();
      
      // The queued messages should have been sent
      expect(mockWs.send).toHaveBeenCalledWith('message 1');
      expect(mockWs.send).toHaveBeenCalledWith('message 2');
      expect(ws.queue.length).toBe(0);
    });
  });
  
  describe('close', () => {
    it('should close the WebSocket and set permanently closed to true', () => {
      ws.close();
      
      expect(ws.permenantlyClose).toBe(true);
      expect(mockWs.close).toHaveBeenCalled();
    });
    
    it('should emit "closed" when the WebSocket is already closed', () => {
      mockWs.readyState = MockWebSocket.CLOSED;
      const emitSpy = vi.spyOn(ws.onReadyStateChange, 'emit');
      
      ws.close();
      
      expect(ws.permenantlyClose).toBe(true);
      expect(emitSpy).toHaveBeenCalledWith('closed');
      expect(mockWs.close).not.toHaveBeenCalled();
    });
  });
  
  describe('event handling', () => {
    it('should emit "open" when the WebSocket opens', () => {
      const emitSpy = vi.spyOn(ws.onReadyStateChange, 'emit');
      
      mockWs.simulateOpen();
      
      expect(emitSpy).toHaveBeenCalledWith('open');
    });
    
    it('should emit "message" when a message is received', () => {
      const emitSpy = vi.spyOn(ws.onMessage, 'emit');
      const messageData = { test: 'data' };
      
      mockWs.simulateMessage(JSON.stringify(messageData));
      
      expect(emitSpy).toHaveBeenCalled();
      expect(emitSpy.mock.calls[0][0].data).toBe(JSON.stringify(messageData));
    });
    
    it('should emit "closed" and set failReason when an error occurs', () => {
      const emitReadyStateSpy = vi.spyOn(ws.onReadyStateChange, 'emit');
      const emitFailSpy = vi.spyOn(ws.onFail, 'emit');
      
      mockWs.simulateError();
      
      expect(ws.permenantlyClose).toBe(true);
      expect(emitReadyStateSpy).toHaveBeenCalledWith('closed');
      expect(emitFailSpy).toHaveBeenCalledWith({ status: 500, reason: 'error trying to connect' });
      expect(ws.failReason).toEqual({ status: 500, reason: 'error trying to connect' });
    });
  });
});
