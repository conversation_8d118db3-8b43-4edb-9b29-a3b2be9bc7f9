import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, fireEvent } from '@testing-library/react';
import { useOutsideAlerter } from '../../../src/util/react/clicked-outside';
import React, { useRef } from 'react';

// Create a test component that uses the hook
const TestComponent = ({ onOutsideClick }: { onOutsideClick: () => void }) => {
  const ref = useRef<HTMLDivElement>(null);
  useOutsideAlerter(ref.current, onOutsideClick);

  return (
    <div data-testid="container">
      <div data-testid="inside-element" ref={ref}>
        Inside Element
      </div>
      <div data-testid="outside-element">
        Outside Element
      </div>
    </div>
  );
};

describe('useOutsideAlerter Hook', () => {
  let onOutsideClick: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    onOutsideClick = vi.fn();
    // Reset the DOM
    document.body.innerHTML = '';
  });

  // This test is skipped because JSDOM doesn't fully simulate browser behavior
  it.skip('should call the listener when clicking outside the referenced element', () => {
    const { getByTestId } = render(<TestComponent onOutsideClick={onOutsideClick} />);

    // Click outside the referenced element
    fireEvent.mouseDown(getByTestId('outside-element'));

    expect(onOutsideClick).toHaveBeenCalledTimes(1);
  });

  it('should not call the listener when clicking inside the referenced element', () => {
    const { getByTestId } = render(<TestComponent onOutsideClick={onOutsideClick} />);

    // Click inside the referenced element
    fireEvent.mouseDown(getByTestId('inside-element'));

    expect(onOutsideClick).not.toHaveBeenCalled();
  });

  it('should add and remove event listeners correctly', () => {
    const addEventListenerSpy = vi.spyOn(document, 'addEventListener');
    const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener');

    const { unmount } = render(<TestComponent onOutsideClick={onOutsideClick} />);

    // Check that the event listener was added
    expect(addEventListenerSpy).toHaveBeenCalledWith('mousedown', expect.any(Function));

    // Unmount the component
    unmount();

    // Check that the event listener was removed
    expect(removeEventListenerSpy).toHaveBeenCalledWith('mousedown', expect.any(Function));
  });

  it('should handle null ref gracefully', () => {
    // Create a component with a null ref
    const NullRefComponent = () => {
      useOutsideAlerter(null, onOutsideClick);
      return <div>Test</div>;
    };

    // This should not throw an error
    expect(() => render(<NullRefComponent />)).not.toThrow();

    // Click anywhere in the document
    fireEvent.mouseDown(document.body);

    // The callback should not be called because the ref is null
    expect(onOutsideClick).not.toHaveBeenCalled();
  });
});
