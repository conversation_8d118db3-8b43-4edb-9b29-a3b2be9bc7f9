import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setItem, getItem, getKeys, removeItem } from '../../src/globals/storage';

// Create a mock localStorage
const localStorageMock = {
  store: {},
  getItem: vi.fn(function(key) { return this.store[key] || null; }),
  setItem: vi.fn(function(key, value) { this.store[key] = value.toString(); }),
  removeItem: vi.fn(function(key) { delete this.store[key]; }),
  clear: vi.fn(function() { this.store = {}; }),
  key: vi.fn(function(index) { return Object.keys(this.store)[index] || null; }),
  get length() { return Object.keys(this.store).length; }
};

// Define localStorage globally for this test file
global.localStorage = localStorageMock;

describe('Storage Utilities', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
    vi.clearAllMocks();
  });

  describe('setItem', () => {
    it('should store a value in localStorage', async () => {
      const key = 'testKey';
      const value = { test: 'value' };

      const result = await setItem(key, value);

      expect(localStorage.setItem).toHaveBeenCalledWith(key, JSON.stringify(value));
      expect(result).toEqual(value);
    });
  });

  describe('getItem', () => {
    it('should retrieve a value from localStorage', async () => {
      const key = 'testKey';
      const value = { test: 'value' };

      // Setup localStorage with a value
      localStorage.setItem(key, JSON.stringify(value));
      vi.mocked(localStorage.getItem).mockReturnValueOnce(JSON.stringify(value));

      const result = await getItem(key);

      expect(localStorage.getItem).toHaveBeenCalledWith(key);
      expect(result).toEqual(value);
    });

    it('should return null for non-existent keys', async () => {
      const key = 'nonExistentKey';

      vi.mocked(localStorage.getItem).mockReturnValueOnce(null);

      const result = await getItem(key);

      expect(localStorage.getItem).toHaveBeenCalledWith(key);
      expect(result).toBeNull();
    });
  });

  describe('getKeys', () => {
    it('should return all keys in localStorage', async () => {
      // Setup localStorage with multiple values
      const keys = ['key1', 'key2', 'key3'];
      keys.forEach(key => localStorage.setItem(key, JSON.stringify({ test: key })));

      // Mock localStorage.length and localStorage.key
      Object.defineProperty(localStorage, 'length', { value: keys.length });
      keys.forEach((key, index) => {
        vi.mocked(localStorage.key).mockReturnValueOnce(key);
      });

      const result = await getKeys();

      expect(result).toEqual(keys);
      expect(localStorage.key).toHaveBeenCalledTimes(keys.length);
    });

    it('should handle empty localStorage', async () => {
      Object.defineProperty(localStorage, 'length', { value: 0 });

      const result = await getKeys();

      expect(result).toEqual([]);
      expect(localStorage.key).not.toHaveBeenCalled();
    });
  });

  describe('removeItem', () => {
    it('should remove an item from localStorage and return its value', async () => {
      const key = 'testKey';
      const value = { test: 'value' };

      // Setup localStorage with a value
      localStorage.setItem(key, JSON.stringify(value));
      vi.mocked(localStorage.getItem).mockReturnValueOnce(JSON.stringify(value));

      const result = await removeItem(key);

      expect(localStorage.getItem).toHaveBeenCalledWith(key);
      expect(localStorage.removeItem).toHaveBeenCalledWith(key);
      expect(result).toEqual(value);
    });

    it('should return null for non-existent keys', async () => {
      const key = 'nonExistentKey';

      vi.mocked(localStorage.getItem).mockReturnValueOnce(null);

      const result = await removeItem(key);

      expect(localStorage.getItem).toHaveBeenCalledWith(key);
      expect(localStorage.removeItem).toHaveBeenCalledWith(key);
      expect(result).toBeNull();
    });
  });
});
