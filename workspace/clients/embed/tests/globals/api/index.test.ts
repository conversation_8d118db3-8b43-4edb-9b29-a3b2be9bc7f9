import { describe, it, expect } from 'vitest';

describe('URL Utilities', () => {
  it('should create valid URLs', () => {
    // Test that we can create a URL with a path
    const url = new URL('/test/path', 'https://example.com');
    expect(url.toString()).toBe('https://example.com/test/path');

    // Test that we can create a URL without a leading slash
    const url2 = new URL('no-leading-slash', 'https://example.com');
    expect(url2.toString()).toBe('https://example.com/no-leading-slash');

    // Test that we can create a URL with a different protocol
    const url3 = new URL('/test/path', 'http://example.com');
    expect(url3.toString()).toBe('http://example.com/test/path');
  });
});
