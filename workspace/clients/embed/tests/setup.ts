// Test setup file for embed client tests
import { vi } from 'vitest';

// Define global atob and btoa functions if not available in the test environment
global.atob = global.atob || ((str: string) => Buffer.from(str, 'base64').toString('binary'));
global.btoa = global.btoa || ((str: string) => Buffer.from(str, 'binary').toString('base64'));

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value.toString();
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      store = {};
    }),
    key: vi.fn((index: number) => Object.keys(store)[index] || null),
    length: vi.fn(() => Object.keys(store).length),
  };
})();

// Define localStorage globally
global.localStorage = localStorageMock;

// Mock environment variables
vi.mock('../src/globals/api/constants', () => ({
  WEB_CLIENT_IS_SECURE: true,
  WEB_CLIENT_HOST: 'test.example.com',
  EMBED_CLIENT_IS_SECURE: true,
  EMBED_CLIENT_HOST: 'embed.test.example.com',
  API_IS_SECURE: true,
  API_HOST: 'api.test.example.com',
}));

// Global beforeEach
beforeEach(() => {
  vi.clearAllMocks();
  localStorageMock.clear();
});
