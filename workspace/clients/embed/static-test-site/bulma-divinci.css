@import "https://cdn.jsdelivr.net/npm/bulma@1.0.2/css/bulma.min.css";
@import "divinci.css";

:root {
  --divi-dark-purp: #1c134d;
}

/* Container for the overall div */
.embed-header {
  background-color: var(--main-header-gradient-end);
  padding: 10px;
}

/* Flexbox container for child elements */
.flex-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.embed-chat-controls {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 1px;
  border-color: dodgerblue;
  margin: 7px;

  .TextareaWrapper {
    flex-grow: 1;
    display: flex;
    width: 100%;
    position: relative;
  }

  .textarea.embed-chat-textarea {
    max-width: inherit;
    max-height: inherit;
    min-width: inherit;
    top: 12px;
    margin: 5px;
    flex-grow: 1;
    overflow-y: hidden;
    height: auto;
    min-height: 40px;
    box-sizing: border-box;

    &:focus {
      background: white;
    }

    &:not(:focus) {
      border-color: dodgerblue;
    }
  }

  .send-chat-button {
    font-weight: bold;
    background-color: var(--divi-dark-purp);
    color: white;
    top: 6px;
    margin: 5px;
    border-radius: 7px;

    :disabled {
      /* Example styling for disabled state */
      background-color: #ccc;
      color: #999;
    }
  }
}

.embed-menu-items {
  width: fit-content;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding: 7px;

  .button {
    margin: 0 10px;
  }

  .login-button {
    font-weight: bold;
    background-color: var(--divi-dark-purp);
    color: white;
    display: block;
    margin-bottom: 10px;
    top: 0;

    :disabled {
      /* Example styling for disabled state */
      background-color: #ccc;
      color: #999;
    }
  }
}

.logout-button {
  top: 0;

  :disabled {
    /* Example styling for disabled state */
    background-color: #ccc;
    color: #999;
  }
}

/* .ai-info {
  font-weight: bold;
    background-color: var(--divi-dark-purp);
    color: white;
} */

.mobile-menu-button {
  width: var(--mobile-menu-width, 40px);
  height: 40px; /* Set a fixed height */
  flex-shrink: 0; /* Prevent shrinking */
  cursor: pointer;
  fill: var(--button-text-color);
  border: 2px solid grey;
  padding: 4px;
  border-radius: 4px;
  background-color: var(--button-bg-color);
  color: var(--button-text-color);
  box-shadow: 1px 1px #0e14547a;
  top: 0px;
}
