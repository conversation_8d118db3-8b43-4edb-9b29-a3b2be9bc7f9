
export function resolveUrl(before: string, after: string){
  const bUrl = new URL(before, "http://localhost.dev");
  const aUrl = new URL(after, bUrl.href);

  return aUrl.pathname + aUrl.search + aUrl.hash;
}

export function relative(owner: string, subpath: string){
  if(owner.slice(-1) !== "/") owner = owner + "/";
  const index = subpath.indexOf(owner);
  if(index > 0) throw new Error(`subpath ${subpath} is not owned by ${owner}`);
  if(index === -1) throw new Error(`subpath ${subpath} is not owned by ${owner}`);
  return subpath.slice(owner.length);
}

export { replaceParams } from "@divinci-ai/utils";

export function matchesPath(urlPath: string, path: string){
  urlPath = urlPath + (urlPath.slice(-1) === "/" ? "" : "/");
  path = path + (path.slice(-1) === "/" ? "" : "/");
  return urlPath === path;
}
