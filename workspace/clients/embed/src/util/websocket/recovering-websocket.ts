import { createSimpleEmitter } from "../events";
import { delay } from "@divinci-ai/utils";

type RecoverWebsocketReadyState = "delay" | "attempt" | "open" | "closing" | "closed";

export class RecoveringWebsocket {
  connection: WebSocket;
  parameters: ConstructorParameters<typeof WebSocket>;
  queue: Array<Parameters<WebSocket["send"]>> = [];
  currentlyDelaying = false;
  permenantlyClose = false;

  onMessage = createSimpleEmitter<[MessageEvent<any>]>();


  onReadyStateChange = createSimpleEmitter<[RecoverWebsocketReadyState]>();
  onConnectError = createSimpleEmitter<[Event]>();

  failReason: undefined | { status: number, reason: string };
  onFail = createSimpleEmitter<[{ status: number, reason: string }]>();

  get readyState(): RecoverWebsocketReadyState{
    const readyState = this.connection.readyState;
    if(this.permenantlyClose){
      return readyState === WebSocket.CLOSING ? "closing" : "closed";
    }
    switch(readyState){
      case WebSocket.CLOSED: return "delay";
      case WebSocket.CONNECTING: return "attempt";
      case WebSocket.OPEN: return "open";
      default: return "closed";
    }
  }

  constructor(private reconnectDelay: number, ...args: ConstructorParameters<typeof WebSocket>){
    this.parameters = args;
    this.connection = new WebSocket(...this.parameters);
    this.#newWebsocket();
  }

  async #delayedReconnect(){
    if(this.currentlyDelaying) return;
    this.currentlyDelaying = true;
    this.onReadyStateChange.emit("delay");
    await delay(this.reconnectDelay);
    if(this.permenantlyClose) return;
    this.currentlyDelaying = false;
    this.connection = new WebSocket(...this.parameters);
    this.#newWebsocket();
  }

  #newWebsocket(){

    console.log("new websocket");

    const ws = this.connection;
    ws.addEventListener("message", (e)=>{
      this.onMessage.emit(e);
    });

    this.onReadyStateChange.emit("attempt");
    ws.addEventListener("open", ()=>{
      this.onReadyStateChange.emit("open");
      this.#emptyQueue();
    });
    ws.addEventListener("close", (e)=>{
      if(this.permenantlyClose){
        return this.onReadyStateChange.emit("closed");
      }
      if(!e.wasClean){
        return this.#delayedReconnect();
      }

      console.log("close error:", e);

      this.permenantlyClose = true;
      this.onReadyStateChange.emit("closed");
      this.failReason = { status: e.code, reason: e.reason };
      this.onFail.emit(this.failReason);
    });
    ws.addEventListener("error", (e)=>{
      console.log("error:", e);
      if(this.permenantlyClose) return;

      this.permenantlyClose = true;
      this.onReadyStateChange.emit("closed");
      this.failReason = { status: 500, reason: "error trying to connect" };
      this.onFail.emit(this.failReason);


      // this.onConnectError.emit(e);
      // this.#delayedReconnect();
    });

  }

  send(...args: Parameters<WebSocket["send"]>){
    if(this.permenantlyClose){
      throw new Error("WebSocket is Closed");
    }
    if(this.connection.readyState === WebSocket.OPEN){
      this.connection.send(...args);
    } else {
      this.queue.push(args);
    }
  }

  #emptyQueue(){
    for(const item of this.queue){
      this.connection.send(...item);
    }
    this.queue = [];
  }

  close(){
    if(this.permenantlyClose){
      return;
    }
    this.permenantlyClose = true;
    if(this.connection.readyState === WebSocket.CLOSED){
      this.onReadyStateChange.emit("closed");
    } else {
      this.connection.close();
    }
  }
}
