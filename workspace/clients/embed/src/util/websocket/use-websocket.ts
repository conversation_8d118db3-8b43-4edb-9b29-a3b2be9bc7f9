import { useRef, useEffect, useState } from "react";

import { useAuth0WebSocket } from "../../globals/auth0";
import { RecoveringWebsocket } from "./recovering-websocket";

export function useWebsocket({ url }: { url: string }){
  const getWS = useAuth0WebSocket();
  const lastUrl = useRef(url);
  const lastWebsocket = useRef<null | RecoveringWebsocket>(null);
  const unmounted = useRef(false);
  const [activeWS, setActiveWS] = useState<null | RecoveringWebsocket>(null);

  useEffect(()=>{
    console.log("attempting to create websocket");
    unmounted.current = false;
    lastUrl.current = url;
    Promise.resolve().then(async ()=>{
      try {
        const websocket = await getWS(url);
        if(lastUrl.current !== url || unmounted.current){
          return websocket.close();
        }
        lastWebsocket.current = websocket;
        setActiveWS(websocket);
      }catch(e){
        console.error("websocket error:", e);
      }
    });

    return ()=>{
      console.log("websocket should close:", activeWS, lastWebsocket.current);
      unmounted.current = true;
      if(lastWebsocket.current){
        lastWebsocket.current.close();
        setActiveWS(null);
        lastWebsocket.current = null;
      }
      lastUrl.current = "";
    };
  }, [url]);

  return activeWS;
}