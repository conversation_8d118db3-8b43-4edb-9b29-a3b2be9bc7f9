import { useEffect } from "react";

export function useOutsideAlerter(ref: null | HTMLElement, listener: () => void) {
  useEffect(()=>{
    function handleClickOutside(event) {
      if(ref && !ref.contains(event.target)){
        listener();
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return ()=>{
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [ref]);
}
