import React, { createContext, useContext, PropsWithChildren, useState, useEffect } from "react";
import { JSON_Object, castToObject, delay } from "@divinci-ai/utils";

import { embedClientUrl } from "./api";
import { WINDOW_MESSENGER } from "./WindowMessenger";

// <iframe src="embed.divinci.app?release=1234"></iframe>;
// <script src="embed.divinci.app/js" divinci-release="1234"></script>;
// <iframe src="embed.divinci.app" divinci-release="1234"></iframe>;

// const RELEASE_LOCAL_STORAGE_KEY = "divinci-release-id";


type GlobalConfig = { url: string, releaseId: string, customCss?: string };
type GlobalConfigContextType = { config: null | GlobalConfig, error: null | Error };

const GlobalConfigContext = createContext<GlobalConfigContextType>({
  config: null, error: null
});

export function useGlobalConfig(){
  return useContext(GlobalConfigContext);
}

export function GlobalConfigProvider({ children }: PropsWithChildren){
  const [config, setConfig] = useState<GlobalConfig | null>(null);
  const [error, setError] = useState<Error | null>(null);

  useEffect(()=>{
    Promise.resolve().then(async ()=>{
      try {
        const configFromUrl = (function(){
          const params = new URLSearchParams(window.location.search);
          const releaseId = params.get("release");
          const customCss = params.get("cssSrc");
          if(releaseId){
            return { releaseId, customCss: customCss || void 0 };
          }
          return void 0;
        })();
        if(configFromUrl) return setConfig({ ...configFromUrl, url: returnToURL(true, configFromUrl) });

        if(window.parent === window) throw new Error("no parent window");

        const raw = await Promise.race([
          delay(5 * 1000).then(()=>{ throw new Error("timeout"); }),
          WINDOW_MESSENGER.request("get-global-config", {})
        ]);
        const value = castToObject(raw);
        if(typeof value.releaseId !== "string") throw new Error("expecting a releaseId from parent");

        const configFromParent = { releaseId: value.releaseId, customCss: getCustomCss(value) };

        setConfig({ ...configFromParent, url: returnToURL(false, configFromParent), });

      }catch(e: any){
        setError(e);
      }
    });
  }, []);

  return (
    <GlobalConfigContext.Provider value={{ config, error }}>
      { children }
    </GlobalConfigContext.Provider>
  );

}

function returnToURL(fromUrl: boolean, config: Omit<GlobalConfig, "url">){
  const returnURl = embedClientUrl("/");
  if(!fromUrl) return returnURl.href;
  returnURl.searchParams.set("release", config.releaseId);
  if(config.customCss) returnURl.searchParams.set("cssSrc", config.customCss);
  return returnURl.href;
}


function getCustomCss(value: JSON_Object){
  if(!("cssSrc" in value)) return void 0;
  if(typeof value.cssSrc === "undefined") return void 0;
  if(typeof value.cssSrc !== "string") throw new Error("expecting customCSS to be a string");
  return value.cssSrc;
}


const CURRENT_SCRIPT = document.currentScript;


// NOT USED: script is loaded inside an iframe, iframe cannot be accessed when different origins
export function getHTMLAttribute(key: string){
  const fromScript = (function(){
    const script = CURRENT_SCRIPT;
    if(!script) return null;
    return script.getAttribute(key);
  })();

  if(fromScript) return fromScript;

  const fromFrame = (function(){

    if(window.parent === window) return null;
    if(!window.frameElement) return null;
    return window.frameElement.getAttribute(key);
  })();

  if(fromFrame) return fromFrame;

  return null;
}
