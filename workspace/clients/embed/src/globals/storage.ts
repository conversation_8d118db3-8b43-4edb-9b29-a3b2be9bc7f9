import { JSON_Unknown } from "@divinci-ai/utils";

export async function setItem(key: string, value: JSON_Unknown) {
  localStorage.setItem(key, JSON.stringify(value));
  return value;
}

export async function getItem(key: string) {
  const val = localStorage.getItem(key);
  if(val === null) return val;
  return JSON.parse(val);
}

export async function getKeys() {
  const l = localStorage.length;
  const keys: Array<string> = [];
  for(let i = 0; i < l; i++) {
    const key = localStorage.key(i);
    if(key !== null) keys.push(key);
  }
  return keys;
}

export async function removeItem(key: string) {
  const val = localStorage.getItem(key);
  localStorage.removeItem(key);
  if(val === null) return val;
  return JSON.parse(val);
}

export const Storage = {
  setItem: setItem,
  getItem: getItem,
  removeItem: removeItem,
};

export class StorageItem<T extends JSON_Unknown> {
  constructor(private key: string) {}
  setItem(value: T) {
    return setItem(this.key, value) as Promise<T>;
  }
  getItem(): Promise<T | null> {
    return getItem(this.key);
  }
  removeItem(): Promise<T | null> {
    return removeItem(this.key);
  }
}
