import { MessageRouter } from "../util/messenger";


// https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage
class WindowMessenger extends MessageRouter {
  constructor(endpointWindow: Window){
    super(
      (listener)=>(window.addEventListener("message", (event)=>{
        if(event.source !== endpointWindow) return;
        console.log("message from parent:", event);
        listener(event.data);
      })),
      (message)=>(endpointWindow.postMessage(message, "*"))
    );
  }

}

export const WINDOW_MESSENGER = new WindowMessenger(window.parent);