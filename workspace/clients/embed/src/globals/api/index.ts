import { jsonResponse } from "@divinci-ai/utils";


import { WEB_CLIENT_IS_SECURE, WEB_CLIENT_HOST } from "./constants";
export function webClientUrl(path: string) {
  return new URL(path, `http${WEB_CLIENT_IS_SECURE ? "s" : ""}://${WEB_CLIENT_HOST}`);
}

import { EMBED_CLIENT_IS_SECURE, EMBED_CLIENT_HOST } from "./constants";
export function embedClientUrl(path: string) {
  return new URL(path, `http${EMBED_CLIENT_IS_SECURE ? "s" : ""}://${EMBED_CLIENT_HOST}`);
}

import { API_IS_SECURE, API_HOST, } from "./constants";
const API_ORIGIN = `http${API_IS_SECURE ? "s" : ""}://${API_HOST}`;
export function apiUrl(path: string) {
  return new URL(path, API_ORIGIN);
}
export { API_ORIGIN };

import { API_LIVE_IS_SECURE, API_LIVE_HOST, } from "./constants";
const API_LIVE_ORIGIN_SUFFIX = `${API_LIVE_IS_SECURE ? "s" : ""}://${API_LIVE_HOST}`;
export function apiLiveUrlHTTP(path: string) {
  return new URL(path, "http" + API_LIVE_ORIGIN_SUFFIX);
}
export function apiLiveUrlWS(path: string) {
  return new URL(path, "ws" + API_LIVE_ORIGIN_SUFFIX);
}

export function fetchApi(path: string, init?: RequestInit | undefined) {
  const href = apiUrl(path).href;
  return fetch(href, init);
}

export function fetchApiJSON(path: string, init?: RequestInit | undefined) {
  return jsonResponse(fetchApi(path, init));
}
