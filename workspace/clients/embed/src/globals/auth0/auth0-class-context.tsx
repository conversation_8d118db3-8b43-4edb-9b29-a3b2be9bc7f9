import React, { createContext, useContext, PropsWith<PERSON>hildren, useState, useRef, useEffect } from "react";
import { createAuth0Client, Auth0Client, User, Auth0ClientOptions } from "@auth0/auth0-spa-js";

import { AUTH0_STORAGE_PREFIX } from "./constants";
import { embedClientUrl } from "../api";
import { waitForEventTarget } from "../../util/events";

type CustomAuth0ContextType = {
  error: null | Error,
  isAuthenticated: boolean | null,
  isLoading: boolean,
  user: null | User,

  getAccessTokenSilently: ()=>Promise<string>,
  getAccessTokenWithPopup: InstanceType<typeof Auth0Client>["getTokenWithPopup"],
  getIdTokenClaims: InstanceType<typeof Auth0Client>["getIdTokenClaims"],
  loginWithRedirect: InstanceType<typeof Auth0Client>["loginWithRedirect"],
  loginWithPopup: InstanceType<typeof Auth0Client>["loginWithPopup"],
  logout: InstanceType<typeof Auth0Client>["logout"],
  logoutSilently: InstanceType<typeof Auth0Client>["logout"],
};


const CONTEXT_NOT_READY = ()=>Promise.reject(new Error("Auth0Client not yet loaded"));

const CustomAuth0Context = createContext<CustomAuth0ContextType>({
  error: null,
  isAuthenticated: null,
  isLoading: true,
  user: null,

  getAccessTokenSilently: CONTEXT_NOT_READY,
  getAccessTokenWithPopup: CONTEXT_NOT_READY,
  getIdTokenClaims: CONTEXT_NOT_READY,
  loginWithRedirect: CONTEXT_NOT_READY,
  loginWithPopup: CONTEXT_NOT_READY,
  logout: CONTEXT_NOT_READY,
  logoutSilently: CONTEXT_NOT_READY,
});


export function useCustomAuth0(){
  return useContext(CustomAuth0Context);
}

export function CustomAuth0Provider({ children, ...auth0Options }: PropsWithChildren<Auth0ClientOptions>){

  const auth0Client = useRef<Auth0Client | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [user, setUser] = useState<User | null>(null);

  useEffect(()=>{
    Promise.resolve().then(async ()=>{
      await setupClient();
      setIsLoading(false);
    });
  }, []);

  return (
    <CustomAuth0Context.Provider value={{
      error, isLoading, isAuthenticated, user,
      getAccessTokenSilently: async function(...args){
        if(error) throw error;
        await setupClient();
        if(!auth0Client.current) throw new Error("Auth0Client not yet loaded");
        return auth0Client.current.getTokenSilently(...args);
      },
      getAccessTokenWithPopup: async ()=>{
        if(error) throw error;
        await setupClient();
        if(!auth0Client.current) throw new Error("Auth0Client not yet loaded");
        return auth0Client.current.getTokenWithPopup();
      },
      getIdTokenClaims: async (...args)=>{
        if(error) throw error;
        await setupClient();
        if(!auth0Client.current) throw new Error("Auth0Client not yet loaded");
        return auth0Client.current.getIdTokenClaims(...args);
      },
      loginWithRedirect: async (...args)=>{
        try {
          setError(null);
          await setupClient();
          if(!auth0Client.current) throw new Error("Auth0Client not yet loaded");
          const result = await auth0Client.current.loginWithRedirect(...args);
          await updateAuthStatus();
          return result;
        }catch(error: any){
          setError(error);
        }
      },
      loginWithPopup: async (...args)=>{
        try {
          setError(null);
          await setupClient();
          if(!auth0Client.current) throw new Error("Auth0Client not yet loaded");
          const result = await auth0Client.current.loginWithPopup(...args);
          await updateAuthStatus();
          return result;
        }catch(error: any){
          setError(error);
        }
      },
      logout: async (...args)=>{
        try {
          setError(null);
          await setupClient();
          if(!auth0Client.current) throw new Error("Auth0Client not yet loaded");
          const result = await auth0Client.current.logout(...args);
          await updateAuthStatus();
          return result;
        }catch(error: any){
          setError(error);
        }
      },
      logoutSilently: async ()=>{
        try {
          setError(null);
          await setupClient();
          if(!isAuthenticated) return Promise.resolve();
          if(!auth0Client.current) throw new Error("Auth0Client not yet loaded");
          await logoutWithIframe(auth0Options, auth0Client.current);
          document.cookie = "";
          clearLocalStorage();
          auth0Client.current = null;
          await setupClient();
        }catch(error: any){
          setError(error);
        }
      },
    }}>
      { children }
    </CustomAuth0Context.Provider>
  );

  async function setupClient(){
    if(auth0Client.current) return auth0Client.current;
    auth0Client.current = await createAuth0Client(auth0Options);
    await updateAuthStatus();
    return auth0Client.current;
  }

  async function updateAuthStatus(){
    if(!auth0Client.current) return false;
    const [updatedStatus, updatedUser] = await Promise.all([
      auth0Client.current.isAuthenticated(),
      auth0Client.current.getUser(),
    ]);
    setIsAuthenticated(updatedStatus);
    setUser(updatedUser || null);
    return updatedStatus;
  }

}

export const SILENT_LOGOUT_PARAM = "silent-logout";

async function logoutWithIframe(options: Auth0ClientOptions, client: Auth0Client){

  const returnToUrl = embedClientUrl("/");
  returnToUrl.searchParams.set(SILENT_LOGOUT_PARAM, "true");

  const iframe = document.createElement("iframe");

  const promise = waitForEventTarget(window, "message", (e: MessageEvent)=>{
    if(e.source !== iframe.contentWindow) return false;
    if(e.data !== "silent-logout") return false;
    return true;
  });
  iframe.style.display = "none";
  iframe.src = `https://${options.domain}/v2/logout?client_id=${options.clientId}&returnTo=${encodeURIComponent(returnToUrl.href)}`;
  document.body.appendChild(iframe);

  await promise;

  try {
    await client.checkSession();
  }catch(e){
    // ok wether sucess or not
  }
}

function clearLocalStorage(){
  for(let i = 0; i < localStorage.length; i++){
    const key = localStorage.key(i);
    if(key && key.startsWith(AUTH0_STORAGE_PREFIX)){
      localStorage.removeItem(key);
    }
  }
}


function deleteAllCookies(){
  console.log("Deleting all cookies:", document.cookie, "cookie?");
  document.cookie.split(";").forEach((cookie)=>{
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substring(0, eqPos) : cookie;
      document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT";
  });
}
