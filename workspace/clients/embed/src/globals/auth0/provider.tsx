
import React, { <PERSON>psWith<PERSON>hildren } from "react";
import { Auth0Provider as Auth0ProviderRaw, useAuth0 } from "@auth0/auth0-react";
import { CustomAuth0Provider, useCustomAuth0 } from "./auth0-class-context";

import {
  AUTH0_CLIENT_DOMAIN, AUTH0_CLIENT_ID, AUTH0_AUDIENCE, REDIRECT_URI, AUTH0_STORAGE_PREFIX,
} from "./constants";

import { setItem, getItem, removeItem } from "../storage";

import { JSON_Unknown } from "@divinci-ai/utils";

export { useCustomAuth0 as useAuth0 };

export function Auth0Provider({ children }: PropsWithChildren){
  return (
    <CustomAuth0Provider
      domain={AUTH0_CLIENT_DOMAIN}
      clientId={AUTH0_CLIENT_ID}
      authorizationParams={{
        redirect_uri: REDIRECT_URI,
        audience: AUTH0_AUDIENCE,
      }}
      cache={{
        set: async (key, v) => {
          await setItem(AUTH0_STORAGE_PREFIX + key, v as JSON_Unknown);
        },
        get: async (key) => {
          const v = await getItem(AUTH0_STORAGE_PREFIX + key);
          if(v === null) return null;
          if(typeof v !== "object") return v;
          const exp = v.decodedToken?.claims?.exp;
          if(typeof exp !== "number") return v;
          if(exp * 1000 < Date.now()) return null;
          return v;
        },
        remove: (key) => {
          return removeItem(AUTH0_STORAGE_PREFIX + key);
        },
      }}
    >
      {children}
    </CustomAuth0Provider>
  );

}
