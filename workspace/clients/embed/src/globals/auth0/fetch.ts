
import { fetchApi, fetchApi<PERSON><PERSON><PERSON> } from "../api";
import { useAuth0 } from "../auth0";
import { jsonResponse } from "@divinci-ai/utils";

import { UNAUTHED_USER_MSG } from "./errors";

export function useOptionalAuth0Fetch(){
  const {
    isAuthenticated, isLoading, getAccessTokenSilently
  } = useAuth0();
  if(isLoading){
    return async ()=>{
      throw new Error(UNAUTHED_USER_MSG);
    };
  }
  if(!isAuthenticated){
    return async (path: string, init?: RequestInit | undefined)=>{
      return fetchApi(path, init);
    };
  }
  return async (path: string, init?: RequestInit | undefined)=>{
    const token = await getAccessTokenSilently();
    return auth0Fetch(token, path, init);
  };
}
export function useAuth0Fetch(){
  const {
    isAuthenticated, isLoading, getAccessTokenSilently
  } = useAuth0();
  if(isLoading){
    return async ()=>{
      throw new Error(UNAUTHED_USER_MSG);
    };
  }
  if(!isAuthenticated){
    return async ()=>{
      throw new Error(UNAUTHED_USER_MSG);
    };
  }
  return async (path: string, init?: RequestInit | undefined)=>{
    const token = await getAccessTokenSilently();
    return auth0Fetch(token, path, init);
  };
}

export function useAuth0FetchJSON(){
  const {
    isAuthenticated, isLoading, getAccessTokenSilently,
  } = useAuth0();
  if(isLoading){
    return async ()=>{
      throw new Error(UNAUTHED_USER_MSG);
    };
  }
  if(!isAuthenticated){
    return async ()=>{
      throw new Error(UNAUTHED_USER_MSG);
    };
  }
  return async (path: string, init?: RequestInit | undefined)=>{
    const token = await getAccessTokenSilently();
    return auth0FetchJSON(token, path, init);
  };
}

export async function auth0Fetch(token: string, path: string, init?: RequestInit | undefined){
  if(!init) init = {};
  if(typeof init.headers === "undefined") init.headers = new Headers();
  else if(
    typeof init.headers === "object" && !(init.headers instanceof Headers)
  ) init.headers = new Headers(init.headers);

  init.headers.append("authorization", "Bearer " + token);

  return fetchApi(path, init);
}

export async function auth0FetchJSON(token: string, path: string, init?: RequestInit | undefined){
  return jsonResponse(auth0Fetch(token, path, init));
}
