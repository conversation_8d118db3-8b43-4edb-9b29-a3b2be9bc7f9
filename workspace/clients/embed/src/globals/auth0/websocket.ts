import { useAuth0 } from "../auth0";
import { apiLiveUrlWS, apiLiveUrlHTTP } from "../api";
import { RecoveringWebsocket } from "../../util/websocket/recovering-websocket";

import { UNAUTHED_USER_MSG } from "./errors";
import { jsonResponse } from "@divinci-ai/utils";

const RECONNECT_DELAY = 1000;

export async function getCookie(authToken: string){
  return await jsonResponse(
    fetch(apiLiveUrlHTTP("/cookie/new"), {
      credentials: "include",
      headers: {
        authorization: "Bearer " + authToken,
      },
      keepalive: true,
    }),
  );
}

export function useAuth0WebSocket(){
  const { isAuthenticated, isLoading, getAccessTokenSilently } = useAuth0();
  if(isLoading){
    return async ()=>{
      throw new Error(UNAUTHED_USER_MSG);
    };
  }
  if(!isAuthenticated){
    return async ()=>{
      throw new Error(UNAUTHED_USER_MSG);
    };
  }
  return async (path: string): Promise<RecoveringWebsocket>=>{
    const authToken = await getAccessTokenSilently();
    await getCookie(authToken);
    return new RecoveringWebsocket(RECONNECT_DELAY, apiLiveUrlWS(path));
  };
}

export function createWebSocket(path: string){
  return new RecoveringWebsocket(RECONNECT_DELAY, apiLiveUrlWS(path));
}

export function useOptionalAuth0WebSocket(){
  const { isAuthenticated, isLoading, getAccessTokenSilently } = useAuth0();
  if(isLoading){
    return async ()=>{
      throw new Error(UNAUTHED_USER_MSG);
    };
  }
  if(!isAuthenticated){
    return async (path: string)=>{
      return new RecoveringWebsocket(RECONNECT_DELAY, apiLiveUrlWS(path));
    };
  }
  return async (path: string)=>{
    const authToken = await getAccessTokenSilently();
    await getCookie(authToken);
    return new RecoveringWebsocket(RECONNECT_DELAY, apiLiveUrlWS(path));
  };
}
