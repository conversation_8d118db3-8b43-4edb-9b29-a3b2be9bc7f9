



import React, { createContext, useContext, PropsWithChildren } from "react";
import { WhiteLabelReleaseDoc } from "@divinci-ai/models";
import { TrackedValueTypes, useTrackedValue, } from "../util/react/tracked-value";
import { useGlobalConfig } from "./global-config";

type ReleaseContextType = TrackedValueTypes<WhiteLabelReleaseDoc>;

const ReleaseContext = createContext<ReleaseContextType>({
  loading: true, error: void 0, value: void 0,
});

export function useRelease(){
  const context = useContext(ReleaseContext);
  return context;
}

export function ReleaseProvider({ children }: PropsWithChildren){
  const { config, error: globalError } = useGlobalConfig();

  const tracked = useTrackedValue<WhiteLabelReleaseDoc>({
    url: !config ? void 0 : `/white-label-release/${config.releaseId}`
  });

  const error = (function(){
    if(globalError) return globalError.message;
    if(tracked.error) return tracked.error;
  })();

  return (
    <ReleaseContext.Provider
      value={error ? { loading: false, error, value: void 0 } : tracked}
    >
      { children }
    </ReleaseContext.Provider>
  );
}

