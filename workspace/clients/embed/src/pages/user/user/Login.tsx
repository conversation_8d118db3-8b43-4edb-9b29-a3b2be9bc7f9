import React, { useState } from "react";

import { useAuth0 } from "../../../globals/auth0";
import { useRelease } from "../../../globals/release";

export function LoginPage(){
  const { value: release } = useRelease();
  const { loginWithPopup } = useAuth0();
  const [error, setError] = useState<string | null>(null);

  if(!release) return null;

  return (
    <div style={{ height: "100%", width: "100%" }} className="center-child" >
      <div >
        {release.allowAnonymousChat ? null : <h1>You must Login to use this AI</h1>}
        <button
          className="loginButton"
          onClick={async ()=>{
          try {
            setError(null);
            await loginWithPopup();
          }catch(e: any){
            setError(e.message);
          }
        }}>Login</button>
        {error ? <div>{error}</div> : null}
      </div>
    </div>
  );
}
