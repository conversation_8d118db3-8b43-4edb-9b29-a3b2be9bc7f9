

import React, { createContext, useContext, PropsWithChildren, useState } from "react";

import { RawAnonymousChatProvider } from "../anonymous-chat/data/anonymous-chat";


type ActiveChatContextType = {
  isAnonymous: boolean,
  chatId: null | string,
  setActive: (isAnonymous: boolean, chatId: null | string)=>any,
};

const ActiveChatContext = createContext<ActiveChatContextType>({
  isAnonymous: true, chatId: null, setActive: ()=>{}
});


export function useActiveChat(){
  return useContext(ActiveChatContext);
}

export function ActiveChatProvider({ children }: PropsWithChildren){

  const [isAnonymous, setIsAnonymous] = useState<boolean>(true);
  const [chatId, setChatId] = useState<null | string>(null);

  return (
    <ActiveChatContext.Provider
      value={{
        isAnonymous, chatId, setActive: (isAnonymous: boolean, chatId: null | string)=>{
          setIsAnonymous(isAnonymous);
          setChatId(chatId);
        }
      }}
    >
    <RawAnonymousChatProvider>
    { children }
    </RawAnonymousChatProvider>
    </ActiveChatContext.Provider>
  );
}

