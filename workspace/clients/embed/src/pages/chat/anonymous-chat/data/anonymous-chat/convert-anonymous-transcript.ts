import { AnonymousMessage, TranscriptMessage, AI_CATEGORY_ENUM } from "@divinci-ai/models";


import { uniqueId } from "@divinci-ai/utils";

export function anonymousToNormalTranscript(
  releaseId: string, anonymousChat: Array<AnonymousMessage>, category: AI_CATEGORY_ENUM
): Array<TranscriptMessage>{

  const messages: Array<TranscriptMessage> = [];

  for(const message of anonymousChat){
    const prevMessage = messages[messages.length - 1];
    const promptId = uniqueId();
    messages.push({
      _id: promptId,
      release: releaseId,
      replyTo: prevMessage ? prevMessage._id : void 0,
      content: message.prompt,
      category: category,
      role: "user",
      name: "You",
      timestamp: message.promptTimestamp,
      metadata: [],
      context: message.context,
    });
    if(message.responseTimestamp === -1) continue;
    messages.push({
      _id: uniqueId(),
      release: releaseId,
      replyTo: promptId,
      content: message.response,
      category: category,
      role: "assistant",
      name: "AI",
      timestamp: message.responseTimestamp,
      metadata: [],
      context: [],
    });
  }

  return messages;
}
