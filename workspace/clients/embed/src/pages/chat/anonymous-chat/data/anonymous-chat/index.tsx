

import React, { createContext, useContext, PropsWithChildren, useState } from "react";
import { jsonResponse } from "@divinci-ai/utils";
import { AI_CATEGORY_ENUM, AnonymousMessage } from "@divinci-ai/models";
import { useOptionalAuth0Fetch } from "../../../../../globals/auth0";
import { useRelease } from "../../../../../globals/release";

import { TranscriptProvider } from "../../../../../components/Transcript";

import { anonymousToNormalTranscript } from "./convert-anonymous-transcript";

type RawAnonymousContextType = {
  releaseId: string,
  messages: Array<AnonymousMessage>,
  signiture: string,
  running: boolean,
  addMessage: (newMessage: string)=>any
};

const RawAnonymousContext = createContext<RawAnonymousContextType>({
  releaseId: "", messages: [], signiture: "", running: false, addMessage: ()=>{}
});


export function useRawAnonymousChat(){
  return useContext(RawAnonymousContext);
}

export function RawAnonymousChatProvider({ children }: PropsWithChildren){

  const auth0Fetch = useOptionalAuth0Fetch();
  const { value: release } = useRelease();
  const [running, setRunning] = useState<boolean>(false);
  const [transcript, setTranscript] = useState<Array<AnonymousMessage>>([]);
  const [signiture, setSigniture] = useState<string>("");
  const [prevError, setPrevError] = useState<boolean>(false);

  return (
    <RawAnonymousContext.Provider
      value={{
        addMessage,
        running,
        messages: transcript,
        signiture,
        releaseId: release ? release._id : "",
      }}
    >
    { children }
    </RawAnonymousContext.Provider>
  );

  async function addMessage(newMessage: string){
    if(!release) throw new Error("❌ No release provided. ");

    const promptResponse: AnonymousMessage = {
      context: [],
      promptTimestamp: Date.now(),
      prompt: newMessage,
      responseTimestamp: -1,
      response: "",
    };

    try {
      setPrevError(false);
      setRunning(true);

      setTranscript([...transcript, promptResponse]);

      const {
        transcript: newTranscript, signiture: newSigniture
      } = await jsonResponse(auth0Fetch("/ai-chat/anonymous-chat", {
        method: "POST",
        body: JSON.stringify({
          releaseId: release._id,
          prevSigniture: signiture,
          transcript: transcript,
          newPrompt: newMessage,
        })
      })) as {
        transcript: Array<AnonymousMessage>, signiture: string
      };

      setRunning(false);
      setTranscript(newTranscript);
      setSigniture(newSigniture);
    }catch(e){
      setPrevError(true);
      setTranscript([...transcript]);
      setRunning(false);
      throw e;
    }
  }


}


export function AnonymousChatProvider({ children }: PropsWithChildren){

  const { releaseId, running, messages, addMessage } = useRawAnonymousChat();

  return (
    <TranscriptProvider
      running={running}
      messages={anonymousToNormalTranscript(releaseId, messages, AI_CATEGORY_ENUM.TEXT)}
      addMessage={addMessage}
    >
    { children }
    </TranscriptProvider>
  );
}
