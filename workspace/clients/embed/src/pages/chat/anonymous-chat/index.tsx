import React, { useEffect } from "react";

import { AnonymousChatProvider, } from "./data/anonymous-chat";

import { RenderMessages, MessageForm } from "../../../components/Transcript";
import { SaveAnonymousChat } from "./SaveAnonymousChat";
import { useActiveChat } from "../data/active-chat";

export function AnonymousChatPage(){

  const { setActive } = useActiveChat();

  useEffect(()=>{
    setActive(true, null);
  }, []);

  return (
    <AnonymousChatProvider>
      <TranscriptRender />
    </AnonymousChatProvider>
  );
}

import { MessageHeader } from "./Message/MessageHeader";
import { MessageFooter } from "./Message/MessageFooter";


function TranscriptRender(){
  return (
    <div className="chatRender">
      <div className="chatTranscript">
      <RenderMessages
        MessageWrapper={({ message, children })=>(
          <>
          <MessageHeader />
          {children}
          <MessageFooter message={message} />
          </>
        )}
      />
      </div>
      <SaveAnonymousChat />
      <MessageForm />
    </div>
  );
}
