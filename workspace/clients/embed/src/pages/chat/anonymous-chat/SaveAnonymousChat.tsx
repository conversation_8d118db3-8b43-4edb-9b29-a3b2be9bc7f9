import React, { useState } from "react";

import { useSaveAnonymous } from "./save-anonymous";

import { useTranscript, } from "../../../components/Transcript";
import { useAuth0 } from "../../../globals/auth0";
import { useNavigate } from "react-router";
import { replaceParams } from "../../../util/router";
import { PATH_CHAT_ITEM_PAGE } from "../Route/path";

import { useActiveChat } from "../data/active-chat";


export function SaveAnonymousChat(){
  const { isAuthenticated } = useAuth0();
  const { messages } = useTranscript();
  const navigate = useNavigate();

  const saveAnonymous = useSaveAnonymous();
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setActive } = useActiveChat();

  if(!isAuthenticated || messages.length <= 1) return null;
  return (
    <>
      { error && <div className="error-message">{ error }</div> }
      <button
        disabled={saving}
        onClick={(e)=>{e.preventDefault(); saveChat(); }}
      >Save Anonymous Chat</button>
    </>
  );

  async function saveChat(){
    try {
      setSaving(true);
      setError(null);

      const chat = await saveAnonymous();

      navigate(replaceParams(PATH_CHAT_ITEM_PAGE, { chatId: chat._id }));

    }catch(e: any){
      setSaving(false);
      setError(e.message);
    }
  }
}

