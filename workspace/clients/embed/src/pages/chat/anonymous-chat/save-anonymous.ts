import { useCallback } from "react";

import { generateTitle } from "../util/title";
import { AIChat } from "@divinci-ai/models";

import { useRawAnonymousChat } from "./data/anonymous-chat";

import { useAuth0, useAuth0Fetch } from "../../../globals/auth0";
import { useRelease } from "../../../globals/release";


export function useSaveAnonymous(){
  const { isAuthenticated } = useAuth0();
  const auth0Fetch = useAuth0Fetch();
  const { messages: anonMessages, signiture } = useRawAnonymousChat();
  const { value: release } = useRelease();

  return useCallback(async function(){
    if(!release){
      throw new Error("No Release Available");
    }
    const title = generateTitle();
    const response = await auth0Fetch("/ai-chat/anonymous-to-owned", {
      method: "POST",
      headers: { "content-type": "application/json" },
      body: JSON.stringify({
        releaseId: release._id,
        title: title,
        prevSigniture: signiture,
        transcript: anonMessages
      })
    });

    if(!response.ok){
      throw await response.json();
    }

    const { chat } = await response.json() as { chat: AIChat };

    return chat;
  }, [isAuthenticated, anonMessages, signiture, release]);
}
