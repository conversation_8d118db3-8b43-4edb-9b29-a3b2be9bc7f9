import React, { createContext, Props<PERSON>ith<PERSON><PERSON>dren, useContext, useState } from "react";

import { TranscriptMessage, Transcript } from "@divinci-ai/models";

import { useRelease  } from "../../../../globals/release";

import { TranscriptProvider } from "../../../../components/Transcript";

import { useTrackWS } from "../../../../util/react/track-ws";
import { useRefetch } from "../../../../util/react/refetch";
import { useAuth0Fetch } from "../../../../globals/auth0";
import { jsonResponse } from "@divinci-ai/utils";

type CurrentChatContextType = {
  chatId: string,
};

const CurrentChatContext = createContext<CurrentChatContextType>({
  chatId: ""
});


export function useCurrentChat(){
  return useContext(CurrentChatContext);
}


export function ChatProvider({ chatId, children }: PropsWithChildren<{ chatId: string }>){

  const auth0Fetch = useAuth0Fetch();
  const [running, setRunning] = useState<boolean>(false);
  const [transcript, setTranscript] = useState<Array<TranscriptMessage>>([]);

  const [forceUpdate, setForceUpdate] = useState(Date.now());
  const [error, setError] = useState<string>("");
  const { value: release } = useRelease();


  useRefetch<Transcript>({
    requiresAuth: true,
    url: `/ai-chat/${chatId}/transcript`,
    forceUpdate, setError,
    setValue: (newValue)=>{
      setError("");
      if(!newValue) return;
      if(!release) return;
      setTranscript(
        newValue.messages.filter((message)=>(message.release === release._id))
      );
    }
  });

  useTrackWS<Transcript>({
    requiresAuth: true,
    url: `/ai-chat/${chatId}/transcript`, setValue:(v)=>{
      if(!release) return;
      setTranscript(v.messages.filter((message)=>(message.release === release._id)));
    },
    forceUpdate: ()=>{setForceUpdate(Date.now());},
  });


  return (
    <CurrentChatContext.Provider value={{ chatId }}>
    <TranscriptProvider running={running} messages={transcript} addMessage={addMessage}>
      { children }
    </TranscriptProvider>
    </CurrentChatContext.Provider>
  );

  async function addMessage(message: string){

    try {
      if(!release){
        throw new Error("No Release Available");
      }
      const { replyTo, assistantName } = (function(){
        const lastMessage = transcript[transcript.length - 1];
        if(!lastMessage) return { assistantName: release._id };
        if(lastMessage.role !== "assistant"){
          throw new Error("Currently waiting for message");
        }
        const replyTo = lastMessage._id;
        return { replyTo };
      })();

      setRunning(true);

      await jsonResponse(auth0Fetch(`/ai-chat/${chatId}/message`, {
        method: "POST",
        body: JSON.stringify({
          content: message,
          assistantName: assistantName,
          replyTo: replyTo
        })
      }));

      setRunning(false);
    }catch(e){
      setRunning(false);
      throw e;
    }
  }
}
