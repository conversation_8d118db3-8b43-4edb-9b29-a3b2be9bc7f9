import React from "react";
import { TranscriptMessage } from "@divinci-ai/models";

import { useCurrentChat } from "../data/chat";
import { MessageEmoji } from "../../../../components/Transcript/RenderMessages/MessageRelated/Emoji";

export function MessageHeader({ message }: { message: TranscriptMessage }) {

  const { chatId } = useCurrentChat();

  return (
    <div className="messageHeader">
      <div />

      <MessageEmoji fetchPrefix={`/ai-chat/${chatId}`} message={message} />
    </div>
  );
}
