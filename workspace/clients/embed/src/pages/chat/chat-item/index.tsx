import React, { useEffect } from "react";

import { use<PERSON>ara<PERSON>  } from "react-router";
import { ChatProvider } from "./data/chat";

import { RenderMessages, MessageForm } from "../../../components/Transcript";

import { useActiveChat } from "../data/active-chat";

export function ChatItemPage(){

  const { chatId } = useParams();

  const { setActive } = useActiveChat();

  useEffect(()=>{
    if(!chatId) return;
    setActive(false, chatId);
  }, [chatId]);

  if(!chatId) return null;

  return (
    <ChatProvider chatId={chatId} >
      <BasicRender />
    </ChatProvider>
  );
}

import { MessageHeader } from "./Message/MessageHeader";
import { MessageFooter } from "./Message/MessageFooter";

function BasicRender(){
  return (
    <div className="chatRender">
      <div className="chatTranscript">
        <span className="icon AINoticed">
          <i className="fas fa-info AINoticedIcon"></i>AIs can make mistakes. Double check important info.
        </span>
      <RenderMessages
        MessageWrapper={({ message, children })=>(
          <>
          <MessageHeader message={message} />
          {children}
          <MessageFooter message={message} />
          </>
        )}
      />
      </div>
      <MessageForm />
      <div className= "Footer">
        <a
          href={`//divinci.app/ai-safety-ethics.html`}
          target="_blank"
          rel="noopener noreferrer"
        >
          <i className= "fas fa-check"></i> AI Safety & Ethics
            -
          <i className= "fas fa-copyright copyright"></i> 2025 Divinci AI, Inc

        </a>
      </div>
    </div>
  );
}
