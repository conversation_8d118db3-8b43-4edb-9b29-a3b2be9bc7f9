import React from "react";
import { Route } from "react-router";
import { relative } from "../../../util/router";

import { ChatOutlet } from "./Outlet";
import {
  PATH_CHAT_PAGE, PATH_ANONYMOUS_CHAT_PAGE, PATH_CHAT_ITEM_PAGE,
} from "./path";

import { ChatIndexPage } from "../chat-index";
import { AnonymousChatPage } from "../anonymous-chat";
import { ChatItemPage } from "../chat-item";

export const ChatRoute = (
  <Route path={PATH_CHAT_PAGE} element={<ChatOutlet />} >
    <Route index element={<ChatIndexPage />} />
    <Route path={relative(PATH_CHAT_PAGE, PATH_ANONYMOUS_CHAT_PAGE)} element={<AnonymousChatPage />} />
    <Route path={relative(PATH_CHAT_PAGE, PATH_CHAT_ITEM_PAGE)} element={<ChatItemPage />} />
  </Route>
)
