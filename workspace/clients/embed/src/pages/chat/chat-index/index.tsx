import React, { useMemo } from "react";
import { useAuth0 } from "../../../globals/auth0";
import { Navigate, useNavigate } from "react-router";
import { PATH_USER_PAGE } from "../../user";
import { useTrackedValue } from "../../../util/react/tracked-value";
import { useRelease } from "../../../globals/release";
import { AIChatMeta } from "@divinci-ai/models";
import { DateDisplay } from "../../../components/DateDisplay";

import { replaceParams } from "../../../util/router";
import { PATH_CHAT_ITEM_PAGE } from "../Route/path";

import { StartNewChat } from "./StartNewChat";

import { useActiveChat } from "../data/active-chat";
import { useSaveAnonymous } from "../anonymous-chat/save-anonymous";
import { useRawAnonymousChat } from "../anonymous-chat/data/anonymous-chat";
import  "./chat.css";


export function ChatIndexPage(){
  const { isAuthenticated } = useAuth0();

  if(!isAuthenticated){
    return <Navigate to={PATH_USER_PAGE} />;
  }

  return <ChatList />;
}

function ChatList(){
  const navigate = useNavigate();
  const { isAnonymous } = useActiveChat();
  const { messages: anonMessages } = useRawAnonymousChat();
  const { value: release } = useRelease();
  const url = useMemo(() => {
    if(!release) return void 0;
    const url = new URL("/ai-chat", window.location.origin);
    url.searchParams.set("release", release._id);
    return url.pathname + url.search;
  }, [release]);

  const { value } = useTrackedValue<Array<AIChatMeta>>({ url });

  return (
    <>
    <div className="start-chat">
    <StartNewChat />
    </div>
    <ul className="chat-list">
      { (value || []).map((chat)=>(
        <li key={chat._id}>
          <a
          title={`${chat.updatedAt}`}
          onClick={async ()=>{
            if(isAnonymous && anonMessages.length > 0){
              if(!window.confirm("Going to this chat will erase the anonymous chat.\nIs this ok?")){
                return;
              }
            }
            navigate(replaceParams(PATH_CHAT_ITEM_PAGE, { chatId: chat._id }));
          }}>
            <div>
              <div className="chat-title">{chat.title}</div>
              {/* <div><span className="last-used">Last Used: </span><DateDisplay timestamp={chat.updatedAt} /></div> */}
            </div>
          </a>
        </li>
      ))}
    </ul>
    </>
  );
}
