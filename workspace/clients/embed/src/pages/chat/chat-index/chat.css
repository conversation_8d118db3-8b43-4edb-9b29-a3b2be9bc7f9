/* .chat-title {
  background-color: #f5f5f5;
  color: #000;
  padding: 10px;
} */

.chat-list {
  list-style-type: none;
  margin: 0;
  padding: 0;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
  max-width: 600px;
  margin: 20px auto; /* Center the list on the page */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Add shadow for depth */
}

.chat-list li {
  border-bottom: 1px solid #ddd;
  padding: 15px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.chat-list li:last-child {
  border-bottom: none; /* Remove border from the last item */
}

.chat-list li:hover {
  background-color: #f0f0f0; /* Highlight the item on hover */
}

.chat-list a {
  text-decoration: none;
  color: #333;
  display: block;
}

.chat-title {
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 5px;
  color: #007bff;
}

.chat-title:hover {
  text-decoration: underline;
}

.last-used {
  color: #666;
  font-size: 14px;
}

.start-chat {
  text-align: center;
  margin: 20px auto;
}

.start-chat button {
  background-color: #007bff;
  color: white;
  padding: 10px 12px;
  font-size: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin: 8px auto;
  top: 8px;
  max-width: 150px;
}

.start-chat button:hover {
  background-color: #0056b3;
}
