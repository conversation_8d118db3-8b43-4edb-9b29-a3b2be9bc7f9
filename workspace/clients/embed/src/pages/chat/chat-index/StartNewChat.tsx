import React, { useState } from "react";
import { useNavigate } from "react-router";

import { generateTitle } from "../util/title";
import { AIChat } from "@divinci-ai/models";
import { useAuth0Fetch } from "../../../globals/auth0";
import { replaceParams } from "../../../util/router";

import { PATH_CHAT_ITEM_PAGE } from "../Route/path";
import { useRelease } from "../../../globals/release";

export function StartNewChat(){
  const { value: release } = useRelease();
  const auth0Fetch = useAuth0Fetch();
  const navigate = useNavigate();

  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  return (
    <>
      <button
        className="button is-outline start-new-chat-button"
        style={{ width: "100%" }}
        disabled={saving}
        onClick={(e)=>{e.preventDefault(); startNewChat(); }}
      >Start New Chat</button>
      { error && <div className="error-message">{ error }</div> }
    </>
  );

  async function startNewChat(){
    try {
      if(!release){
        throw new Error("No Release Available");
      }

      setSaving(true);
      setError(null);
      const newTitle = generateTitle();

      const response = await auth0Fetch("/ai-chat", {
        method: "POST",
        body: JSON.stringify({ title: newTitle, releases: [release._id] }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      if(!response.ok){
        throw await response.json();
      }
      const { chat } = await response.json() as { chat: AIChat };

      console.log("💬 createNewChat::response: ", chat);

      // Use the provided URL from the response to navigate
      navigate(replaceParams(PATH_CHAT_ITEM_PAGE, { chatId: chat._id }));
    }catch(e: any){
      setSaving(false);
      setError(e.message);
    }
  }
}
