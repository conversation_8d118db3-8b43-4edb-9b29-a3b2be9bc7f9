import React from "react";

import ReactMarkdown from "react-markdown";
import { useRelease } from "../../globals/release";
import { DateDisplay } from "../../components/DateDisplay";

export function ReleaseInfoPage(){

  const { value: release } = useRelease();

  if(!release) return null;

  return (
    <div>
      <h1>{release.title}</h1>
      <div><span>Version: </span><span>{release.version}</span></div>
      {!release.releaseDate ? null : (
        <div><span>Released on: </span><span><DateDisplay timestamp={release.releaseDate} /></span></div>
      )}
      <ReactMarkdown>{release.description}</ReactMarkdown>
    </div>
  );
}

