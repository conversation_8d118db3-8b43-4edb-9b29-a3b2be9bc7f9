import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth0 } from "../../globals/auth0";
import { PATH_CHAT_PAGE, PATH_ANONYMOUS_CHAT_PAGE } from "../chat";
import { PATH_USER_PAGE } from "../user";
import { useRelease } from "../../globals/release";

export function IndexPage(){
  const { isAuthenticated, isLoading } = useAuth0();
  const { value: release } = useRelease();

  if(isLoading){
    return <h1>Loading...</h1>;
  }
  if(!release){
    return <h1>Loading...</h1>;
  }

  if(isAuthenticated){
    return <Navigate to={PATH_CHAT_PAGE} />;
  }

  if(!release.allowAnonymousChat){
    return <Navigate to={PATH_USER_PAGE} />;
  }

  return <Navigate to={PATH_ANONYMOUS_CHAT_PAGE} />;
}
