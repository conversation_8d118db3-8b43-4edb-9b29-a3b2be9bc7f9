import React, { useEffect } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { useAuth0 } from "./globals/auth0";

import "bulma/css/bulma.css";

import { webClientUrl } from "./globals/api";
import { Auth0Provider } from "./globals/auth0";
import { ReleaseProvider, useRelease } from "./globals/release";

import { Router } from "./router/Router";
import { GlobalConfigProvider, useGlobalConfig } from "./globals/global-config";

import { ActiveChatProvider } from "./pages/chat/data/active-chat";
import Header from "./components/Transcript/Header";

function initRun(rootElement: Element){
  const root = createRoot(rootElement);

  root.render(
    <GlobalConfigProvider>
     <Auth0Provider>
      <ReleaseProvider>
        <ActiveChatProvider>
          <App />
        </ActiveChatProvider>
      </ReleaseProvider>
     </Auth0Provider>
    </GlobalConfigProvider>
  );
}

function App(){
  const { isLoading: auth0Loading, error } = useAuth0();
  const { loading: releaseLoading, error: releaseError } = useRelease();
  const { config } = useGlobalConfig();

  useEffect(()=>{
    console.log("custom css attempt", config);
    if(config && config.customCss) {
      const url = new URL(config.customCss);
      const styleLink = document.createElement("link");
      styleLink.rel = "stylesheet";
      styleLink.href = url.href;
      document.head.appendChild(styleLink);
      console.log("custom css apply:", config.customCss, styleLink);
    }

  }, [config]);

  console.log("auth0Loading", auth0Loading, error);
  console.log("releaseLoading", releaseLoading);

  if(releaseError){
    const divinciUrl = webClientUrl("/").href;
    return (
      <div style={{ width: "100%", height: "100%" }} className="center-child">
        <div>
        <h1>Expecting a Release Id</h1>
        <p>Please go to <a target="_blank" href={divinciUrl}>{divinciUrl}</a> and find a release you'd like to embed</p>
        </div>
      </div>
    );
  }

  if(releaseLoading) {
    return <h1> ⏳ Please Wait... </h1>;
  }

  return (
    <BrowserRouter>
     <Header />
     <Router />
    </BrowserRouter>
  );
}

import { SILENT_LOGOUT_PARAM } from "./globals/auth0";
const selfUrl = new URL(window.location.href);
if(selfUrl.searchParams.get(SILENT_LOGOUT_PARAM) === "true") {
  window.parent.postMessage("silent-logout", "*");
} else {
  const root = document.querySelector("#root");
  if(!root) throw new Error("expecting a #root element");
  initRun(root);
}
