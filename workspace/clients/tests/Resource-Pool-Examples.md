# Resource Pool Examples

This document provides examples of using the resource pools in the hybrid StorageState + Resource Pooling architecture.

## Table of Contents

- [User Group Pool](#user-group-pool)
- [Whitelabel Pool](#whitelabel-pool)
- [Vector Pool](#vector-pool)
- [Combining Resource Pools](#combining-resource-pools)
- [Best Practices](#best-practices)

## User Group Pool

The user group pool provides a way to reuse user groups across tests.

### Basic Usage

```typescript
import { test, expect } from '@playwright/test';
import { getUserGroup, releaseUserGroup } from '../resources/user-group-pool';

test('should create a user group', async () => {
  // Get a user group from the pool
  const userGroup = await getUserGroup('admin');
  
  try {
    // Use the user group in the test
    // ...
    
    // Verify the user group properties
    expect(userGroup._id).toBeDefined();
    expect(userGroup.name).toBeDefined();
    expect(userGroup.slug).toBeDefined();
  } finally {
    // Release the user group back to the pool
    releaseUserGroup('admin');
  }
});
```

### Creating a User Group with a Specific Name

```typescript
import { test, expect } from '@playwright/test';
import { getUserGroup, releaseUserGroup } from '../resources/user-group-pool';

test('should create a user group with a specific name', async () => {
  // Get a user group from the pool with a specific name
  const userGroup = await getUserGroup('admin', 'Test Group');
  
  try {
    // Verify the user group properties
    expect(userGroup.name).toBe('Test Group');
  } finally {
    // Release the user group back to the pool
    releaseUserGroup('admin', 'Test Group');
  }
});
```

### Creating Multiple User Groups

```typescript
import { test, expect } from '@playwright/test';
import { getUserGroup, releaseUserGroup } from '../resources/user-group-pool';

test('should create multiple user groups', async () => {
  // Get two user groups from the pool
  const group1 = await getUserGroup('admin', 'Group 1');
  const group2 = await getUserGroup('admin', 'Group 2');
  
  try {
    // Verify that the groups are different
    expect(group1._id).not.toBe(group2._id);
    expect(group1.name).toBe('Group 1');
    expect(group2.name).toBe('Group 2');
  } finally {
    // Release the user groups back to the pool
    releaseUserGroup('admin', 'Group 1');
    releaseUserGroup('admin', 'Group 2');
  }
});
```

## Whitelabel Pool

The whitelabel pool provides a way to reuse whitelabels across tests.

### Basic Usage

```typescript
import { test, expect } from '@playwright/test';
import { getWhitelabel, releaseWhitelabel } from '../resources/whitelabel-pool';

test('should create a whitelabel', async () => {
  // Get a whitelabel from the pool
  const whitelabel = await getWhitelabel('admin');
  
  try {
    // Use the whitelabel in the test
    // ...
    
    // Verify the whitelabel properties
    expect(whitelabel._id).toBeDefined();
    expect(whitelabel.name).toBeDefined();
    expect(whitelabel.slug).toBeDefined();
    expect(whitelabel.groupId).toBeDefined();
  } finally {
    // Release the whitelabel back to the pool
    releaseWhitelabel('admin');
  }
});
```

### Creating a Whitelabel with a Specific Group

```typescript
import { test, expect } from '@playwright/test';
import { getUserGroup, releaseUserGroup } from '../resources/user-group-pool';
import { getWhitelabel, releaseWhitelabel } from '../resources/whitelabel-pool';

test('should create a whitelabel with a specific group', async () => {
  // Get a user group from the pool
  const userGroup = await getUserGroup('admin');
  
  try {
    // Get a whitelabel from the pool with the specific group
    const whitelabel = await getWhitelabel('admin', userGroup._id);
    
    try {
      // Verify the whitelabel properties
      expect(whitelabel.groupId).toBe(userGroup._id);
    } finally {
      // Release the whitelabel back to the pool
      releaseWhitelabel('admin', userGroup._id);
    }
  } finally {
    // Release the user group back to the pool
    releaseUserGroup('admin');
  }
});
```

### Creating Multiple Whitelabels in the Same Group

```typescript
import { test, expect } from '@playwright/test';
import { getUserGroup, releaseUserGroup } from '../resources/user-group-pool';
import { getWhitelabel, releaseWhitelabel } from '../resources/whitelabel-pool';

test('should create multiple whitelabels in the same group', async () => {
  // Get a user group from the pool
  const userGroup = await getUserGroup('admin');
  
  try {
    // Get two whitelabels from the pool with the same group
    const whitelabel1 = await getWhitelabel('admin', userGroup._id, 'Whitelabel 1');
    const whitelabel2 = await getWhitelabel('admin', userGroup._id, 'Whitelabel 2');
    
    try {
      // Verify that the whitelabels are different
      expect(whitelabel1._id).not.toBe(whitelabel2._id);
      expect(whitelabel1.name).toBe('Whitelabel 1');
      expect(whitelabel2.name).toBe('Whitelabel 2');
      expect(whitelabel1.groupId).toBe(userGroup._id);
      expect(whitelabel2.groupId).toBe(userGroup._id);
    } finally {
      // Release the whitelabels back to the pool
      releaseWhitelabel('admin', userGroup._id, 'Whitelabel 1');
      releaseWhitelabel('admin', userGroup._id, 'Whitelabel 2');
    }
  } finally {
    // Release the user group back to the pool
    releaseUserGroup('admin');
  }
});
```

## Vector Pool

The vector pool provides a way to reuse vectors across tests.

### Basic Usage

```typescript
import { test, expect } from '@playwright/test';
import { getVector, releaseVector } from '../resources/vector-pool';

test('should create a vector', async () => {
  // Get a vector from the pool
  const vector = await getVector('admin');
  
  try {
    // Use the vector in the test
    // ...
    
    // Verify the vector properties
    expect(vector._id).toBeDefined();
    expect(vector.name).toBeDefined();
    expect(vector.whitelabelId).toBeDefined();
  } finally {
    // Release the vector back to the pool
    releaseVector('admin');
  }
});
```

### Creating a Vector with a Specific Whitelabel

```typescript
import { test, expect } from '@playwright/test';
import { getWhitelabel, releaseWhitelabel } from '../resources/whitelabel-pool';
import { getVector, releaseVector } from '../resources/vector-pool';

test('should create a vector with a specific whitelabel', async () => {
  // Get a whitelabel from the pool
  const whitelabel = await getWhitelabel('admin');
  
  try {
    // Get a vector from the pool with the specific whitelabel
    const vector = await getVector('admin', whitelabel._id);
    
    try {
      // Verify the vector properties
      expect(vector.whitelabelId).toBe(whitelabel._id);
    } finally {
      // Release the vector back to the pool
      releaseVector('admin', whitelabel._id);
    }
  } finally {
    // Release the whitelabel back to the pool
    releaseWhitelabel('admin');
  }
});
```

## Combining Resource Pools

The resource pools can be combined to create complex test scenarios.

### Creating a Complete Test Environment

```typescript
import { test, expect } from '@playwright/test';
import { getUserGroup, releaseUserGroup } from '../resources/user-group-pool';
import { getWhitelabel, releaseWhitelabel } from '../resources/whitelabel-pool';
import { getVector, releaseVector } from '../resources/vector-pool';

test('should create a complete test environment', async () => {
  // Get a user group from the pool
  const userGroup = await getUserGroup('admin', 'Test Group');
  
  try {
    // Get a whitelabel from the pool with the specific group
    const whitelabel = await getWhitelabel('admin', userGroup._id, 'Test Whitelabel');
    
    try {
      // Get a vector from the pool with the specific whitelabel
      const vector = await getVector('admin', whitelabel._id, 'Test Vector');
      
      try {
        // Use the resources in the test
        // ...
        
        // Verify the resource properties
        expect(userGroup._id).toBeDefined();
        expect(whitelabel.groupId).toBe(userGroup._id);
        expect(vector.whitelabelId).toBe(whitelabel._id);
      } finally {
        // Release the vector back to the pool
        releaseVector('admin', whitelabel._id, 'Test Vector');
      }
    } finally {
      // Release the whitelabel back to the pool
      releaseWhitelabel('admin', userGroup._id, 'Test Whitelabel');
    }
  } finally {
    // Release the user group back to the pool
    releaseUserGroup('admin', 'Test Group');
  }
});
```

## Best Practices

1. **Always Release Resources**: Always release resources back to the pool when you're done with them, even if the test fails.
2. **Use Try/Finally Blocks**: Use try/finally blocks to ensure resources are released, even if an error occurs.
3. **Release in Reverse Order**: Release resources in the reverse order that you acquired them to avoid dependency issues.
4. **Use Specific Names**: Use specific names for resources to make it easier to identify them in logs and error messages.
5. **Reuse Resources**: Reuse resources across tests to reduce the number of API calls and avoid rate limits.
6. **Clean Up After Tests**: Clean up any test-specific data that isn't managed by the resource pools.
