# Divinci Testing Suite

This directory contains the testing suite for the <PERSON><PERSON><PERSON> application, including both UI End-to-End (E2E) tests and API tests. The tests are built using Playwright, a modern testing framework that enables reliable end-to-end testing for web applications.

## Table of Contents

- [Overview](#overview)
- [Test Architecture](#test-architecture)
- [Test Structure](#test-structure)
- [Authentication](#authentication)
  - [E2E Tests Authentication](#e2e-tests-authentication)
  - [API Tests Authentication](#api-tests-authentication)
- [Resource Pooling](#resource-pooling)
- [Running Tests](#running-tests)
- [Writing Tests](#writing-tests)
- [Test Utilities](#test-utilities)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Overview

The testing suite is divided into two main categories:

1. **UI End-to-End (E2E) Tests** - Located in `src/e2e/`
   - Test the application from a user's perspective
   - Interact with the UI elements
   - Verify workflows like login, creating whitelabels, uploading files, etc.

2. **API Tests** - Located in `src/tests/`
   - Test the API endpoints directly
   - Verify data manipulation and business logic
   - Include story tests that test specific user stories or scenarios

## Test Architecture

The testing suite uses a hybrid architecture that combines <PERSON><PERSON>'s `storageState` for authentication with resource pooling for efficient resource management. This approach has several benefits:

1. **Direct API Testing**: Tests hit the real API running in Docker Compose
2. **Efficient Resource Management**: Resources are reused to avoid hitting rate limits
3. **Reliable Authentication**: Playwright's `storageState` provides consistent auth handling
4. **Separation of Concerns**: Clear separation between auth, resource management, and test logic

For more details, see the [StorageState_Resource-Pooling-Architecture-for-Testing.md](./StorageState_Resource-Pooling-Architecture-for-Testing.md) document.

## Authentication

### E2E Tests Authentication

E2E tests use Playwright's `storageState` feature to handle authentication. This approach has several benefits:

1. Tests don't need to log in for each test run, making them faster and more reliable
2. Auth0 authentication is isolated to a single setup file (`auth.setup.ts`)
3. The authentication state is saved to files (`auth.json`, `auth/admin.json`, etc.) and reused across tests

#### How it works

1. The `auth.setup.ts` file is responsible for creating the auth state files with the authenticated session
2. This file is run automatically before other tests through the Playwright configuration
3. All tests use the appropriate `storageState` based on the user role they need

#### Running the auth setup manually

If you need to regenerate the auth state files manually, you can run:

```bash
npx playwright test src/auth/auth.setup.ts
npx playwright test src/auth/api-auth.setup.ts
```

### API Tests Authentication

API tests also use Playwright's `storageState` for authentication, but with a focus on API interactions. This approach has several benefits:

1. Consistent authentication across all tests
2. No need to handle authentication in each test
3. Easy to switch between different user roles

#### How it works

1. The `api-auth.setup.ts` file is responsible for creating the auth state files for API tests
2. The `ApiClient` class uses these auth state files to authenticate API requests
3. Different user roles (admin, user, owner) are supported through different auth state files

Example of using the API client in tests:

```typescript
import { test, expect } from '@playwright/test';
import { ApiClient } from '../api/api-client';
import { getAuthFileForRole } from '../auth/auth-utils';

test('should get user profile', async () => {
  // Create an API client with admin auth
  const client = new ApiClient(undefined, getAuthFileForRole('admin'));

  // Make an authenticated request
  const profile = await client.get('user/profile');

  // Verify the response
  expect(profile.email).toBeDefined();
});
```

## Resource Pooling

The testing suite uses resource pooling to efficiently manage test resources like user groups, whitelabels, etc. This approach has several benefits:

1. Resources are reused across tests, reducing the number of API calls
2. Tests run faster because they don't need to create new resources for each test
3. Resource cleanup is handled automatically
4. Tests are more reliable because they don't hit rate limits

### How it works

1. The `resource-pool.ts` module provides a generic resource pool implementation
2. Resource-specific pools (e.g., `user-group-pool.ts`) extend this implementation
3. Tests get resources from the pool and release them when done
4. Resources are automatically cleaned up when they're no longer needed

Example of using the resource pool in tests:

```typescript
import { test, expect } from '@playwright/test';
import { getUserGroup, releaseUserGroup } from '../resources/user-group-pool';

test('should create a whitelabel in a user group', async () => {
  // Get a user group from the pool
  const userGroup = await getUserGroup('admin');

  try {
    // Use the user group in the test
    // ...

    // Verify the results
    // ...
  } finally {
    // Release the user group back to the pool
    releaseUserGroup('admin');
  }
});
```

## Test Structure

The testing suite is organized as follows:

```
workspace/clients/tests/
├── auth/                     # Auth state files
│   ├── admin.json
│   ├── user.json
│   └── owner.json
├── auth.json                 # E2E auth state file
├── playwright.config.ts      # Playwright configuration
├── src/
│   ├── auth/                 # Authentication layer
│   │   ├── auth.setup.ts     # E2E auth setup
│   │   ├── api-auth.setup.ts # API auth setup
│   │   └── auth-utils.ts     # Auth utilities
│   ├── config/               # Configuration
│   │   ├── config.ts         # Configuration system
│   │   └── env.ts            # Environment-specific config
│   ├── api/                  # API clients
│   │   ├── api-client.ts     # Base API client
│   │   ├── user-group-api.ts # User group API client
│   │   └── ...
│   ├── resources/            # Resource management
│   │   ├── resource-pool.ts  # Generic resource pool
│   │   ├── user-group-pool.ts # User group pool
│   │   └── ...
│   ├── utils/                # Utilities
│   │   ├── test-utils.ts     # Test utilities
│   │   └── ...
│   ├── e2e/                  # E2E tests
│   │   └── ...
│   └── tests/                # API tests
│       └── ...
└── test-files/               # Files used for testing uploads
    ├── raw-rag-example.pdf
    └── ...
```

### E2E Tests

The E2E tests in `src/e2e/` test the application from a user's perspective:

- `login.spec.ts` - Tests the Auth0 login flow (doesn't use `storageState`)
- `rag-vector-create.spec.ts` - Tests creating a RAG vector
- `rag-vector-upload-file.spec.ts` - Tests uploading a file to a RAG vector
- `rag-vector-upload-file-status.spec.ts` - Tests the file upload status
- `audio-transcript-*.spec.ts` - Tests for audio transcription features
- `create-whitelabel.spec.ts` - Tests creating a whitelabel

### API Tests

The API tests in `src/` (excluding `src/e2e/`) test the API endpoints directly:

- `story-test/` - Story-based API tests that test specific user stories or scenarios
- `tests/` - Other API tests that test specific API endpoints or functionality

## Running Tests

### Running all tests

```bash
npx playwright test
```

### Running E2E tests only

```bash
npx playwright test src/e2e/
```

### Running API tests only

```bash
npx playwright test --grep-invert "src/e2e/"
```

### Running a specific test

```bash
npx playwright test src/e2e/rag-vector-upload-file.spec.ts
```

### Running tests with UI

```bash
npx playwright test --ui
```

### Running tests with debugging

```bash
npx playwright test --debug
```

### Running tests with specific project

```bash
npx playwright test --project=chromium
```

## Test Utilities

### E2E Test Utilities

The `src/e2e/utils` directory contains utility functions for E2E tests:

- `common.ts` - Common utility functions for navigation, verification, etc.
- `test-auth.ts` - Authentication utilities (only used for testing auth bypass scenarios)

### API Test Utilities

The following utilities are available for API tests:

- `src/globals/auth0/mock-auth.ts` - Mock authentication functions for API tests
- `src/util/storage-state-resource-pool.ts` - Resource pool for reusable resources
- `src/story-test/util/group/storage-state-actions.ts` - Functions for creating and managing user groups

## Test Files

The `test-files` directory contains files used for testing uploads, such as PDFs and audio files.

## Writing Tests

### Writing E2E Tests

E2E tests should be placed in the `src/e2e/` directory and follow these guidelines:

1. Use the appropriate `storageState` for authentication
2. Use page objects or utility functions for common operations
3. Use descriptive test names and comments
4. Include proper error handling and timeouts

Example:

```typescript
import { test, expect } from '@playwright/test';

test('should create a new whitelabel', async ({ page }) => {
  // Navigate to the whitelabel page
  await page.goto('http://localhost:8080/white-label');
  await page.waitForLoadState('networkidle');

  // Click the create button
  await page.click('button:has-text("Create")');

  // Fill in the form
  await page.fill('input[name="title"]', 'Test Whitelabel');
  await page.fill('textarea[name="description"]', 'This is a test whitelabel');

  // Submit the form
  await page.click('button:has-text("Create")');

  // Verify that the whitelabel was created
  await expect(page.locator('.notification.is-success')).toBeVisible();
});
```

### Writing API Tests

API tests should be placed in the `src/tests/` directory and follow these guidelines:

1. Use the `ApiClient` class for making API requests
2. Use resource pools for managing test resources
3. Use descriptive test names and comments
4. Include proper error handling and assertions
5. Always release resources back to the pool when done

Example:

```typescript
import { test, expect } from '@playwright/test';
import { getUserGroup, releaseUserGroup } from '../resources/user-group-pool';
import { createWhitelabel } from '../api/whitelabel-api';
import { getAuthFileForRole } from '../auth/auth-utils';

test('should create a whitelabel in a user group', async () => {
  // Get a user group from the pool
  const userGroup = await getUserGroup('admin');

  try {
    // Create a whitelabel in the user group
    const whitelabel = await createWhitelabel(
      'admin',
      {
        name: 'Test Whitelabel',
        groupId: userGroup._id
      }
    );

    // Verify the whitelabel
    expect(whitelabel.name).toBe('Test Whitelabel');
    expect(whitelabel.groupId).toBe(userGroup._id);
  } finally {
    // Release the user group back to the pool
    releaseUserGroup('admin');
  }
});
```

## Best Practices

1. **Use storageState for authentication** - Avoid logging in for each test
2. **Use resource pools for test resources** - Avoid creating new resources for each test
3. **Always release resources back to the pool** - Use try/finally blocks
4. **Use the ApiClient class for API requests** - Avoid direct fetch calls
5. **Include proper error handling** - Make tests robust and reliable
6. **Use descriptive test names and comments** - Make tests easy to understand
7. **Keep tests independent** - Tests should not depend on each other
8. **Clean up after tests** - Remove any test data created during tests

## Troubleshooting

### Common Issues

1. **Authentication failures**
   - Check if the auth state files exist and are valid
   - Regenerate the auth state files using the auth setup tests
   - Verify that the Auth0 credentials are correct

2. **Resource pool issues**
   - Check if resources are being released back to the pool
   - Increase the pool size if needed
   - Clear the pool if it gets corrupted

3. **API errors**
   - Check if the API is running and accessible
   - Verify that the API client is configured correctly
   - Check if the auth state files are valid

4. **Selector errors**
   - Check if the selectors are correct and unique
   - Use more specific selectors or add data-testid attributes to elements

5. **Timeouts**
   - Increase the timeout in the test or in the Playwright configuration
   - Check if the application is running and accessible

### Getting Help

If you encounter any issues or have questions, please contact the testing team or refer to the documentation:

- [StorageState_Resource-Pooling-Architecture-for-Testing.md](./StorageState_Resource-Pooling-Architecture-for-Testing.md)
- [Playwright Documentation](https://playwright.dev/docs/intro)
- [Playwright API Reference](https://playwright.dev/docs/api/class-playwright)
