import { createWriteStream, mkdirSync, existsSync } from 'fs';
import { join as pathJoin } from 'path';

import INIT_TEST from 'tap';
type TAP = typeof INIT_TEST;

import { TapError } from './types';

export function setupTestReporting(initTest: TAP) {
  // Create reports directory ifit doesn't exist
  const reportsDir = pathJoin(__dirname, '../reports');
  if(!existsSync(reportsDir)) {
    mkdirSync(reportsDir, { recursive: true });
  }

  // Configure tap
  initTest.jobs = 1;

  // File outputs
  // const tapStream = createWriteStream(pathJoin(reportsDir, `test-report-${Date.now()}.tap`));
  // initTest.pipe(tapStream);



  // Generate summary when tests complete
  initTest.on('end', () => {
    if(initTest.errors.length > 0) {
      console.log('\n❌ Test Failures:');
      console.log('====================');
      initTest.errors.forEach((error, index) => {
        console.log(formatTestFailure(error, index));
      });
    }

    const summary = {
      total: initTest.count,
      passed: initTest.passing(),
      failed: initTest.errors.length,
      skipped: initTest.counts.skip || 0,
      todo: initTest.counts.todo || 0,
      time: initTest.time,
      results: initTest.results,
      rawPassing: initTest.passing(), // Add raw passing value
      errorCount: initTest.errors.length // Add error count
    };

    // Add more detailed logging
    console.log('\n🔍 Test Execution Details:');
    console.log('====================');
    const results = initTest.results; // Type assertion since we know the structure

    if(!results){
      return console.log('No test results available');
    }

    console.log(`Total Tests: ${results.count ?? 0}`);
    console.log(`Passed: ${results.pass ?? 0}`);
    console.log(`Failed: ${results.fail ?? 0}`);
    console.log(`Duration: ${results.time ?? 0}ms\n`);

    if(!results.failures) return;
    if(results.failures.length === 0) return;

    console.log('❌ Failed Tests:');
    console.log('==============');
    for(let index = 0; index < results.failures.length; index++) {
      const failure = results.failures[index];
      console.log(`\n${index + 1}. ${failure.name || 'Unnamed Test'}`);
      if(failure.diag) {
        if(failure.diag.message) {
          console.log(`   Message: ${failure.diag.message}`);
        }
        if(failure.diag.error) {
          console.log(`   Error: ${failure.diag.error}`);
        }
        if(failure.diag.expected !== undefined) {
          console.log(`   Expected: ${JSON.stringify(failure.diag.expected)}`);
          console.log(`   Actual: ${JSON.stringify(failure.diag.actual)}`);
        }
        if(failure.diag.at) {
          console.log(`   Location: ${failure.diag.at.fileName}:${failure.diag.at.lineNumber}`);
        }
      }
      if(failure.time) {
        console.log(`   Duration: ${failure.time}ms`);
      }
    }
  });
}

function formatTestFailure(error: TapError, index: number): string {
  const details = [
    `\n❌ Failure #${index + 1}:`,
    `Test Name: ${error.name || 'Unnamed Test'}`,
    `File: ${error.diag?.at?.fileName || 'Unknown file'}`,
    `Line: ${error.diag?.at?.lineNumber || 'Unknown line'}`,
  ];

  if(error.diag?.error) {
    details.push(`Error: ${error.diag.error}`);
  }

  if(error.diag?.stack) {
    details.push(`Stack Trace:\n${error.diag.stack}`);
  }

  return details.join('\n');
}

