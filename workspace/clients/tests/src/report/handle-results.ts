import { TapTestResult } from "./types";

export function handleTestResults(results: Array<TapTestResult>){
  // Check for test failures
  const testFailures: Array<NonNullable<TapTestResult>> = [];
  for(const result of results) {
    if(result === null) continue;
    if(!("failures" in result)) continue;
    if(!Array.isArray(result.failures)) continue;
    if(result.failures.length === 0) continue;
    testFailures.push(result);
  }

  if(testFailures.length === 0) return;

  console.log('\n❌ Test Suite Failures Detected:');
  console.log('==============================');
  for(let index = 0; index < testFailures.length; index++) {
    const suite = testFailures[index];
    console.log(`\nSuite ${index + 1}: ${suite.failures[0]?.fullname || 'Unknown Suite'}`);
    for(const failure of suite.failures) {
      console.log(`\n  Failed Test: ${failure.name}`);
      if(!failure.diag) continue;
      if(failure.diag.message) {
        console.log(`  Message: ${failure.diag.message}`);
      }
      if(failure.diag.error) {
        console.log(`  Error: ${failure.diag.error}`);
      }
      if(failure.diag.expected !== undefined) {
        console.log(`  Expected: ${JSON.stringify(failure.diag.expected)}`);
        console.log(`  Actual: ${JSON.stringify(failure.diag.actual)}`);
      }
      console.log(`  Location: ${failure.diag.at?.fileName}:${failure.diag.at?.lineNumber}`);
    }
  }
  throw new Error(`${testFailures.length} test suites contained failures`);
}
