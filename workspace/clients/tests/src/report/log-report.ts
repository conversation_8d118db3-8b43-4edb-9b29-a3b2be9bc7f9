import INIT_TEST from 'tap';
type TAP = typeof INIT_TEST;


export async function logTestReport(initTest: TAP){
  // Determine test status
  const testResults = {
    total: initTest.count,
    passed: initTest.passing(),
    failed: initTest.errors.length,
    hasErrors: initTest.errors.length > 0,
    allTestsPassing: initTest.passing() && initTest.errors.length === 0
  };

  console.log('\n📊 Final Test Results:');
  console.log('====================');
  console.log(`Total Tests Run: ${testResults.total}`);
  console.log(`Tests Passed: ${testResults.passed}`);
  // console.log(`Tests Failed: ${testResults.failed}`);
  console.log(`All Tests Passing: ${testResults.allTestsPassing}`);

  if(!testResults.allTestsPassing) {
    console.log('\n❌ Test Failures:');
    initTest.errors.forEach((error: any, index) => {
      console.log(`\nFailure ${index + 1}:`);
      console.log(`Name: ${error.name || 'Unnamed Test'}`);
      // Use error.diag for the error message
      console.log(`Error: ${error.diag?.error || 'No error message provided'}`);

      if(error.diag) {
        console.log('Diagnostics:');
        console.log(JSON.stringify(error.diag, null, 2));
      }

      // Use error.diag.stack for stack trace if available
      if(error.diag?.stack) {
        console.log('Stack Trace:');
        console.log(error.diag.stack);
      }
    });
  }

  // Determine exit code
  const exitCode = testResults.allTestsPassing ? 0 : 1;
  console.log(`\n${exitCode === 0 ? '✅' : '❌'} Tests ${exitCode === 0 ? 'passed' : 'failed'} - Exit code: ${exitCode}`);

  // Ensure all streams are flushed before exit
  const streams = [process.stdout, process.stderr];
  const flushPromises = streams.map(stream =>
    new Promise(resolve => {
      // If stream can't be flushed within 1 second, resolve anyway
      const timeout = setTimeout(resolve, 1000);
      stream.write('', () => {
        clearTimeout(timeout);
        resolve(true);
      });
    })
  );

  await Promise.all(flushPromises);

  return exitCode;
}