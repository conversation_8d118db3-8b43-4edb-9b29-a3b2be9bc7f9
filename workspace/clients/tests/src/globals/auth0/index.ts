

import { browserService } from "../browser";
import { testServer } from "../test-server";

import { getUserToJWT, makeAuth0Fetch } from "./client";

import { readFileSync } from "fs";
import { resolve as pathResolve } from "path";

const html = readFileSync(pathResolve(__dirname, "./index.html"), "utf-8");

export type AuthFetcher = typeof makeAuth0Fetch;

import { router } from "./router";
export const config = { id: "auth0", html, router };
export * from "./client";
export * from "./types";
export * from "./storage-state";
export * from "./mock-auth";
import { UserConfig } from "./types";
export async function authFetch({ username, password }: UserConfig){
  const browser = await browserService.use();
  await testServer.use();

  const jwt = await getUserToJWT(browser, { username, password });

  const fetcher = makeAuth0Fetch(jwt, { username, password });

  browserService.release();
  testServer.release();

  return fetcher;
}
