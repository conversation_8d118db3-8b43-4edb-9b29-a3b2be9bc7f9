/**
 * Mock Auth0 Authentication
 * 
 * This module provides mock authentication functions for tests.
 * It creates fake JWT tokens and user IDs for testing purposes.
 */

import { AuthFetch } from './types';
import { API_ENDPOINT_ORIGIN } from '../../constants/internal';
import { DIVINCI_ORGANIZATION_HEADER } from '@divinci-ai/models';
import { throttledFetch } from '../../util/request-throttler';

// User configurations
const adminUser = { username: '<EMAIL>', password: '(abc123ABC)' };
const regularUser = { username: '<EMAIL>', password: '(abc123ABC)' };
const ownerUser = { username: '<EMAIL>', password: '(abc123ABC)' };

// Create a mock JWT token
function createMockJwt(email: string): string {
  // This is a fake JWT token for testing purposes only
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    sub: `auth0|${Buffer.from(email).toString('hex')}`,
    email,
    name: email.split('@')[0],
    exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
  }));
  const signature = btoa('fake_signature');
  
  return `Bearer ${header}.${payload}.${signature}`;
}

// Create a mock user ID
function createMockUserId(email: string): string {
  return `auth0|${Buffer.from(email).toString('hex')}`;
}

/**
 * Get a mock authenticated fetch function for the admin user
 * @returns Mock authenticated fetch function
 */
export function getMockAdminFetch(): AuthFetch {
  return createMockAuthFetch(adminUser.username);
}

/**
 * Get a mock authenticated fetch function for a regular user
 * @returns Mock authenticated fetch function
 */
export function getMockUserFetch(): AuthFetch {
  return createMockAuthFetch(regularUser.username);
}

/**
 * Get a mock authenticated fetch function for an owner user
 * @returns Mock authenticated fetch function
 */
export function getMockOwnerFetch(): AuthFetch {
  return createMockAuthFetch(ownerUser.username);
}

/**
 * Create a mock authenticated fetch function
 * @param email User email
 * @returns Mock authenticated fetch function
 */
function createMockAuthFetch(email: string): AuthFetch {
  const jwt = createMockJwt(email);
  const userId = createMockUserId(email);
  
  const authFetchUncasted = function(...args: Parameters<typeof fetch>) {
    const url = new URL(
      (args[0] instanceof Request) ? args[0].url : args[0] as string,
      API_ENDPOINT_ORIGIN
    );
    const init = args[1] || {};
    
    const headers = new Headers(init.headers);
    headers.set('Authorization', jwt);
    if (authFetch.groupId) {
      headers.set(DIVINCI_ORGANIZATION_HEADER, authFetch.groupId);
    }
    init.headers = headers;
    
    if (!init.signal) {
      init.signal = AbortSignal.timeout(30 * 1000);
    }
    
    // Use throttled fetch to avoid rate limiting
    return throttledFetch(url.toString(), init);
  };
  
  const authFetch = authFetchUncasted as AuthFetch;
  
  authFetch.userId = userId;
  authFetch.email = email;
  authFetch.username = email;
  authFetch.password = '(abc123ABC)';
  
  return authFetch;
}

/**
 * Get the appropriate mock fetch function based on the user role
 * @param role User role (admin, user, owner)
 * @returns Mock authenticated fetch function
 */
export function getMockFetchByRole(role: 'admin' | 'user' | 'owner'): AuthFetch {
  switch (role) {
    case 'admin':
      return getMockAdminFetch();
    case 'user':
      return getMockUserFetch();
    case 'owner':
      return getMockOwnerFetch();
    default:
      return getMockUserFetch();
  }
}
