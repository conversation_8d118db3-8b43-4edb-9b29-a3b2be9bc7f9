/**
 * Auth0 Storage State
 * 
 * This module provides utilities for working with <PERSON><PERSON>'s storage state
 * to authenticate requests without using the connection pool mechanism.
 */

import { API_ENDPOINT_ORIGIN } from '../../constants/internal';
import { DIVINCI_ORGANIZATION_HEADER } from '@divinci-ai/models';
import { AuthFetch, JWTConfig, UserConfig } from './types';
import { throttledFetch } from '../../util/request-throttler';
import path from 'path';
import fs from 'fs';

// Define the paths to the storage state files
const authDir = path.join(__dirname, '../../../auth');
const adminAuthFile = path.join(authDir, 'admin.json');
const userAuthFile = path.join(authDir, 'user.json');
const ownerAuthFile = path.join(authDir, 'owner.json');

// User configurations
const adminUser = { username: '<EMAIL>', password: '(abc123ABC)' };
const regularUser = { username: '<EMAIL>', password: '(abc123ABC)' };
const ownerUser = { username: '<EMAIL>', password: '(abc123ABC)' };

// Cache for JWT tokens extracted from storage state files
const jwtCache = new Map<string, { jwt: string; userId: string; expiresAt: number }>();

/**
 * Extract JWT and user ID from a storage state file
 * @param storageStatePath Path to the storage state file
 * @returns JWT and user ID
 */
function extractJwtFromStorageState(storageStatePath: string): { jwt: string; userId: string } | null {
  try {
    // Check if the file exists
    if (!fs.existsSync(storageStatePath)) {
      console.error(`Storage state file not found: ${storageStatePath}`);
      return null;
    }

    // Check if we have a cached JWT for this file
    const cachedJwt = jwtCache.get(storageStatePath);
    if (cachedJwt && cachedJwt.expiresAt > Date.now()) {
      return {
        jwt: cachedJwt.jwt,
        userId: cachedJwt.userId
      };
    }

    // Read the storage state file
    const storageState = JSON.parse(fs.readFileSync(storageStatePath, 'utf8'));

    // Extract the JWT from localStorage
    let jwt = '';
    let userId = '';

    // Find the JWT in localStorage
    if (storageState.origins && storageState.origins.length > 0) {
      for (const origin of storageState.origins) {
        if (origin.localStorage) {
          for (const item of origin.localStorage) {
            if (item.name === 'auth0.is.authenticated' && item.value === 'true') {
              // Found authenticated state, now look for the JWT
              for (const tokenItem of origin.localStorage) {
                if (tokenItem.name.startsWith('@@auth0spajs@@')) {
                  try {
                    const tokenData = JSON.parse(tokenItem.value);
                    if (tokenData.access_token) {
                      jwt = `Bearer ${tokenData.access_token}`;
                    }
                  } catch (e) {
                    console.error('Error parsing token data:', e);
                  }
                }
              }

              // Look for user ID
              for (const userItem of origin.localStorage) {
                if (userItem.name === 'auth0.user') {
                  try {
                    const userData = JSON.parse(userItem.value);
                    if (userData.sub) {
                      userId = userData.sub;
                    }
                  } catch (e) {
                    console.error('Error parsing user data:', e);
                  }
                }
              }
            }
          }
        }
      }
    }

    if (!jwt || !userId) {
      console.error('Failed to extract JWT or user ID from storage state');
      return null;
    }

    // Cache the JWT with a 1-hour expiration
    jwtCache.set(storageStatePath, {
      jwt,
      userId,
      expiresAt: Date.now() + 60 * 60 * 1000 // 1 hour
    });

    return { jwt, userId };
  } catch (error) {
    console.error('Error extracting JWT from storage state:', error);
    return null;
  }
}

/**
 * Get an authenticated fetch function for the admin user
 * @returns Authenticated fetch function
 */
export function getAdminFetch(): AuthFetch | null {
  const jwtConfig = extractJwtFromStorageState(adminAuthFile);
  if (!jwtConfig) return null;
  return makeStorageStateFetch(jwtConfig, adminUser);
}

/**
 * Get an authenticated fetch function for a regular user
 * @returns Authenticated fetch function
 */
export function getUserFetch(): AuthFetch | null {
  const jwtConfig = extractJwtFromStorageState(userAuthFile);
  if (!jwtConfig) return null;
  return makeStorageStateFetch(jwtConfig, regularUser);
}

/**
 * Get an authenticated fetch function for an owner user
 * @returns Authenticated fetch function
 */
export function getOwnerFetch(): AuthFetch | null {
  const jwtConfig = extractJwtFromStorageState(ownerAuthFile);
  if (!jwtConfig) return null;
  return makeStorageStateFetch(jwtConfig, ownerUser);
}

/**
 * Create an authenticated fetch function using JWT from storage state
 * @param jwtConfig JWT configuration
 * @param userConfig User configuration
 * @returns Authenticated fetch function
 */
function makeStorageStateFetch(
  { jwt, userId }: JWTConfig,
  { username, password }: UserConfig
): AuthFetch {
  // Ensure JWT is properly formatted with "Bearer" prefix
  const authHeader = jwt.startsWith('Bearer ') ? jwt : `Bearer ${jwt}`;

  const authFetchUncasted = function (...args: Parameters<typeof fetch>) {
    const url = new URL(
      (args[0] instanceof Request) ? args[0].url : args[0] as string,
      API_ENDPOINT_ORIGIN
    );
    const init = args[1] || {};

    const headers = new Headers(init.headers);
    headers.set('Authorization', authHeader);
    if (authFetch.groupId) {
      headers.set(DIVINCI_ORGANIZATION_HEADER, authFetch.groupId);
    }
    init.headers = headers;

    if (!init.signal) {
      init.signal = AbortSignal.timeout(30 * 1000);
    }

    // Use throttled fetch to avoid rate limiting
    return throttledFetch(url.toString(), init);
  };

  const authFetch = authFetchUncasted as AuthFetch;

  authFetch.userId = userId;
  authFetch.email = username;
  authFetch.username = username;
  authFetch.password = password;

  return authFetch;
}

/**
 * Check if the storage state files exist
 * @returns True if all storage state files exist
 */
export function checkStorageStateFiles(): boolean {
  return (
    fs.existsSync(adminAuthFile) &&
    fs.existsSync(userAuthFile) &&
    fs.existsSync(ownerAuthFile)
  );
}

/**
 * Get the appropriate fetch function based on the user role
 * @param role User role (admin, user, owner)
 * @returns Authenticated fetch function
 */
export function getFetchByRole(role: 'admin' | 'user' | 'owner'): AuthFetch | null {
  switch (role) {
    case 'admin':
      return getAdminFetch();
    case 'user':
      return getUserFetch();
    case 'owner':
      return getOwnerFetch();
    default:
      return null;
  }
}
