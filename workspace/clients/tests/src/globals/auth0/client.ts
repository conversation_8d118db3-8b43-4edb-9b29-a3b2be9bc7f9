import * as fs from "fs";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "playwright";

import { TEST_ENV, TEST_SERVER_ENDPOINT_ORIGIN, API_ENDPOINT_ORIGIN } from "../../constants/internal";

import { delay } from "@divinci-ai/utils";
import { UserConfig, <PERSON>th<PERSON><PERSON><PERSON>, JWTConfig } from "./types";
import { saveScreenshot } from "../screenshot";
import { tokenCache } from "./token-cache";

export async function usePageToLoginAsUser(page: Page, { username, password }: UserConfig){

  // 🌐 Navigating to test server endpoint...
  await page.goto(`${TEST_SERVER_ENDPOINT_ORIGIN}`);
  // 📍 Current URL: ${page.url()}

  // 🔍 Looking for login button...

  const loginButton = page.locator("#auth0-login");

  // Add visibility check
  // ⏳ Waiting for auth0-login button to be visible...
  try {
    await loginButton.waitFor({ state: "visible", timeout: 10000 });
    // ✅ Auth0 login button is visible
  }catch(error){
    console.error("❌ Auth0 login button not found or not visible");

    // Get page content and show first 555 characters
    /*
    const content = await page.content();
    const preview = content.slice(0, 555) + (content.length > 555 ? "..." : "");
    console.log("📄 Page content preview:", preview);
    */

    // Create playwright-report directory ifit doesn"t exist
    await fs.promises.mkdir("playwright-report", { recursive: true });

    // Save screenshot with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    await page.screenshot({
      path: `playwright-report/auth0-login-error-${timestamp}.png`,
      fullPage: true
    });
    throw error;
  }

  // 🖱️ Clicking auth0-login button...
  await loginButton.click();

  // ⌛ Waiting for username field...
  await page.locator("#username").waitFor({ state: "visible" });

  // ✍️ Filling in username: ${username}
  await page.locator("#username").fill(username);

  // ⌛ Waiting for password field...
  await page.locator("#password").waitFor({ state: "visible" });
  // 🔑 Filling in password: ${password}
  await page.locator("#password").fill(password);

  // 👆 Clicking continue button...
  await page.getByRole("button", { name: "Continue", exact: true }).click();

  const currentURL = new URL(page.url());
  if(currentURL.host === AUTH0_CLIENT_DOMAIN && currentURL.pathname === `/u/consent`) {
    // 🤝 Handling consent page...
    await page.locator("button[type=submit][value=accept]").click();
  }

  // 🔄 Waiting for user info...
  return waitForUserInfo(page);
}

export async function waitForUserInfo(page: Page){
  // Get the JWT
  let jwt = null;
  let userId = null;
  let times = 0;
  const maxAttempts = 20;

  try {
    do {
      await delay(555);
      // 🔄 Attempt ${times + 1}/${maxAttempts} to get user info...
      jwt = await page.locator("#auth0-jwt").textContent();
      userId = await page.locator("#auth0-userId").textContent();

      if(jwt && userId) {
        // ✅ Successfully retrieved user info!
        break;
      }

      times++;
      await new Promise((res)=>(setTimeout(res, 555)));
    } while(times < maxAttempts);

    if(!jwt || !userId) {

      await saveScreenshot(page, "auth0-jwt-timeout");

      // Log current page state

      if(!jwt) throw new Error("📛 Did not retrieve jwt.");
      if(!userId) throw new Error("📛 Did not retrieve userID.");
    }

    return { jwt, userId };

  }catch(error){
    await saveScreenshot(page, "auth0-error");
    throw error;
  }
}

export async function getUserToJWT(browser: Browser, { username, password }: UserConfig): Promise<JWTConfig>{
  // Check if we have a cached token for this user
  const cachedToken = tokenCache.getToken(username);
  if (cachedToken) {
    console.log(`🔑 Using cached token for ${username}`);
    return cachedToken;
  }

  console.log(`🔑 No cached token found for ${username}, logging in...`);
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    const userInfo = await usePageToLoginAsUser(page, { username, password });

    // Cache the token for future use (default expiration: 1 hour)
    tokenCache.setToken(username, userInfo);
    console.log(`🔑 Token cached for ${username}`);

    return userInfo;
  } finally {
    await context.close();
  }
}

import { AUTH0_CLIENT_ID, AUTH0_CLIENT_SECRET, AUTH0_CLIENT_DOMAIN } from "../../constants/auth0";

export async function revokeRefreshToken(refreshToken: string){
  const response = await fetch(`https://${AUTH0_CLIENT_DOMAIN}/oauth/revoke`, {
    method: "POST",
    headers: {
      "content-type": "application/json"
    },
    body: JSON.stringify({
      "client_id": AUTH0_CLIENT_ID,
      "client_secret": AUTH0_CLIENT_SECRET,
      "token": refreshToken
    })
  });

  if(response.ok) return;
  const json = await response.json();
  if(!response.ok) throw json;
}


import { DIVINCI_ORGANIZATION_HEADER } from "@divinci-ai/models";
import { throttledFetch } from "../../util/request-throttler";

export function makeAuth0Fetch({ jwt, userId }: JWTConfig, { username, password }: UserConfig): AuthFetch{
  // Ensure JWT is properly formatted with "Bearer" prefix
  const authHeader = jwt.startsWith("Bearer ") ? jwt : `Bearer ${jwt}`;
  // 🔑 Creating Auth0 fetch with token:', authHeader.substring(0, 20) + '...

  const authFetchUncasted = function(...args: Parameters<typeof fetch>){
    const url = new URL((args[0] instanceof Request) ? args[0].url : args[0], API_ENDPOINT_ORIGIN);
    const init = args[1] || {};

    // Ensure HTTPS for api.dev.divinci.app
    // Convert url to string if it's a URL or Request object
    if(TEST_ENV === "local") url.protocol = "http:";
    else url.protocol = "https:";

    const headers = new Headers(init.headers);
    headers.set("Authorization", authHeader);
    if(authFetch.groupId){
      headers.set(DIVINCI_ORGANIZATION_HEADER, authFetch.groupId);
    }
    init.headers = headers;

    if(!init.signal){
      init.signal = AbortSignal.timeout(30 * 1000);
    }

    // Use throttled fetch to avoid rate limiting
    return throttledFetch(url.toString(), init);
  };

  const authFetch = authFetchUncasted as AuthFetch;

  authFetch.userId = userId;
  authFetch.email = username;
  authFetch.username = username;
  authFetch.password = password;

  return authFetch;
}
