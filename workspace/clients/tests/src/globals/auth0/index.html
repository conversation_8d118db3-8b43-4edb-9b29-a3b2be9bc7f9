<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
  </head>

  <body>
    <script src="https://cdn.auth0.com/js/auth0-spa-js/2.0/auth0-spa-js.production.js"></script>
    <script>
      let auth0Client = null;

      async function auth0login() {
        await auth0Client.loginWithRedirect({
          authorizationParams: {
            redirect_uri: window.location.origin
          }
        });
      };

      function auth0logout() {
        auth0Client.logout({
          logoutParams: {
            returnTo: window.location.origin
          }
        });
      };
    </script>
    <script>

      const authPromise = Promise.resolve().then(async ()=>{
        const response = await fetch(`${window.location.origin}/auth0/auth0-config.json`);
        const json = await response.json(); 
        if(!response.ok) throw json;

        console.log("Creating client");
        auth0Client = await auth0.createAuth0Client({ ...json, cacheLocation: 'localstorage', });

        console.log("Checking if Authenticated");
        await (async ()=>{
          const isAuthenticated = await auth0Client.isAuthenticated();
          if(isAuthenticated){
            return;
          }

          const query = window.location.search;
          if(!query.includes("code=") || !query.includes("state=")){
            return;
          }

          await auth0Client.handleRedirectCallback();
          window.history.replaceState({}, document.title, "/");
        })();

        console.log("Updating UI");
        await auth0updateUI();
      }).catch((e)=>{
        console.error(e);
        document.querySelector("#auth0-error").textContent = e.message;
        throw e;
      });

      async function auth0updateUI() {
        const isAuthenticated = await auth0Client.isAuthenticated();

        document.querySelector("#auth0-logout").disabled = !isAuthenticated;
        document.querySelector("#auth0-login").disabled = isAuthenticated;

        if(!isAuthenticated){
          return;
        }

        const [jwt, user] = await Promise.all([
          auth0Client.getTokenSilently(),
          auth0Client.getUser()
        ]);
        document.querySelector("#auth0-jwt").textContent = jwt;
        document.querySelector("#auth0-userId").textContent = user.sub;
        document.querySelector("#auth0-user").textContent = JSON.stringify(user, null, 2);
      };

      async function getAuthFetch(){
        await authPromise;
        const isAuthenticated = await auth0Client.isAuthenticated();
        if(!isAuthenticated){
          throw new Error("not authenticated");
        }
        const jwt = await auth0Client.getTokenSilently();
        const authHeader = `Bearer ${jwt}`;

        return function(url, config){
          config = config ? config : {};
          config.headers = new Headers(config.headers);
          config.headers.set("Authorization", authHeader);
          return fetch(url, config);
        }
      }

    </script>
    <h2>SPA Authentication Sample</h2>
    <p>Welcome to our page!</p>
    <pre id="auth0-error"></pre>
    <button id="auth0-login" disabled="true" onclick="auth0login()">Log in</button>
    <button id="auth0-logout" disabled="true" onclick="auth0logout()">Log out</button>
    <pre id="auth0-jwt"></pre>
    <pre id="auth0-userId"></pre>
    <pre id="auth0-user"></pre>
  </body>
</html>