/**
 * Auth0 Token Cache
 * 
 * This module provides a cache for Auth0 tokens to avoid making too many login requests
 * during test execution. This helps prevent "Too Many Requests" errors from Auth0.
 */

import { JWTConfig } from './types';

interface CachedToken extends JWTConfig {
  username: string;
  expiresAt: number; // Timestamp when token expires
}

class Auth0TokenCache {
  private tokens: Map<string, CachedToken> = new Map();
  private initialized = false;

  /**
   * Initialize the token cache
   */
  public init(): void {
    if (this.initialized) return;
    this.tokens.clear();
    this.initialized = true;
    console.log('🔐 Auth0 token cache initialized');
  }

  /**
   * Get a token from the cache
   * @param username The username to get the token for
   * @returns The cached token or null if not found or expired
   */
  public getToken(username: string): JWTConfig | null {
    if (!this.initialized) {
      this.init();
    }

    const cachedToken = this.tokens.get(username);
    if (!cachedToken) {
      return null;
    }

    // Check if token is expired (with 5 minute buffer)
    if (cachedToken.expiresAt < Date.now() + 5 * 60 * 1000) {
      this.tokens.delete(username);
      return null;
    }

    return {
      jwt: cachedToken.jwt,
      userId: cachedToken.userId
    };
  }

  /**
   * Store a token in the cache
   * @param username The username the token belongs to
   * @param token The token to store
   * @param expiresIn Token expiration time in seconds (default: 1 hour)
   */
  public setToken(username: string, token: JWTConfig, expiresIn: number = 3600): void {
    if (!this.initialized) {
      this.init();
    }

    this.tokens.set(username, {
      ...token,
      username,
      expiresAt: Date.now() + expiresIn * 1000
    });
  }

  /**
   * Clear the token cache
   */
  public clear(): void {
    this.tokens.clear();
  }

  /**
   * Get the number of cached tokens
   */
  public size(): number {
    return this.tokens.size;
  }
}

// Export a singleton instance
export const tokenCache = new Auth0TokenCache();
