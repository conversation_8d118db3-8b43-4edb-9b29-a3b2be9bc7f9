
import { Server } from "http";
import express, { Express, Router } from "express";
import { Service } from "../../util/service";
import { promisify } from "util";
import { errorToString } from "../../util/http";
import { WEB_TEST_CLIENT_PORT } from "../../constants/internal";

import { config as auth0Config } from "../auth0";
import { config as stripeSubscriptionConfig } from "../stripe-subscription";
import { config as stripePaymentMethodConfig } from "../stripe-payment-method";

import { optionalAuthMiddlewareHTTP, getUserIdOptional } from "./middleware";
import { finalizeHTML, fileToFragment } from "./build-html";

 const PARTS = [
  auth0Config,
  stripeSubscriptionConfig,
  stripePaymentMethodConfig
];

class TestServer extends Service<{ server: Server, port: string, app: Express }> {
  server: Server | undefined;

  constructor(){
    super("Test Server");
  }

  async create(){

    const { idList, router, htmlText } = buildTestServerParts(PARTS);

    const httpApp = express();

    const server = new Server();

    server.on("request", function(req, res){
      httpApp(req, res);
    });

    httpApp.use(optionalAuthMiddlewareHTTP);
    httpApp.get("/has-user", (req, res, next)=>{
      try {
        const user = getUserIdOptional(req);

        const hasUser = user === null ? false : true;

        res.statusCode = 200;
        res.json({ result: "ok", hasUser });
      }catch(e){
        res.statusCode = 500;
        res.json({ result: "error", error: errorToString(e) });
        next(e);
      }
    });

    httpApp.use(router);

    httpApp.get("/*", (_, res) => {
      res.statusCode = 200;
      res.setHeader("content-type", "text/html");
      res.end(htmlText);
    });

    await promisify(server.listen.bind(server, WEB_TEST_CLIENT_PORT))();

    console.log("serving:", idList, " on port ", WEB_TEST_CLIENT_PORT);

    return { server, port: WEB_TEST_CLIENT_PORT, app: httpApp };
  }
  async kill({ server }: { server: Server, port: string, app: Express }){
    await promisify(server.close.bind(server))();
  }
}

export const testServer = new TestServer();

function buildTestServerParts(parts: Array<{ id: string, html: string, router: Router }>){
  const foundIds = new Set<string>();

  const appRouter = Router();
  let combinedFragment = "";

  for(const { id, html, router } of parts){
    if(foundIds.has(id)){
      throw new Error(`duplicate fragment id ${id}`);
    }
    foundIds.add(id);
    combinedFragment += fileToFragment(id, html);
    appRouter.use("/" + id, router);
  }

  const htmlText = finalizeHTML(combinedFragment);

  return { idList: Array.from(foundIds), router: appRouter, htmlText };
}
