import { load as cheerioLoad } from "cheerio";

export function finalizeHTML(combinedFragment: string){
  return `<!DOCTYPE html><html><head><meta charset="UTF-8" /></head><body>${combinedFragment}</body></html>`;
}

export function fileToFragment(idName: string, htmlStr: string){
  const queryable = cheerioLoad(htmlStr);
  const body = queryable("body");
  const bodyStr = body.first().html();
  if(bodyStr === null){
    throw new Error("unable to find body of html doc");
  }
  return `<div id="${idName}" >${bodyStr}</div>`;
}

