import { AUTH0_LOGIN_ENDPOINT, AUTH0_AUDIENCE, } from "../../constants/auth0";

import { IncomingMessage } from "http";
import { auth, AuthResult } from "express-oauth2-jwt-bearer";

export const optionalAuthMiddlewareHTTP = auth({
  audience: AUTH0_AUDIENCE,
  issuerBaseURL: AUTH0_LOGIN_ENDPOINT,
  tokenSigningAlg: "RS256",
  authRequired: false
});

export function getUserIdOptional(reqUncasted: IncomingMessage){
  const req = reqUncasted as IncomingMessage & { auth: AuthResult };

  console.log('🔍 Checking auth request: ', {
    headers: req.headers,
    authPresent: !!req.auth,
    url: req.url
  });

  if(!req){
    console.error("🚫 Test Server: Request object is missing.");
    return null;
  }
  if(typeof req.auth === "undefined"){
    console.warn("🙅🏻‍♂️ Test Server: No auth in req:", {
      headers: req.headers,
      url: req.url
    });
    return null;
  }

  console.info("📛 Test Server: Auth payload:", req.auth?.payload);

  const user_id = req.auth?.payload.sub;
  if(typeof user_id === "undefined"){
    console.warn("🙅🏻‍♂️ Test Server: No subject in auth payload:", req.auth?.payload);
    return null;
  }
  return user_id;
}
