import { <PERSON><PERSON><PERSON> } from "playwright";

import { usePageToLoginAsUser } from "../auth0";

export async function addPaymentMethodInClient(browser: <PERSON><PERSON><PERSON>, { username, password }: { username: string, password: string }){
  const context = await browser.newContext();
  const page = await context.newPage();

  await usePageToLoginAsUser(page, { username, password });

  await page.locator("#stripe-payment-method-add").click();


  const maybeLoginButtons = await page.locator("button.LinkActionButton").all();
  const noLinkButton = maybeLoginButtons.find(async (button)=>{
    const content = await button.allTextContents();
    return content[0] === "Pay without Link";
  });

  if(typeof noLinkButton !== "undefined"){
    await noLinkButton.click();
  }


  // Within the Stripe Page
  await page.locator("#cardNumber").fill("4242" + "4242" + "4242" + "4242");
  const currentYear = (Number.parseInt(new Date().getFullYear().toString().substring(2)) + 4).toString();
  await page.locator("#cardExpiry").fill("01" + currentYear);
  await page.locator("#cardCvc").fill("123");
  await page.locator("#billingName").fill("Money Mcgee");
  await page.locator("#billingPostalCode").fill("20001");

  await page.locator("form.PaymentForm-form button[type=submit]").click();

}
