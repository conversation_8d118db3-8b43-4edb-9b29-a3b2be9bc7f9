import { readFileSync } from "fs";
import { resolve as pathResolve } from "path";

import { browserService } from "../browser";
import { testServer } from "../test-server";
import { addPaymentMethodInClient } from "./client";

const html = readFileSync(pathResolve(__dirname, './index.html'), "utf-8");

import { router } from "./router";
export const config = { id: "stripe-payment-method", html, router };

export async function addPaymentMethod({ username, password }: { username: string, password: string }){
  const browser = await browserService.use();
  await testServer.use();

  await addPaymentMethodInClient(browser, { username, password });

  browserService.release();
  testServer.release();
}