<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
  </head>
  <body>
    <script>
      async function stripePaymentMethodAdd() {
        try {
          const stripeAuthFetch = await getAuthFetch();
          const response = await stripeAuthFetch(`${window.location.origin}/stripe-payment-method/add`);
          const json = await response.json();
          if(!response.ok) throw json;
          window.location.href = json.stripeUrl;
        }catch(e){
          document.querySelector("#stripe-payment-method-error").textContent = e.message;
        }
      };

      async function stripePaymentMethodManage() {
        try {
          const stripeAuthFetch = await getAuthFetch();
          const response = await stripeAuthFetch(`${window.location.origin}/stripe-payment-method/manage`);
          const json = await response.json();
          if(!response.ok) throw json;
          window.location.href = json.stripeUrl;
        }catch(e){
          document.querySelector("#stripe-payment-method-error").textContent = e.message;
        }
      }

    </script>
 
    <h2>Stripe Payment Method Sample</h2>
    <pre id="stripe-payment-method-error"></pre>
    <button id="stripe-payment-method-add" onclick="stripePaymentMethodAdd()">Add</button>
    <button id="stripe-payment-method-manage" onclick="stripePaymentMethodManage()">Manage</button>
  </body>
</html>