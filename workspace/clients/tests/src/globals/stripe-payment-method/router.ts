import { Router } from "express";
import { getUserId } from "@divinci-ai/server-globals";

import { API_ENDPOINT_ORIGIN } from "../../constants/internal";
import { SubscriptionInfo } from "@divinci-ai/models";

import { relayAuthFetch } from "../../util/http";

export const router = Router();

router.get("/add", async (req, res, next)=>{
  try {
    getUserId(req);

    const json = await relayAuthFetch(req, `${API_ENDPOINT_ORIGIN}/money/withdrawable/payment-method/create`);

    res.statusCode = 200;
    res.json(json);
  }catch(e){
    next(e);
  }
});

router.get("/list", async (req, res, next)=>{
  try {
    getUserId(req);

    const json = await relayAuthFetch(req, `${API_ENDPOINT_ORIGIN}/money/withdrawable/payment-method/list`);

    res.statusCode = 200;
    res.json(json);
  }catch(e){
    next(e);
  }
});


router.get("/manage", async (req, res, next)=>{
  try {
    getUserId(req);

    const json = await relayAuthFetch(req, `${API_ENDPOINT_ORIGIN}/money/withdrawable/payment-method/manage`);

    res.statusCode = 200;
    res.json(json);
  }catch(e){
    next(e);
  }
});
