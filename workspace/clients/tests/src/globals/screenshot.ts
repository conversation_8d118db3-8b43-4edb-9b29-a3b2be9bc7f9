import * as fs from 'fs';
import { join as pathJoin } from 'path';
import { Page } from 'playwright';

const SCREENSHOT_DIR = 'test-screenshots';

export async function saveScreenshot(page: Page, prefix: string) {
  // Get the workspace root directory
  const workspaceRoot = process.cwd();
  const screenshotDir = pathJoin(workspaceRoot, SCREENSHOT_DIR);

  // Ensure directory exists
  await fs.promises.mkdir(screenshotDir, { recursive: true });

  // Generate timestamp and filename
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `${prefix}-${timestamp}.png`;
  const fullPath = pathJoin(screenshotDir, filename);

  // Save screenshot
  await page.screenshot({
    path: fullPath,
    fullPage: true
  });

  console.log(`📸 Screenshot saved to: ${fullPath}`);

  // Log directory contents for debugging
  console.log('📂 Screenshot directory contents:');
  const files = await fs.promises.readdir(screenshotDir);
  console.log(files);

  return fullPath;
}
