import { firefox, chromium, webkit, <PERSON><PERSON><PERSON> } from "playwright";
import { Service } from "../util/service";

class BrowserService extends Service<Browser> {
  async create() {
    const browsers = [
      {
        name: 'Firefox',
        launcher: firefox
      },
      {
        name: 'Chromium',
        launcher: chromium
      },
      // Try Brave specifically (on different OS paths)
      {
        name: 'Brave',
        launcher: chromium,
        executablePath: process.platform === 'win32'
          ? 'C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe'
          : process.platform === 'darwin'
            ? '/Applications/Brave Browser.app/Contents/MacOS/Brave Browser'
            : '/usr/bin/brave-browser'
      },
      {
        name: 'WebKit',
        launcher: webkit
      }
    ];

    for(const browser of browsers){
      try {
        const instance = await browser.launcher.launch({
          headless: true,
          ...(browser.executablePath && { executablePath: browser.executablePath })
        });
        console.log(`💫 Successfully launched ${browser.name}.`);
        return instance;
      }catch(error){
        console.log(`❌ Failed to launch ${browser.name}: ${(error as any).message}`);
      }
    }

    throw new Error('❌ No supported browsers could be launched.');
  }

  kill(browser: Browser) {
    return browser.close();
  }
}

export const browserService = new BrowserService("Playwright Browser");
