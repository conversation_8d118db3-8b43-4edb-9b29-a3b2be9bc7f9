import { exec as execCallback } from "child_process";
import { promisify } from "util";

import { delay } from "@divinci-ai/utils";

import { Service } from "../util/service";

const exec = promisify(execCallback);

export class DockerService extends Service<{ stdout: string; stderr: string }> {
  constructor(private dockerComposeConfig: string, dockerName: string) {
    super(dockerName);
  }
  async create() {
    console.log("🐳 Starting Docker cleanup...");
    await exec(`docker compose -f ${this.dockerComposeConfig} down -v`);
    
    console.log("🚀 Starting Docker containers...");
    const result = await exec(
      `docker compose -f ${this.dockerComposeConfig} up --remove-orphans -V -d`,
    );
    console.log("📝 Docker compose output:", result.stdout);
    if (result.stderr) console.error("⚠️ Docker compose stderr:", result.stderr);
    
    console.log("⏳ Waiting for containers to be healthy...");
    await delay(5 * 1000);
    
    // Add container status check
    const statusResult = await exec(`docker compose -f ${this.dockerComposeConfig} ps`);
    console.log("📊 Container status:", statusResult.stdout);
    
    return result;
  }

  async kill() {
    console.log("🛑 Stopping Docker containers...");
    const result = await exec(`docker compose -f ${this.dockerComposeConfig} down -v`);
    console.log("✅ Docker cleanup complete:", result.stdout);
    if (result.stderr) console.error("⚠️ Docker cleanup stderr:", result.stderr);
  }
}

export async function isDockerComposeRunning(file: string) {
  console.log("🔍 Checking if Docker Compose is running...");
  try {
    const result = await exec(`docker compose -f ${file} ps --format json`);
    // console.log("📊 Raw Docker Compose status:", result.stdout || "No output");
    
    if(!result.stdout) {
      console.log("ℹ️ No Docker services found");
      return false;
    }
    
    const servicesSplit = result.stdout.trim().split("\n");
    if(servicesSplit.length === 0) {
      console.log("ℹ️ No services in Docker output");
      return false;
    }
    
    try {
      const services = servicesSplit.map((serviceRaw) => {
        try {
          return JSON.parse(serviceRaw);
        } catch (e) {
          console.error("⚠️ Failed to parse service JSON:", serviceRaw);
          return null;
        }
      }).filter(Boolean);
      
      const running = services.filter(service => service.State === "running");
      const notRunning = services.filter(service => service.State !== "running");
      
      console.log("📈 Service Status Summary:");
      console.log(`🏃 Running services: ${running.length}`);
      console.log(`⏹️ Not running services: ${notRunning.length}`);
      console.log("🔍 Service details:", {
        running: running.map(s => ({ name: s.Name, state: s.State })),
        notRunning: notRunning.map(s => ({ name: s.Name, state: s.State }))
      });
      
      return running.length > 0;
    } catch (e) {
      console.error("❌ Error processing Docker services:", e);
      return false;
    }
  } catch (error) {
    console.error("❌ Error checking Docker status:", error);
    return false;
  }
}


export function ensureDockerService(serviceName: string, file: string) {
  console.log("🔧 Creating Docker service instance...");
  const service = new DockerService(file, serviceName);
  
  return {
    async use() {
      console.log("🔍 Checking Docker service status...");
      const isRunning = await isDockerComposeRunning(file);
      console.log("📊 Docker running status:", isRunning);
      
      if(isRunning) {
        console.log("⏭️ Docker service is already running, skipping start");
        return;
      }
      
      console.log("🚀 Starting Docker service...");
      try {
        const result = await service.use();
        console.log("✅ Docker service started successfully");
        return result;
      } catch (error) {
        console.error("❌ Failed to start Docker service:", error);
        throw error;
      }
    },
    
    async release() {
      console.log("🔍 Checking Docker service status before release...");
      const isRunning = await isDockerComposeRunning(file);
      console.log("📊 Docker running status:", isRunning);
      
      if(!isRunning) {
        console.log("⏭️ Docker service is not running, skipping release");
        return;
      }
      
      if(await isDockerComposeRunning(file)) {
        console.log("⚠️ Won't release, Docker Service isn't controlled by this process");
        return;
      }
      
      console.log("🛑 Releasing Docker service...");
      try {
        await service.release();
        console.log("✅ Docker service released successfully");
      } catch (error) {
        console.error("❌ Failed to release Docker service:", error);
        throw error;
      }
    }
  };
}
