import { config as dotEnvConfig } from "dotenv";
import { resolve as pathResolve } from "path";
import { __packageroot, __gitroot } from "../constants/paths";
import { statSync as fsStat, readdirSync as fsReadDir } from "fs";

/**
 * Sets up environment variables by loading .env files from specified paths.
 *
 * This function:
 * 1. Checks for valid environment directories in possible paths
 * 2. Sets the global __env variable to the valid path
 * 3. Loads .env files that are either shared or non-client specific
 *
 * Possible paths checked:
 * - ./env relative to package root
 * - ./private-keys/api-test relative to git root
 *
 * @throws Error ifno valid environment directory is found
 */

const PRIVATE_KEYS_FOLDER = process.env.PRIVATE_KEYS_FOLDER || "test-api-local";

export function setupEnv(){
  const possiblePaths = [
    pathResolve(__packageroot, './env'),
    pathResolve(__gitroot, `./private-keys/${PRIVATE_KEYS_FOLDER}`),
  ];

  const filteredPath = possiblePaths.find((envPath) => {
    const stat = fsStat(envPath, { throwIfNoEntry: false }); // just checking if folder exists

    if(typeof stat === "undefined") {
      console.log(`📁 Directory not found: ${envPath}`);
      return false;
    }
    if(!stat.isDirectory()) {
      console.log(`❌ Not a directory: ${envPath}`);
      return false;
    }

    console.log(`✅ Valid directory found: ${envPath}`);
    return true;
  });

  if(typeof filteredPath === "undefined"){
    throw new Error("None of the possible paths exist or are directories");
  }

  (global as any).__env = filteredPath;

  const isClient = /.*-client-.*/;
  const isShared = /\.shared\./;
  const isEnv = /.*\.env/;
  const files = fsReadDir(filteredPath);

  return files.map((file)=>{
    if(!isEnv.test(file)) return;
    if(isClient.test(file) && !isShared.test(file)) return;
    const filePath = pathResolve(filteredPath, file);
    const dotEnvVars = dotEnvConfig({ path: filePath });
    return dotEnvVars
  });
}
