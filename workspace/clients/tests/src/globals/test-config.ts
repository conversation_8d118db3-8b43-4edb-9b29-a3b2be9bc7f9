import { TEST_SERVER_ENDPOINT_ORIGIN } from "../constants/internal";

export interface TestConfig {
  environment: 'local' | 'development' | 'staging' | 'production';
  apiTestEndpoint: string;
}

export function getTestConfig(): TestConfig {
  // Default to 'local' if TEST_ENV isn't set
  const environment = process.env.TEST_ENV || 'local';

  // Use the TEST_SERVER_ENDPOINT_ORIGIN that's already set by setupEnv()
  const apiTestEndpoint = TEST_SERVER_ENDPOINT_ORIGIN || '';

  if(!apiTestEndpoint) {
    throw new Error('❌ TEST_SERVER_ENDPOINT_ORIGIN is not set.');
  }

  return {
    environment: environment as TestConfig['environment'],
    apiTestEndpoint,
  };
}

let SKIP_PERMISSION_TESTS = process.env.SKIP_PERMISSION_TESTS === "true";

export function setSkipPermission(newValue: boolean){
  SKIP_PERMISSION_TESTS = newValue;
}

export function getSkipPermission(){
  return SKIP_PERMISSION_TESTS;
}
