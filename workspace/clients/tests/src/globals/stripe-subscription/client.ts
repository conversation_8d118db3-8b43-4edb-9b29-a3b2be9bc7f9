
import { <PERSON><PERSON><PERSON>, <PERSON> } from "playwright";

import { usePageToLoginAsUser } from "../auth0";
import { SubscriptionInfo } from "@divinci-ai/models";

import { TEST_SERVER_ENDPOINT_ORIGIN } from "../../constants/internal";
import { delay } from "@divinci-ai/utils";

export async function startSubscription(browser: <PERSON>rows<PERSON>, { username, password }: { username: string, password: string }){

  const context = await browser.newContext();
  const page = await context.newPage();

  await usePageToLoginAsUser(page, { username, password });

  const status = await getSubscriptionStatus(page);

  if(status.active){
    throw new Error("already started");
  }

  await page.locator("#stripe-subscription-start").click();

  // Within the Stripe Page
  await page.locator("#cardNumber").fill("4242" + "4242" + "4242" + "4242");
  const currentYear = (Number.parseInt(new Date().getFullYear().toString().substring(2)) + 4).toString();
  await page.locator("#cardExpiry").fill("01" + currentYear);
  await page.locator("#cardCvc").fill("123");
  await page.locator("#billingName").fill("Money Mcgee");
  await page.locator("#billingPostalCode").fill("20001");

  await page.locator("#enableStripePass").click();

  await page.locator("form.PaymentForm-form button[type=submit]").click();

  let counter = 0;
  do {
    counter++;
    await delay(100);
  } while(new URL(page.url()).origin !== TEST_SERVER_ENDPOINT_ORIGIN && counter < 200);

  if(new URL(page.url()).origin !== TEST_SERVER_ENDPOINT_ORIGIN){
    throw new Error("should have returned");
  }

  return await getSubscriptionStatus(page);
}

export async function endSubscription(browser: Browser, { username, password }: { username: string, password: string }): Promise<SubscriptionInfo> {
  const context = await browser.newContext();
  const page = await context.newPage();

  await usePageToLoginAsUser(page, { username, password });


  const status = await getSubscriptionStatus(page);

  if(!status.active){
    throw new Error("No Subscription Currently");
  }

  if(!status.willRenew){
    throw new Error("Already Canceled");
  }

  await page.locator("#stripe-subscription-manage").click();

  // Within the Stripe Page
  await page.locator("a[data-test=cancel-subscription]").click();
  await page.locator("button[data-test=confirm]").click();
  await page.locator("input[data-test=cancel-reason-opt-other][type=radio]").click();
  await page.locator(`textarea[placeholder="Any additional feedback?"]`).fill("Just testing");
  await page.locator("button[data-test=submit-reason]").click();

  await page.locator("a[data-test=renew-subscription]");

  await page.locator("a[data-testid=return-to-business-link]").click();

  return await getSubscriptionStatus(page);
}

export async function restartSubscription(browser: Browser, { username, password }: { username: string, password: string }): Promise<SubscriptionInfo> {
  const context = await browser.newContext();
  const page = await context.newPage();

  await usePageToLoginAsUser(page, { username, password });

  const status = await getSubscriptionStatus(page);

  if(!status.active){
    throw new Error("No Subscription Currently");
  }

  if(!status.willRenew){
    throw new Error("Already Canceled");
  }

  await page.locator("#stripe-subscription-manage").click();

  // Within the Stripe Page
  await page.locator("a[data-test=renew-subscription]").click();
  await page.locator("button[data-test=confirm]").click();

  await page.locator("a[data-test=cancel-subscription]");

  await page.locator("a[data-testid=return-to-business-link]").click();

  return await getSubscriptionStatus(page);
}



async function getSubscriptionStatus(page: Page): Promise<SubscriptionInfo> {

  await page.goto(`${TEST_SERVER_ENDPOINT_ORIGIN}`);

  // Get the JWT
  let manageableStr: string | null = null;
  let times = 0;
  do {
    manageableStr = await page.locator("#stripe-subscription-manageable").textContent();
    times++;
    await new Promise((res)=>(
      setTimeout(res, 100)
    ));
  } while((manageableStr === null || manageableStr === "") && times < 20);

  if(manageableStr === null){
    throw new Error("Did not retrieve subscription manageable");
  }

  const status = await page.locator("#stripe-subscription-status").textContent();

  if(status === null){
    throw new Error("Did not retrieve the subscription status");
  }

  return JSON.parse(status);
}
