<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
  </head>
  <body>
    <script>
      let stripeAuthFetch = null;

      async function stripeSubscriptionStart() {
        try {
          const response = await stripeAuthFetch(`${window.location.origin}/stripe-subscription/start`);
          const json = await response.json();
          if(!response.ok) throw json;
          window.location.href = json.stripeUrl;
        }catch(e){
          document.querySelector("#stripe-subscription-error").textContent = e.message;
        }
      };

      async function stripeSubscriptionManage() {
        try {
          const response = await stripeAuthFetch(`${window.location.origin}/stripe-subscription/manage`);
          const json = await response.json();
          if(!response.ok) throw json;
          window.location.href = json.stripeUrl;
        }catch(e){
          document.querySelector("#stripe-subscription-error").textContent = e.message;
        }
      };

      async function stripeSubscriptionCancel() {
        try {
          const response = await stripeAuthFetch(`${window.location.origin}/stripe-subscription/cancel`);
          const json = await response.json();
          if(!response.ok) throw json;
          await stripeSubscriptionLoad();
        }catch(e){
          document.querySelector("#stripe-subscription-error").textContent = e.message;
        }
      };
    </script>
 
    <script>
      Promise.resolve().then(async function(){
        stripeAuthFetch = await getAuthFetch();
        await stripeSubscriptionLoad();
      }).catch(function(e){
        console.error(e);
        document.querySelector("#stripe-subscription-error").textContent = e.message;
      })

      async function stripeSubscriptionLoad(){
        const response = await stripeAuthFetch(`${window.location.origin}/stripe-subscription/status`);
        const json = await response.json();
        if(!response.ok) throw json;

        console.log("stripe-subscription:", json);

        /*
        {
          active: boolean;
          willRenew: boolean;
          manageable: boolean;
          startTimestamp: number;
          endTimestamp: number;
        }
        */

        document.querySelector("#stripe-subscription-manageable").textContent = json.manageable;
        document.querySelector("#stripe-subscription-status").textContent = JSON.stringify(json, null, 2);

        document.querySelector("#stripe-subscription-start").disabled = json.manageable;
        document.querySelector("#stripe-subscription-manage").disabled = !json.manageable;
        document.querySelector("#stripe-subscription-cancel").disabled = !json.active;
      }
    </script>

    <h2>Stripe Subscription Sample</h2>
    <pre id="stripe-subscription-error"></pre>
    <button id="stripe-subscription-start" disabled="true" onclick="stripeSubscriptionStart()">Start</button>
    <button id="stripe-subscription-manage" disabled="true" onclick="stripeSubscriptionManage()">Manage</button>
    <button id="stripe-subscription-cancel" disabled="true" onclick="stripeSubscriptionCancel()">Cancel</button>
    <pre id="stripe-subscription-manageable"></pre>
    <pre id="stripe-subscription-status"></pre>
  </body>
</html>