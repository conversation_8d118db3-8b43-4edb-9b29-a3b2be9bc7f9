import { Router } from "express";
import { getUserId } from "@divinci-ai/server-globals";

import { API_ENDPOINT_ORIGIN, API_TEST_ENDPOINT_ORIGIN } from "../../constants/internal";
import { SubscriptionInfo } from "@divinci-ai/models";
import { fetchBody } from "../../util/http";

import { relayAuthFetch } from "../../util/http";

export const router = Router();

router.get("/status", async (req, res, next)=>{
  try {
    getUserId(req);

    const json = (await relayAuthFetch(req, `${API_ENDPOINT_ORIGIN}/money/transcript-value/wallet`)) as { status: SubscriptionInfo };

    res.statusCode = 200;
    res.json(json.status);
  }catch(e){
    next(e);
  }
});

router.get("/start", async (req, res, next)=>{
  try {
    getUserId(req);

    const json = await relayAuthFetch(req, `${API_ENDPOINT_ORIGIN}/money/transcript-value/start`);

    res.statusCode = 200;
    res.json(json);
  }catch(e){
    next(e);
  }
});

router.get("/manage", async (req, res, next) => {
  try {
    getUserId(req);

    const json = await relayAuthFetch(req, `${API_ENDPOINT_ORIGIN}/money/transcript-value/manage`);

    res.statusCode = 200;
    res.json(json);
  }catch(e){
    next(e);
  }
});

router.get("/cancel", async (req, res, next)=>{
  try {
    getUserId(req);

    const json = (await relayAuthFetch(req,
      `${API_TEST_ENDPOINT_ORIGIN}/money/stripe-subscription`,
      fetchBody("DELETE", {}))
    ) as { status: SubscriptionInfo };

    res.statusCode = 200;
    res.json(json);
  }catch(e){
    next(e);
  }
});


