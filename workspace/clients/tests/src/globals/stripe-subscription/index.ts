import { readFileSync } from "fs";
import { resolve as pathResolve } from "path";

import { browserService } from "../browser";
import { testServer } from "../test-server";

const html = readFileSync(pathResolve(__dirname, './index.html'), "utf-8");
import { router } from "./router";
export const config = { id: "stripe-subscription", html, router };
export * from "./client";

import {
  startSubscription as startSubscriptionInClient,
  endSubscription as endSubscriptionInClient,
  restartSubscription as restartSubscriptionInClient
} from "./client";

export async function startSubscription({ username, password }: { username: string, password: string }){
  const browser = await browserService.use();
  await testServer.use();

  await startSubscriptionInClient(browser, { username, password });

  browserService.release();
  testServer.release();
}

export async function endSubscription({ username, password }: { username: string, password: string }){
  const browser = await browserService.use();
  await testServer.use();

  await endSubscriptionInClient(browser, { username, password });

  browserService.release();
  testServer.release();
}

export async function restartSubscription({ username, password }: { username: string, password: string }){
  const browser = await browserService.use();
  await testServer.use();

  await restartSubscriptionInClient(browser, { username, password });

  browserService.release();
  testServer.release();
}
