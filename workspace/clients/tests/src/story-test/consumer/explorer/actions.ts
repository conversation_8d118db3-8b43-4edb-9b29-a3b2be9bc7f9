import { AuthFetch } from "../../../globals/auth0";
import { handleFetch } from "../../../util/http";
import { uniqueId } from "@divinci-ai/utils";



async function getTrendingChats(user: AuthFetch){
  return await handleFetch (user("/ai-chat/trending"))
}
async function getTopChats(user: AuthFetch){}

async function getChat(user: AuthFetch, chatId: string){
  return await handleFetch (user(`/ai-chat/${chatId}`))
}

async function getTrendingReleases(user: AuthFetch){}
async function getTopReleases(user: AuthFetch){}

async function getRelease(user: AuthFetch, releaseId:string){
  return await handleFetch(user(`/white-label-release/${releaseId}`))
}

async function startChat(user: AuthFetch){
  const newTitle = "Consumer Chat:" + uniqueId()
  return await handleFetch (user("/ai-chat", {
    method: "POST",
    body: JSON.stringify({ title: newTitle, releases: [] }),
    headers: {
      "Content-Type": "application/json",
    },
  }));
}

async function startRelease(user: AuthFetch){
  const whiteLabel = await createWhiteLabel()
  createReleaseDraft()
  finalizeReleaseDraft()
}
import { FineTuneBaseModel, WhiteLabelReleaseClientSide } from "@divinci-ai/models";
type FineTuneCustomModel = FineTuneBaseModel & { _id: string, baseModel: string };
type FineTuneModels = {
  usableModels: Array<FineTuneBaseModel>,
  baseModels: Array<FineTuneBaseModel>,
  fineTunedModels: Array<FineTuneCustomModel>,
};
async function getAiModels(user: AuthFetch, whitelabelId: string){
  const ai_models = await handleFetch (user(`/white-label/${whitelabelId}/fine-tune/custom-ai/base-models`))
  return ai_models as FineTuneModels
}

async function createReleaseDraft(user: AuthFetch, whitelabelId: string){
  const ai_models = await getAiModels(user, whitelabelId)
  const ai_model = ai_models.usableModels[0]
  return await handleFetch (user(`/white-label/${whitelabelId}/release`, {
    method: "POST",
    body: JSON.stringify({ 
      title: "Consumer Release:" + uniqueId(),
      description: "",
      allowAnonymousChat: false,
      maxAnonymousChatMessages: 1,
      prefixList: [
        {title: "Only Prefix", category: ai_model.category, assistantName: ai_model.value}
      ]
     }),
    headers: {
      "Content-Type": "application/json",
    },
  }))as WhiteLabelReleaseClientSide;
}

async function finalizeReleaseDraft(user: AuthFetch, whitelabelId: string, releaseId: string){
  return await handleFetch(user(`/white-label/${whitelabelId}/release/${releaseId}/release`))
}