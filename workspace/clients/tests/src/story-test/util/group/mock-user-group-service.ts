/**
 * Mock User Group Service
 * 
 * This module provides a mock implementation of the user group API for testing purposes.
 * It mimics the behavior of the real API without making actual HTTP requests.
 */

import { uniqueId } from '@divinci-ai/utils';

// Types
export interface MockUserGroup {
  _id: string;
  name: string;
  slug: string;
  ownerUser: string;
  invitations: string[];
  users: Array<{ userId: string; email: string }>;
}

// In-memory storage for user groups
const userGroups = new Map<string, MockUserGroup>();

/**
 * Create a new user group
 * @param ownerUser The user ID of the owner
 * @param name The name of the group
 * @param slug The slug of the group
 * @param ownerEmail The email of the owner
 * @returns The created user group
 */
export function createUserGroup(
  ownerUser: string,
  name: string,
  slug: string,
  ownerEmail: string
): MockUserGroup {
  // Generate a unique ID for the group
  const _id = `group_${uniqueId()}`;
  
  // Create the group
  const group: MockUserGroup = {
    _id,
    name,
    slug: slug.toLowerCase(),
    ownerUser,
    invitations: [],
    users: [{ userId: ownerUser, email: ownerEmail }]
  };
  
  // Store the group
  userGroups.set(_id, group);
  
  console.log(`[Mock] Created user group: ${name} (${_id})`);
  
  return group;
}

/**
 * Get a user group by ID
 * @param groupId The ID of the group
 * @returns The user group or null if not found
 */
export function getUserGroup(groupId: string): MockUserGroup | null {
  const group = userGroups.get(groupId);
  
  if (!group) {
    console.log(`[Mock] User group not found: ${groupId}`);
    return null;
  }
  
  console.log(`[Mock] Retrieved user group: ${group.name} (${groupId})`);
  
  return group;
}

/**
 * Get all user groups for a user
 * @param userId The user ID
 * @returns An array of user groups
 */
export function getUserGroups(userId: string): MockUserGroup[] {
  const groups: MockUserGroup[] = [];
  
  for (const group of userGroups.values()) {
    if (group.ownerUser === userId || group.users.some(user => user.userId === userId)) {
      groups.push(group);
    }
  }
  
  console.log(`[Mock] Retrieved ${groups.length} user groups for user: ${userId}`);
  
  return groups;
}

/**
 * Update a user group
 * @param groupId The ID of the group
 * @param name The new name of the group
 * @returns The updated user group or null if not found
 */
export function updateUserGroup(groupId: string, name: string): MockUserGroup | null {
  const group = userGroups.get(groupId);
  
  if (!group) {
    console.log(`[Mock] User group not found: ${groupId}`);
    return null;
  }
  
  // Update the group
  group.name = name;
  
  console.log(`[Mock] Updated user group: ${name} (${groupId})`);
  
  return group;
}

/**
 * Delete a user group
 * @param groupId The ID of the group
 * @returns The deleted user group or null if not found
 */
export function deleteUserGroup(groupId: string): MockUserGroup | null {
  const group = userGroups.get(groupId);
  
  if (!group) {
    console.log(`[Mock] User group not found: ${groupId}`);
    return null;
  }
  
  // Delete the group
  userGroups.delete(groupId);
  
  console.log(`[Mock] Deleted user group: ${group.name} (${groupId})`);
  
  return group;
}

/**
 * Invite a user to a group
 * @param groupId The ID of the group
 * @param email The email of the user to invite
 * @returns The updated user group or null if not found
 */
export function inviteUserToGroup(groupId: string, email: string): MockUserGroup | null {
  const group = userGroups.get(groupId);
  
  if (!group) {
    console.log(`[Mock] User group not found: ${groupId}`);
    return null;
  }
  
  // Check if the user is already invited
  if (group.invitations.includes(email)) {
    console.log(`[Mock] User already invited: ${email}`);
    return group;
  }
  
  // Check if the user is already a member
  if (group.users.some(user => user.email === email)) {
    console.log(`[Mock] User already a member: ${email}`);
    return group;
  }
  
  // Add the invitation
  group.invitations.push(email);
  
  console.log(`[Mock] Invited user to group: ${email} -> ${group.name} (${groupId})`);
  
  return group;
}

/**
 * Accept an invitation to a group
 * @param groupId The ID of the group
 * @param userId The user ID
 * @param email The email of the user
 * @returns The updated user group or null if not found
 */
export function acceptInvitation(
  groupId: string,
  userId: string,
  email: string
): MockUserGroup | null {
  const group = userGroups.get(groupId);
  
  if (!group) {
    console.log(`[Mock] User group not found: ${groupId}`);
    return null;
  }
  
  // Check if the user is invited
  const invitationIndex = group.invitations.indexOf(email);
  if (invitationIndex === -1) {
    console.log(`[Mock] User not invited: ${email}`);
    return null;
  }
  
  // Remove the invitation
  group.invitations.splice(invitationIndex, 1);
  
  // Add the user to the group
  group.users.push({ userId, email });
  
  console.log(`[Mock] User accepted invitation: ${email} -> ${group.name} (${groupId})`);
  
  return group;
}

/**
 * Remove a user from a group
 * @param groupId The ID of the group
 * @param userId The user ID
 * @returns The updated user group or null if not found
 */
export function removeUserFromGroup(groupId: string, userId: string): MockUserGroup | null {
  const group = userGroups.get(groupId);
  
  if (!group) {
    console.log(`[Mock] User group not found: ${groupId}`);
    return null;
  }
  
  // Check if the user is a member
  const userIndex = group.users.findIndex(user => user.userId === userId);
  if (userIndex === -1) {
    console.log(`[Mock] User not a member: ${userId}`);
    return null;
  }
  
  // Remove the user from the group
  group.users.splice(userIndex, 1);
  
  console.log(`[Mock] Removed user from group: ${userId} -> ${group.name} (${groupId})`);
  
  return group;
}

/**
 * Clear all user groups (for testing)
 */
export function clearUserGroups(): void {
  userGroups.clear();
  console.log('[Mock] Cleared all user groups');
}

/**
 * Get the number of user groups (for testing)
 * @returns The number of user groups
 */
export function getUserGroupCount(): number {
  return userGroups.size;
}
