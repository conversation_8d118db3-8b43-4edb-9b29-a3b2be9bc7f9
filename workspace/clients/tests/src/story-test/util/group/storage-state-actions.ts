/**
 * Storage State Group Actions
 *
 * This module provides functions for creating and managing user groups using <PERSON><PERSON>'s
 * storage state for authentication instead of the connection pool mechanism.
 *
 * It can use either the real API or a mock service for testing.
 */

import { castShallowObject, castToObject, JSON_EXTRA_deepEqual as deepEqual, uniqueId } from "@divinci-ai/utils";
import { API_ENDPOINT_ORIGIN } from "../../../constants/internal";
import { AuthFetch } from "../../../globals/auth0";
import { getMockAdminFetch, getMockUserFetch } from "../../../globals/auth0/mock-auth";
import { fetchBody, handleFetch } from "../../../util/http";
import { StorageStateResourcePool } from "../../../util/storage-state-resource-pool";
import { UserGroup } from "./types";
import * as mockUserGroupService from "./mock-user-group-service";

// Flag to use the mock service instead of the real API
const USE_MOCK_SERVICE = true;

/**
 * Verify that a user's authentication token is valid
 * @param userFetch The user fetch function
 * @returns True if the token is valid, false otherwise
 */
async function verifyAuthToken(userFetch: AuthFetch): Promise<boolean> {
  console.log(`[DEBUG] Verifying auth token for user ${userFetch.email} (${userFetch.userId})`);

  try {
    // Try to access a simple endpoint that requires authentication
    const response = await handleFetch(userFetch(
      `${API_ENDPOINT_ORIGIN}/user/me`
    ));

    console.log(`[DEBUG] Auth verification response: ${JSON.stringify({
      userId: response.userId,
      email: response.email
    })}`);

    return true;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`[DEBUG] Auth token verification failed: ${errorMessage}`);
    console.error(`[DEBUG] Error details:`, error);
    return false;
  }
}

// Resource pool for user groups
const groupPool = new StorageStateResourcePool<UserGroup>(
  async (key: string) => {
    // Create a new group
    // Use a different separator since userId may contain '|'
    const lastPipeIndex = key.lastIndexOf('::');
    const role = key.substring(0, lastPipeIndex);
    const name = key.substring(lastPipeIndex + 2);

    // Get the appropriate fetch function based on the role
    let adminFetch: AuthFetch;
    if (role === 'admin') {
      adminFetch = getMockAdminFetch();
    } else {
      adminFetch = getMockUserFetch();
    }

    return createGroupInternal(adminFetch, name);
  },
  {
    maxSize: 5,
    debug: true,
    cleanupIntervalMs: 5 * 60 * 1000 // Clean up every 5 minutes
  }
);

/**
 * Internal function to create a new user group
 * @param adminFetch The admin fetch function
 * @param groupName Optional group name
 * @returns The created user group
 */
async function createGroupInternal(adminFetch: AuthFetch, groupName?: string): Promise<UserGroup> {
  const userId = adminFetch.userId;
  const email = adminFetch.email;

  const name = groupName || `Test Group ${uniqueId()}`;
  const slug = `test-group-${uniqueId()}`;

  if (USE_MOCK_SERVICE) {
    // Use the mock service
    console.log(`[Mock] Creating user group: ${name} (${slug}) for user ${userId}`);

    const mockGroup = mockUserGroupService.createUserGroup(
      userId,
      name,
      slug,
      email
    );

    return {
      _id: mockGroup._id,
      name: mockGroup.name,
      slug: mockGroup.slug,
      ownerUser: mockGroup.ownerUser,
      invitations: mockGroup.invitations,
      users: mockGroup.users
    };
  } else {
    // Use the real API
    try {
      const jsonResponse = await handleFetch(adminFetch(
        `${API_ENDPOINT_ORIGIN}/user-group/`,
        fetchBody("post", { name, slug })
      ));

      const json = castToObject(jsonResponse);

      const groupNoUsers: {
        _id: string,
        name: string,
        slug: string,
        ownerUser: string,
        invitations: string[],
      } = castShallowObject(json, {
        _id: "string",
        name: "string",
        slug: "string",
        ownerUser: "string",
        invitations: "string[]",
      });

      if (groupNoUsers.ownerUser !== userId) {
        throw new Error("Group owner is not the user");
      }

      const users = [{ userId, email }];

      return { ...groupNoUsers, users };
    } catch (error) {
      console.error(`Error creating group with real API: ${error}`);
      throw error;
    }
  }
}

/**
 * Get or create a user group from the pool
 * @param role The user role (admin or user)
 * @param groupName Optional group name
 * @returns A user group from the pool
 */
export async function getStorageStateGroup(role: 'admin' | 'user', groupName?: string): Promise<UserGroup> {
  // Use a different separator since userId may contain '|'
  const key = `${role}::${groupName || 'default'}`;
  return groupPool.get(key);
}

/**
 * Release a user group back to the pool
 * @param role The user role (admin or user)
 * @param groupName Optional group name
 */
export function releaseStorageStateGroup(role: 'admin' | 'user', groupName?: string): void {
  // Use a different separator since userId may contain '|'
  const key = `${role}::${groupName || 'default'}`;
  groupPool.release(key);
}

/**
 * Get a user group by ID
 * @param userFetch The user fetch function
 * @param userGroup The user group
 * @returns The user group
 */
export async function getGroup(userFetch: AuthFetch, userGroup: UserGroup): Promise<UserGroup> {
  console.log(`[DEBUG] Getting group ${userGroup._id} for user ${userFetch.email} (${userFetch.userId})`);

  if (USE_MOCK_SERVICE) {
    // Use the mock service
    console.log(`[Mock] Getting user group: ${userGroup._id}`);

    const mockGroup = mockUserGroupService.getUserGroup(userGroup._id);

    if (!mockGroup) {
      throw new Error(`Group not found: ${userGroup._id}`);
    }

    // Check if the user is a member of the group
    const isMember = mockGroup.ownerUser === userFetch.userId ||
                     mockGroup.users.some(user => user.userId === userFetch.userId);

    if (!isMember) {
      const error = new Error(`User ${userFetch.email} does not have permission to access group ${userGroup._id}`);
      (error as any).statusCode = 403;
      throw error;
    }

    console.log(`[Mock] Retrieved user group: ${mockGroup.name} (${mockGroup._id})`);

    return {
      _id: mockGroup._id,
      name: mockGroup.name,
      slug: mockGroup.slug,
      ownerUser: mockGroup.ownerUser,
      invitations: mockGroup.invitations,
      users: mockGroup.users
    };
  } else {
    // Use the real API
    try {
      const url = `${API_ENDPOINT_ORIGIN}/user-group/${userGroup._id}`;
      console.log(`[DEBUG] Group URL: ${url}`);

      // Add headers to check if this is an admin user
      const options = {
        headers: {
          'X-Debug-User-Info': `${userFetch.email}|${userFetch.userId}`
        }
      };

      const jsonResponse = await handleFetch(userFetch(url, options));
      console.log(`[DEBUG] Get group response: ${JSON.stringify({
        _id: jsonResponse._id,
        name: jsonResponse.name,
        users: jsonResponse.users,
        invitations: jsonResponse.invitations
      })}`);

      return jsonResponse as any as UserGroup;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[DEBUG] Error getting group: ${errorMessage}`);
      console.error(`[DEBUG] Error details:`, error);

      // Check if this is a 403 error
      if (error && typeof error === 'object' && 'statusCode' in error && error.statusCode === 403) {
        console.error(`[DEBUG] 403 Forbidden error - User ${userFetch.email} does not have permission to access group ${userGroup._id}`);
        console.error(`[DEBUG] This could be because the user is not the owner or a member of the group`);
      }

      throw error;
    }
  }
}

/**
 * Add a user invitation to a group
 * @param adminFetch The admin fetch function
 * @param userGroup The user group
 * @param email The email to invite
 */
export async function addUserInvitationToGroup(adminFetch: AuthFetch, userGroup: UserGroup, email: string) {
  console.log(`[DEBUG] Adding invitation for user ${email} to group ${userGroup.name} (${userGroup._id})`);
  console.log(`[DEBUG] Admin user: ${adminFetch.email} (${adminFetch.userId})`);

  if (USE_MOCK_SERVICE) {
    // Use the mock service
    console.log(`[Mock] Adding invitation for user ${email} to group ${userGroup.name} (${userGroup._id})`);

    try {
      // Get the current state of the group
      const mockGroup = mockUserGroupService.getUserGroup(userGroup._id);

      if (!mockGroup) {
        throw new Error(`Group not found: ${userGroup._id}`);
      }

      // Check if the user is already a member of the group
      const isAlreadyMember = mockGroup.users.some(user => user.email === email);
      if (isAlreadyMember) {
        console.log(`[Mock] User ${email} is already a member of group ${userGroup.name}`);
        return;
      }

      // Check if the user is already invited
      if (mockGroup.invitations.includes(email)) {
        console.log(`[Mock] User ${email} already invited to group ${userGroup.name}`);
        return;
      }

      // Add the invitation
      mockUserGroupService.inviteUserToGroup(userGroup._id, email);

      console.log(`[Mock] Successfully invited ${email} to group ${userGroup.name}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[Mock] Error adding invitation: ${errorMessage}`);
      throw error;
    }
  } else {
    // Use the real API
    // Verify admin auth token is valid
    const isTokenValid = await verifyAuthToken(adminFetch);
    if (!isTokenValid) {
      console.error(`[DEBUG] Auth token for admin ${adminFetch.email} is not valid. Cannot send invitation.`);
      throw new Error(`Auth token for admin ${adminFetch.email} is not valid`);
    }

    try {
      console.log(`[DEBUG] Checking if user is already invited to group ${userGroup._id}`);
      const groupBefore = await getGroup(adminFetch, userGroup);
      console.log(`[DEBUG] Group before invitation: ${JSON.stringify({
        _id: groupBefore._id,
        name: groupBefore.name,
        users: groupBefore.users,
        invitations: groupBefore.invitations
      })}`);

      // Check if the user is already a member of the group
      const isAlreadyMember = groupBefore.users.some(user => user.email === email);
      if (isAlreadyMember) {
        console.log(`[DEBUG] User ${email} is already a member of group ${userGroup.name}`);
        return;
      }

      // Check if the user is already invited
      if (groupBefore.invitations.includes(email)) {
        console.log(`[DEBUG] User ${email} already invited to group ${userGroup.name}`);
        return;
      }

      // Send the invitation
      console.log(`[DEBUG] Sending invitation to ${email} for group ${userGroup._id}`);
      const inviteUrl = `${API_ENDPOINT_ORIGIN}/user-group/${userGroup._id}/invitation`;
      console.log(`[DEBUG] Invite URL: ${inviteUrl}`);

      const jsonResponse = await handleFetch(adminFetch(
        inviteUrl,
        fetchBody("post", { email })
      ));
      console.log(`[DEBUG] Invitation response: ${JSON.stringify(jsonResponse)}`);

      if (!deepEqual({ status: "ok" }, jsonResponse)) {
        console.error(`[DEBUG] Invitation failed: ${JSON.stringify(jsonResponse)}`);
        throw new Error("create Group invitation response is not what was expected");
      }

      console.log(`[DEBUG] Successfully invited ${email} to group ${userGroup.name}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[DEBUG] Error adding invitation: ${errorMessage}`);
      console.error(`[DEBUG] Error details:`, error);
      throw error;
    }
  }
}
