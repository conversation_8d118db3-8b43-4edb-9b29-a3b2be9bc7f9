import { fetchBody, handleFetch } from "../../../util/http";
import { AuthFetch } from "../../../globals/auth0";
import { castShallowObject, castToObject, deepEqual, JSON_Unknown, uniqueId } from "@divinci-ai/utils";
import { UserGroup } from "./types";

import { API_ENDPOINT_ORIGIN } from "../../../constants/internal";

export async function createGroup(adminFetch: AuthFetch): Promise<UserGroup>{
  const userId = adminFetch.userId;

  const name = "New User Group";
  const slug = "new-user-group" + uniqueId();

  const jsonResponse = await handleFetch(adminFetch(
    `${API_ENDPOINT_ORIGIN}/user-group/`,
    fetchBody("post", { name, slug })
  ));

  const json = castToObject(jsonResponse);

  const testable = { ...json };
  delete testable._id;

  const groupNoUsers: {
    _id: string,
    name: string,
    slug: string,
    ownerUser: string,
    invitations: string[],
  } = castShallowObject(json, {
    _id: "string",
    name: "string",
    slug: "string",
    ownerUser: "string",
    invitations: "string[]",
  });

  if(groupNoUsers.ownerUser !== userId){
    throw new Error("Group owner is not the user");
  }
  if(groupNoUsers.name !== name){
    throw new Error("Group name is not what was expected");
  }
  if(groupNoUsers.slug !== slug){
    throw new Error("Group slug is not what was expected");
  }

  const users: Array<{ userId: string, email: string }> = [];
  if(!Array.isArray(json.users)){
    throw new Error("Users expected to be an array");
  }
  for(const user of json.users){
    users.push(castShallowObject(user, {
      userId: "string", email: "string"
    }));
  }
  if(users.length !== 1 || users[0].userId !== userId){
    throw new Error("Group users is not what was expected");
  }

  const jsonResponse2 = await getGroup(adminFetch, { ...groupNoUsers, users });
  if(!deepEqual(jsonResponse, jsonResponse2 as any as JSON_Unknown)){
    throw new Error("Retrieved UserGroup is not the same as created UserGroup");
  }

  return { ...groupNoUsers, users };
}

export async function getGroup(userFetch: AuthFetch, userGroup: UserGroup): Promise<UserGroup>{

  const jsonResponse = await handleFetch(userFetch(
    `${API_ENDPOINT_ORIGIN}/user-group/${userGroup._id}`,
  ));

  // await UserGroupModel.validate(jsonResponse);
  return jsonResponse as any as UserGroup;
}


export async function deleteGroup(adminFetch: AuthFetch, userGroup: UserGroup){
  const jsonResponse = await handleFetch(adminFetch(
    `${API_ENDPOINT_ORIGIN}/user-group/${userGroup._id}`, { method: "DELETE" }
  ));

  if(!deepEqual(userGroup, jsonResponse)){
    throw new Error("delete Group response is not what was expected");
  }
}


export async function addUserInvitationToGroup(adminFetch: AuthFetch, userGroup: UserGroup, email: string){

  const groupBefore = await getGroup(adminFetch, userGroup);

  if(groupBefore.invitations.includes(email)){
    throw new Error("Group already inviting email");
  }

  const jsonResponse = await handleFetch(adminFetch(
    `${API_ENDPOINT_ORIGIN}/user-group/${userGroup._id}/invitation`,
    fetchBody("post", { email })
  ));

  if(!deepEqual({ status: "ok" }, jsonResponse)){
    throw new Error("create Group invitation response is not what was expected");
  }

  const groupAfter = await getGroup(adminFetch, userGroup);

  if(!groupAfter.invitations.includes(email)){
    throw new Error("Group isn't inviting email");
  }
}

export async function removeUserInvitationFromGroup(adminFetch: AuthFetch, userGroup: UserGroup, email: string){
  const jsonResponse = await handleFetch(adminFetch(
    `${API_ENDPOINT_ORIGIN}/user-group/${userGroup._id}/invitation`,
    fetchBody("delete", { email })
  ));

  if(!deepEqual({ status: "ok" }, jsonResponse)){
    throw new Error("delete Group invitation response is not what was expected");
  }
}

export async function acceptUserInvitationToGroup(userFetch: AuthFetch, userGroup: UserGroup){
  const jsonResponse = await handleFetch(userFetch(
    `${API_ENDPOINT_ORIGIN}/user-group/${userGroup._id}/invitation/accept`,
  ));

  if(!deepEqual({ status: "ok" }, jsonResponse)){
    throw new Error("Accept Group invite response is not what was expected");
  }
}

export async function removeUserFromGroup(adminFetch: AuthFetch, userGroup: UserGroup, userId: string){
  const jsonResponse = await handleFetch(adminFetch(
    `${API_ENDPOINT_ORIGIN}/user-group/${userGroup._id}/user`,
    fetchBody("delete", { userId })
  ));

  if(!deepEqual({ status: "ok" }, jsonResponse)){
    throw new Error("delete user from Group response is not what was expected");
  }
}
