/**
 * Pooled Group Actions
 *
 * This module provides functions for creating and managing user groups with resource pooling
 * to avoid creating too many groups and hitting rate limits.
 */

import { castShallowObject, castToObject, JSON_EXTRA_deepEqual as deepEqual, uniqueId } from "@divinci-ai/utils";
import { API_ENDPOINT_ORIGIN } from "../../../constants/internal";
import { AuthFetch } from "../../../globals/auth0";
import { fetchBody, handleFetch } from "../../../util/http";
import { ResourcePool } from "../../../util/resource-pool";
import { UserGroup } from "./types";

/**
 * Verify that a user's authentication token is valid
 * @param userFetch The user fetch function
 * @returns True if the token is valid, false otherwise
 */
async function verifyAuthToken(userFetch: AuthFetch): Promise<boolean> {
  console.log(`[DEBUG] Verifying auth token for user ${userFetch.email} (${userFetch.userId})`);

  try {
    // Try to access a simple endpoint that requires authentication
    const response = await handleFetch(userFetch(
      `${API_ENDPOINT_ORIGIN}/user/me`
    ));

    console.log(`[DEBUG] Auth verification response: ${JSON.stringify({
      userId: response.userId,
      email: response.email
    })}`);

    return true;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`[DEBUG] Auth token verification failed: ${errorMessage}`);
    console.error(`[DEBUG] Error details:`, error);
    return false;
  }
}

// Map to store fetch functions by userId
const fetchMap = new Map<string, AuthFetch>();

// Resource pool for user groups
const groupPool = new ResourcePool<UserGroup>(
  async (key: string) => {
    // Create a new group
    // Use a different separator since userId may contain '|'
    const lastPipeIndex = key.lastIndexOf('::');
    const userId = key.substring(0, lastPipeIndex);
    const name = key.substring(lastPipeIndex + 2);

    const adminFetch = fetchMap.get(userId);

    if (!adminFetch) {
      throw new Error(`No fetch function found for user ${userId}`);
    }

    return createGroupInternal(adminFetch, name);
  },
  { maxSize: 5, debug: true }
);

/**
 * Internal function to create a new user group
 * @param adminFetch The admin fetch function
 * @param groupName Optional group name
 * @returns The created user group
 */
async function createGroupInternal(adminFetch: AuthFetch, groupName?: string): Promise<UserGroup> {
  const userId = adminFetch.userId;

  const name = groupName || `Test Group ${uniqueId()}`;
  const slug = `test-group-${uniqueId()}`;

  const jsonResponse = await handleFetch(adminFetch(
    `${API_ENDPOINT_ORIGIN}/user-group/`,
    fetchBody("post", { name, slug })
  ));

  const json = castToObject(jsonResponse);

  const groupNoUsers: {
    _id: string,
    name: string,
    slug: string,
    ownerUser: string,
    invitations: string[],
  } = castShallowObject(json, {
    _id: "string",
    name: "string",
    slug: "string",
    ownerUser: "string",
    invitations: "string[]",
  });

  if (groupNoUsers.ownerUser !== userId) {
    throw new Error("Group owner is not the user");
  }

  const users = [{ userId, email: adminFetch.email }];

  return { ...groupNoUsers, users };
}

/**
 * Get or create a user group from the pool
 * @param adminFetch The admin fetch function
 * @param groupName Optional group name
 * @returns A user group from the pool
 */
export async function getPooledGroup(adminFetch: AuthFetch, groupName?: string): Promise<UserGroup> {
  // Make sure adminFetch is a valid AuthFetch object
  if (!adminFetch || typeof adminFetch !== 'function') {
    console.error('Invalid adminFetch object:', adminFetch);
    throw new Error('Invalid adminFetch object');
  }

  // Store the fetch function in the map
  fetchMap.set(adminFetch.userId, adminFetch);

  // Use a different separator since userId may contain '|'
  const key = `${adminFetch.userId}::${groupName || 'default'}`;
  return groupPool.get(key);
}

/**
 * Release a user group back to the pool
 * @param adminFetch The admin fetch function
 * @param groupName Optional group name
 */
export function releasePooledGroup(adminFetch: AuthFetch, groupName?: string): void {
  // Make sure adminFetch is a valid AuthFetch object
  if (!adminFetch || typeof adminFetch !== 'function' || !adminFetch.userId) {
    console.error('Invalid adminFetch object:', adminFetch);
    return;
  }

  // Use a different separator since userId may contain '|'
  const key = `${adminFetch.userId}::${groupName || 'default'}`;
  groupPool.release(key);
}

/**
 * Get a user group by ID
 * @param userFetch The user fetch function
 * @param userGroup The user group
 * @returns The user group
 */
export async function getGroup(userFetch: AuthFetch, userGroup: UserGroup): Promise<UserGroup> {
  console.log(`[DEBUG] Getting group ${userGroup._id} for user ${userFetch.email} (${userFetch.userId})`);

  try {
    const url = `${API_ENDPOINT_ORIGIN}/user-group/${userGroup._id}`;
    console.log(`[DEBUG] Group URL: ${url}`);

    // Add headers to check if this is an admin user
    const options = {
      headers: {
        'X-Debug-User-Info': `${userFetch.email}|${userFetch.userId}`
      }
    };

    const jsonResponse = await handleFetch(userFetch(url, options));
    console.log(`[DEBUG] Get group response: ${JSON.stringify({
      _id: jsonResponse._id,
      name: jsonResponse.name,
      users: jsonResponse.users,
      invitations: jsonResponse.invitations
    })}`);

    return jsonResponse as any as UserGroup;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`[DEBUG] Error getting group: ${errorMessage}`);
    console.error(`[DEBUG] Error details:`, error);

    // Check if this is a 403 error
    if (error && typeof error === 'object' && 'statusCode' in error && error.statusCode === 403) {
      console.error(`[DEBUG] 403 Forbidden error - User ${userFetch.email} does not have permission to access group ${userGroup._id}`);
      console.error(`[DEBUG] This could be because the user is not the owner or a member of the group`);
    }

    throw error;
  }
}

/**
 * Add a user invitation to a group
 * @param adminFetch The admin fetch function
 * @param userGroup The user group
 * @param email The email to invite
 */
export async function addUserInvitationToGroup(adminFetch: AuthFetch, userGroup: UserGroup, email: string) {
  console.log(`[DEBUG] Adding invitation for user ${email} to group ${userGroup.name} (${userGroup._id})`);
  console.log(`[DEBUG] Admin user: ${adminFetch.email} (${adminFetch.userId})`);

  // Verify admin auth token is valid
  const isTokenValid = await verifyAuthToken(adminFetch);
  if (!isTokenValid) {
    console.error(`[DEBUG] Auth token for admin ${adminFetch.email} is not valid. Cannot send invitation.`);
    throw new Error(`Auth token for admin ${adminFetch.email} is not valid`);
  }

  try {
    console.log(`[DEBUG] Checking if user is already invited to group ${userGroup._id}`);
    const groupBefore = await getGroup(adminFetch, userGroup);
    console.log(`[DEBUG] Group before invitation: ${JSON.stringify({
      _id: groupBefore._id,
      name: groupBefore.name,
      users: groupBefore.users,
      invitations: groupBefore.invitations
    })}`);

    // Check if the user is already a member of the group
    const isAlreadyMember = groupBefore.users.some(user => user.email === email);
    if (isAlreadyMember) {
      console.log(`[DEBUG] User ${email} is already a member of group ${userGroup.name}`);
      return;
    }

    // Check if the user is already invited
    if (groupBefore.invitations.includes(email)) {
      console.log(`[DEBUG] User ${email} already invited to group ${userGroup.name}`);
      return;
    }

    // Try to send the invitation with retries
    const maxRetries = 3;
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[DEBUG] Sending invitation to ${email} for group ${userGroup._id} (Attempt ${attempt}/${maxRetries})`);
        const inviteUrl = `${API_ENDPOINT_ORIGIN}/user-group/${userGroup._id}/invitation`;
        console.log(`[DEBUG] Invite URL: ${inviteUrl}`);

        const jsonResponse = await handleFetch(adminFetch(
          inviteUrl,
          fetchBody("post", { email })
        ));
        console.log(`[DEBUG] Invitation response: ${JSON.stringify(jsonResponse)}`);

        if (!deepEqual({ status: "ok" }, jsonResponse)) {
          console.error(`[DEBUG] Invitation failed: ${JSON.stringify(jsonResponse)}`);
          throw new Error("create Group invitation response is not what was expected");
        }

        // Verify invitation was added
        const groupAfter = await getGroup(adminFetch, userGroup);
        console.log(`[DEBUG] Group after invitation: ${JSON.stringify({
          _id: groupAfter._id,
          name: groupAfter.name,
          users: groupAfter.users,
          invitations: groupAfter.invitations
        })}`);

        if (!groupAfter.invitations.includes(email)) {
          console.warn(`[DEBUG] WARNING: User ${email} not found in invitations after adding`);
          // Continue with the next retry if we have attempts left
          if (attempt < maxRetries) {
            throw new Error("Invitation not found in group after adding");
          }
        } else {
          console.log(`[DEBUG] Successfully invited ${email} to group ${userGroup.name}`);
          return; // Success, exit the function
        }
      } catch (error) {
        lastError = error;
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`[DEBUG] Error adding invitation (Attempt ${attempt}/${maxRetries}): ${errorMessage}`);

        if (attempt < maxRetries) {
          // Wait before retrying (exponential backoff)
          const delay = Math.pow(2, attempt) * 500; // 1s, 2s, 4s
          console.log(`[DEBUG] Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // If we get here, all retries failed
    console.error(`[DEBUG] All ${maxRetries} attempts to add invitation failed`);
    throw lastError || new Error("Failed to add invitation after multiple attempts");
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`[DEBUG] Error adding invitation: ${errorMessage}`);
    console.error(`[DEBUG] Error details:`, error);
    throw error;
  }
}

/**
 * Accept a user invitation to a group
 * @param userFetch The user fetch function
 * @param userGroup The user group
 */
export async function acceptUserInvitationToGroup(userFetch: AuthFetch, userGroup: UserGroup) {
  console.log(`[DEBUG] Accepting invitation for user ${userFetch.email} (${userFetch.userId}) to group ${userGroup.name} (${userGroup._id})`);

  // Verify auth token is valid
  const isTokenValid = await verifyAuthToken(userFetch);
  if (!isTokenValid) {
    console.error(`[DEBUG] Auth token for user ${userFetch.email} is not valid. Cannot accept invitation.`);
    throw new Error(`Auth token for user ${userFetch.email} is not valid`);
  }

  try {
    // Check if user is already in the group
    console.log(`[DEBUG] Checking if user is already in group ${userGroup._id}`);

    let groupBefore;
    try {
      groupBefore = await getGroup(userFetch, userGroup);
      console.log(`[DEBUG] Group before: ${JSON.stringify({
        _id: groupBefore._id,
        name: groupBefore.name,
        users: groupBefore.users,
        invitations: groupBefore.invitations
      })}`);
    } catch (error) {
      if (error && typeof error === 'object' && 'statusCode' in error && error.statusCode === 403) {
        // If we get a 403, the user might not have permission to view the group directly
        // This is expected if they're not a member yet
        console.log(`[DEBUG] User cannot view group directly (403). This is expected if not a member yet.`);
        // Continue with the invitation acceptance
      } else {
        throw error;
      }
    }

    // If we successfully got the group, check if user is already a member
    if (groupBefore && groupBefore.users && groupBefore.users.some(user => user.userId === userFetch.userId)) {
      console.log(`[DEBUG] User ${userFetch.email} already in group ${userGroup.name}`);
      return;
    }

    // Try to accept the invitation with retries
    const maxRetries = 3;
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[DEBUG] Accepting invitation to group ${userGroup._id} (Attempt ${attempt}/${maxRetries})`);
        const acceptUrl = `${API_ENDPOINT_ORIGIN}/user-group/${userGroup._id}/invitation/accept`;
        console.log(`[DEBUG] Accept URL: ${acceptUrl}`);

        const jsonResponse = await handleFetch(userFetch(acceptUrl));
        console.log(`[DEBUG] Accept invitation response: ${JSON.stringify(jsonResponse)}`);

        if (!deepEqual({ status: "ok" }, jsonResponse)) {
          console.error(`[DEBUG] Accept invitation failed: ${JSON.stringify(jsonResponse)}`);
          throw new Error("Accept Group invite response is not what was expected");
        }

        console.log(`[DEBUG] Successfully accepted invitation to group ${userGroup.name}`);
        return; // Success, exit the function
      } catch (error) {
        lastError = error;
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`[DEBUG] Error accepting invitation (Attempt ${attempt}/${maxRetries}): ${errorMessage}`);

        if (attempt < maxRetries) {
          // Wait before retrying (exponential backoff)
          const delay = Math.pow(2, attempt) * 500; // 1s, 2s, 4s
          console.log(`[DEBUG] Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // If we get here, all retries failed
    console.error(`[DEBUG] All ${maxRetries} attempts to accept invitation failed`);
    throw lastError;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`[DEBUG] Error accepting invitation: ${errorMessage}`);
    console.error(`[DEBUG] Error details:`, error);
    throw error;
  }
}

/**
 * Set permissions for a group
 * @param owner The owner fetch function
 * @param permissionUrl The permission URL
 * @param userGroup The user group
 * @param permissions The permissions to set
 */
export function setPermissionForGroup(
  owner: AuthFetch, permissionUrl: string, userGroup: UserGroup, permissions: string[]
) {
  return handleFetch(owner(
    `${permissionUrl}/user-group`,
    fetchBody("POST", { groupSlug: userGroup.slug, permissions: permissions })
  ));
}

/**
 * Remove permissions for a group
 * @param owner The owner fetch function
 * @param permissionUrl The permission URL
 * @param userGroup The user group
 */
export function removePermissionForGroup(owner: AuthFetch, permissionUrl: string, userGroup: UserGroup) {
  return handleFetch(owner(
    `${permissionUrl}/user-group`,
    fetchBody("delete", { groupSlug: userGroup.slug })
  ));
}
