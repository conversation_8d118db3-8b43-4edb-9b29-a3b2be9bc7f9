import { AuthFetch } from "../../../../globals/auth0";
import { DocumentPermissionDoc } from "../types";

import { fetchBody, handleFetch } from "../../../../util/http";


export async function getPermission(owner: AuthFetch, permissionUrl: string){
  const result = await handleFetch(owner(permissionUrl));
  return result as { doc: DocumentPermissionDoc };
}

export async function clearPermissions(owner: AuthFetch, permissionUrl: string){
  const { doc } = await getPermission(owner, permissionUrl);

  await Promise.all([
    owner(`${permissionUrl}/loggedin`, fetchBody("POST", { permissions: [] })),
    owner(`${permissionUrl}/anonymous`, fetchBody("POST", { permissions: [] })),
    Promise.all(Object.values(doc.group).map(({ groupSlug })=>(
      owner(`${permissionUrl}/user-group`, fetchBody("DELETE", { groupSlug }))
    ))),
    Promise.all(Object.values(doc.userPermissions).map(({ userId })=>(
      owner(`${permissionUrl}/active-user`, fetchBody("DELETE", { userId }))
    ))),
  ]);
}

export function setAnonymousPermission(owner: AuthFetch, permissionUrl: string, permissions: string[]){
  return handleFetch(owner(
    `${permissionUrl}/anonymous`,
    { method: "POST", body: JSON.stringify({ permissions }) }
  ));
}

export function setLoggedInPermission(owner: AuthFetch, permissionUrl: string, permissions: string[]){
  return handleFetch(owner(
    `${permissionUrl}/loggedin`,
    { method: "POST", body: JSON.stringify({ permissions }) }
  ));
}
