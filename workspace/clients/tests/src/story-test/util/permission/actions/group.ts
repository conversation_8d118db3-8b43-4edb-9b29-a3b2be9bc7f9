import { AuthFetch } from "../../../../globals/auth0";
import { fetchBody, handleFetch } from "../../../../util/http";
import { UserGroup } from "../../group";

export function setPermissionForGroup(
  owner: AuthFetch, permissionUrl: string, userGroup: UserGroup, permissions: string[]
){
  return handleFetch(owner(
    `${permissionUrl}/user-group`,
    fetchBody("POST", { groupSlug: userGroup.slug, permissions: permissions })
  ));
}

export function removePermissionForGroup(owner: AuthFetch, permissionUrl: string, userGroup: UserGroup){
  return handleFetch(owner(
    `${permissionUrl}/user-group`,
    fetchBody("delete", { groupSlug: userGroup.slug })
  ));
}
