
import { JSON_EXTRA_deepEqual, jsonResponse } from "@divinci-ai/utils";
import { fetchBody, } from "../../../../util/http";


export async function inviteNewOwnerForPermission(adminFetch: typeof fetch, permissionUrl: string, newOwnerEmail: string){
  const changeResult = await jsonResponse(
    adminFetch(
      `${permissionUrl}/new-owner`,
      fetchBody("POST", { email: newOwnerEmail }) // Not testing replyTo
    )
  );

  if(!JSON_EXTRA_deepEqual(changeResult, { status: "ok" })){
    throw new Error("Unexpected result when setting owner");
  }

  return changeResult;
}

export async function retractNewOwnerForPermission(adminFetch: typeof fetch, permissionUrl: string){
  const retractResult = await jsonResponse(
    adminFetch(
      `${permissionUrl}/new-owner`,
      { method: "DELETE" }
    )
  );

  if(!JSON_EXTRA_deepEqual(retractResult, { status: "ok" })){
    throw new Error("Unexpected result when retracting newOwner");
  }

  return retractResult;
}

export async function acceptNewOwnerForPermission(allowedFetch: typeof fetch, permissionUrl: string){
  const acceptResult = await jsonResponse(
    allowedFetch(
      `${permissionUrl}/new-owner/accept`,
    )
  );

  if(!JSON_EXTRA_deepEqual(acceptResult, { status: "ok" })){
    throw new Error("Unexpected result when accepting newOwner");
  }

  return acceptResult;
}
