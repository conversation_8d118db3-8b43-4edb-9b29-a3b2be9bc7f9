
import { AuthFetch } from "../../../../globals/auth0";
import { fetchBody, handleFetch } from "../../../../util/http";

import { PermissionUser } from "../types";

export function addUserInviteToPermission(
  owner: AuthFetch, permissionUrl: string, user: PermissionUser, permissions: string[]
){
  return handleFetch(owner(
    `${permissionUrl}/invite-user`,
    fetchBody("post", { email: user.email, permissions })
  ));
}

export function retractUserInviteToPermission(
  owner: AuthFetch, permissionUrl: string, user: PermissionUser
){
  return handleFetch(owner(
    `${permissionUrl}/invite-user`,
    fetchBody("delete", { email: user.email })
  ));
}

export function acceptUserInviteToPermission(
  invitedUser: AuthFetch, permissionUrl: string
){
  return handleFetch(invitedUser(
    `${permissionUrl}/invite-user/accept`
  ));
}

export function updateUserPermission(
  owner: AuthFetch, permissionUrl: string, user: PermissionUser, permissions: string[]
){
  return handleFetch(owner(
    `${permissionUrl}/active-user`,
    fetchBody("post", { userId: user.userId, permissions })
  ));
}

export function removeUserPermission(
  owner: AuthFetch, permissionUrl: string, user: PermissionUser
){
  return handleFetch(owner(
    `${permissionUrl}/active-user`,
    fetchBody("delete", { userId: user.userId })
  ));
}
