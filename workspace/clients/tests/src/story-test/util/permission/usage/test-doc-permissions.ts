import { Test } from "tap";
import { AuthFetch } from "../../../../globals/auth0";
import { DocConfig, PermissionRunner, TestFn } from "../types";

import { shouldFailWithStatus } from "../../../../util/http";
import { Doc } from "@divinci-ai/models";
import { DIVINCI_PERMISSION_DRY_HEADER } from "@divinci-ai/server-globals";

function permissionOverrideRunner(runner: AuthFetch): AuthFetch{
  const fetcher = function(...args: Parameters<typeof fetch>){
    const url = args[0];
    let options = args[1];
    if(!options) options = {};
    options.headers = new Headers(options.headers || {});
    options.headers.set(DIVINCI_PERMISSION_DRY_HEADER, "true");
    return runner(url, options);
  } as AuthFetch;

  fetcher.username = runner.username;
  fetcher.password = runner.password;
  fetcher.email = runner.email;
  fetcher.userId = runner.userId;

  return fetcher;
}

export async function testDocPermissions<TestDoc extends Doc<any>>(
  fnTest: Test,
  permissionRunner: PermissionRunner<any>,
  docConfig: DocConfig<TestDoc>,
  runnableTest: TestFn<TestDoc>,
  admin: AuthFetch, userFetch: AuthFetch,
  isAllowed: boolean
): Promise<any>{

  const originalUsers = { admin, user: userFetch };

  // Should always fail without permission
  await fnTest.test("Should fail without permission", async (test)=>{
    const doc = await docConfig.createDoc(admin);
    await shouldFailWithStatus(
      runnableTest.fn(test, admin, originalUsers.user, doc),
      403, "Should have failed with 403 because user doesn't have permission"
    );
    test.pass("Failed with 403 because user doesn't have permission");
  });


  // If permission can't be added to the user, then it should fail when attempting to add it
  if(!isAllowed){
    return await fnTest.test("Should fail adding permission", async (test)=>{
      const doc = await docConfig.createDoc(admin);
      const permissionUrl = docConfig.docToURLPrefix(doc);
      await shouldFailWithStatus(
        permissionRunner.addPermissionForUser(
          test, originalUsers, permissionUrl, runnableTest.permission
        ),
        400, "Should have failed with 400 because it's a bad form when adding unallowed permission"
      );
      test.pass("Failed with 400 because user can't recieve permission");
    });
  }

  for(const remover of permissionRunner.removePermissionForUser){
    await fnTest.test(`Permission can be added and removed by ${remover.name}`, async (test)=>{
      const doc = await docConfig.createDoc(originalUsers.admin);
      const permissionUrl = docConfig.docToURLPrefix(doc);

      const { config, admin, user } = await permissionRunner.addPermissionForUser(
        test, originalUsers, permissionUrl, runnableTest.permission
      );
      test.pass("Able to add permission for user");
      const permissionUser = permissionOverrideRunner(user);

      await runnableTest.fn(test, admin, permissionUser, doc);
      test.pass("Able to run test with permission");

      const removeUsers = await remover.runner(
        test, { admin, user }, permissionUrl, runnableTest.permission, config
      );
      test.pass("Able to remove permission for user");

      await shouldFailWithStatus(
        runnableTest.fn(test, removeUsers.admin, removeUsers.user, doc),
        403, "Should have failed with 403 because user doesn't have permission"
      );
      test.pass("Should have failed with 403 because permission removed from user");
    });
  }
}
