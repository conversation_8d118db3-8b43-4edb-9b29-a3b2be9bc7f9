import { Test } from "tap";
import { getPermission } from "../../actions";
import { DocumentPermissionDoc, PermissionRunner } from "../../types";

import { UserGroup } from "../../../group";
import {
  getPooledGroup,
  releasePooledGroup,
  addUserInvitationToGroup,
  acceptUserInvitationToGroup,
  setPermissionForGroup,
  removePermissionForGroup,
  getGroup
} from "../../../group/pooled-actions";
export const GroupPermission: PermissionRunner<UserGroup> = {
  key: "group",
  isAllowed: (doc, permission)=>(doc.userRestriction.includes(permission)),
  async addPermissionForUser(test, { admin, user }, permissionUrl, permission){
    console.log(`[DEBUG] Adding group permission for user ${user.email} (${user.userId})`);
    console.log(`[DEBUG] Admin user: ${admin.email} (${admin.userId})`);
    console.log(`[DEBUG] Permission URL: ${permissionUrl}`);
    console.log(`[DEBUG] Permission: ${permission}`);

    try {
      const { doc: prev } = await getPermission(admin, permissionUrl);
      test.equal(
        Object.keys(prev.group).length, 0,
        "There should be no groups on the permission"
      );

      // Get a group from the pool instead of creating a new one
      console.log(`[DEBUG] Getting group from pool for admin ${admin.email}`);
      const userGroup = await getPooledGroup(admin, `test-group-${Date.now()}`);
      test.pass("Group obtained from pool");
      console.log(`[DEBUG] Group obtained from pool: ${userGroup._id} (${userGroup.name})`);

      // Add user to group if not already a member
      console.log(`[DEBUG] Inviting user ${user.email} to group ${userGroup._id}`);
      await addUserInvitationToGroup(admin, userGroup, user.email);
      test.pass("User invited to group");

      try {
        console.log(`[DEBUG] User ${user.email} accepting invitation to group ${userGroup._id}`);
        await acceptUserInvitationToGroup(user, userGroup);
        test.pass("User accepted invitation");
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`[DEBUG] Error accepting invitation: ${errorMessage}`);

        // If we get a 403 error, we'll try a workaround
        if (error && typeof error === 'object' && 'statusCode' in error && error.statusCode === 403) {
          console.log(`[DEBUG] Using admin to verify if user is in the group after invitation`);

          // Use the admin to check if the user is in the group
          const groupAfter = await getGroup(admin, userGroup);
          const userInGroup = groupAfter.users.some(u => u.email === user.email || u.userId === user.userId);

          if (userInGroup) {
            console.log(`[DEBUG] User ${user.email} is already in the group according to admin check`);
            test.pass("User is in group (verified by admin)");
          } else {
            // If the user is not in the group, we need to throw the original error
            throw error;
          }
        } else {
          // For other errors, just rethrow
          throw error;
        }
      }

      console.log(`[DEBUG] Setting permission for group ${userGroup.slug}`);
      await setPermissionForGroup(admin, permissionUrl, userGroup, [permission]);
      console.log(`[DEBUG] Permission set for group`);

      const { doc: after } = await getPermission(admin, permissionUrl);
      ensureGroupHasPermission(test, after, userGroup, permission);
      user.groupId = userGroup._id;

      console.log(`[DEBUG] Successfully added group permission for user ${user.email}`);
      return { config: userGroup, admin, user };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[DEBUG] Error in addPermissionForUser: ${errorMessage}`);
      console.error(`[DEBUG] Error details:`, error);
      throw error;
    }
  },
  removePermissionForUser: [
    {
      name: "Remove the whole group from the permission",
      runner: async (test, { admin, user }, permissionUrl, permission, userGroup)=>{
        const { doc: permissionBefore } = await getPermission(admin, permissionUrl);
        ensureGroupHasPermission(test, permissionBefore, userGroup, permission);

        await removePermissionForGroup(admin, permissionUrl, userGroup);

        const { doc: permissionAfter } = await getPermission(admin, permissionUrl);

        test.equal(
          permissionAfter.group[userGroup.slug], void 0,
          "Group should not be on the permission"
        );

        // Release the group back to the pool
        releasePooledGroup(admin, userGroup.name);

        return { admin, user };
      },
    },
    {
      name: "Set Group Permission to Empty",
      runner: async (test, { admin, user }, permissionUrl, permission, userGroup)=>{
        const { doc: permissionBefore } = await getPermission(admin, permissionUrl);
        ensureGroupHasPermission(test, permissionBefore, userGroup, permission);

        await setPermissionForGroup(admin, permissionUrl, userGroup, []);

        const { doc: permissionAfter } = await getPermission(admin, permissionUrl);

        test.equal(
          Object.keys(permissionAfter.group).length, 1,
          "There should be a group on the permission"
        );
        test.not(
          permissionAfter.group[userGroup.slug], void 0,
          "The group slug should be available on the permission"
        );
        test.notOk(
          permissionAfter.group[userGroup.slug].permissions.includes(permission),
          "The group slug should not have the added permission"
        );

        // Release the group back to the pool
        releasePooledGroup(admin, userGroup.name);

        return { admin, user };
      }
    }
    // Remove Permission from Group
  ]
};

function ensureGroupHasPermission(
  test: Test, doc: DocumentPermissionDoc, userGroup: UserGroup, permission: string
){
  test.not(
    Object.keys(doc.group).length, 0,
    "There should be a group on the permission"
  );
  test.ok(
    userGroup.slug in doc.group,
    "The group slug should be available on the permission"
  );
  test.ok(
    doc.group[userGroup.slug].permissions.includes(permission),
    "The group slug should have the added permission"
  );
}
