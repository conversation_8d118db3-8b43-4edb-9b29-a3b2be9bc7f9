import { AuthFetch } from "../../../../../globals/auth0";
import { getPermission, setAnonymousPermission } from "../../actions";
import { PermissionRunner } from "../../types";
export const AnonymousPermission: PermissionRunner<void> = {
  key: "anonymous",
  isAllowed: (doc, permission)=>(doc.anonymousRestriction.includes(permission)),
  async addPermissionForUser(test, { admin }, permissionUrl: string, permission: string){
    const { doc: prev } = await getPermission(admin, permissionUrl);
    test.notOk(
      prev.anonymousPermissions.permissions.includes(permission),
      "Should not have permission"
    );

    await setAnonymousPermission(admin, permissionUrl, [permission]);

    const { doc: after } = await getPermission(admin, permissionUrl);
    test.ok(
      after.anonymousPermissions.permissions.includes(permission),
      "Should have permission"
    );

    return { config: void 0, admin, user: createAnonymousFetcher() };
  },
  removePermissionForUser: [{
    name: "Set Anonymous Permission to Empty",
    runner: async (test, { admin }, permissionUrl: string, permission: string)=>{
      const { doc: prev } = await getPermission(admin, permissionUrl);
      test.ok(
        prev.anonymousPermissions.permissions.includes(permission),
        "Should have permission"
      );

      await setAnonymousPermission(
        admin, permissionUrl,
        prev.anonymousPermissions.permissions.filter((p)=>p !== permission)
      );

      const { doc: after } = await getPermission(admin, permissionUrl);
      test.notOk(
        after.anonymousPermissions.permissions.includes(permission),
        "Should not have permission"
      );
      return { admin, user: createAnonymousFetcher() };
    }
  }]
};

function createAnonymousFetcher(): AuthFetch{
  const anon = fetch as AuthFetch;
  anon.userId = "anonymous";
  anon.email = "";
  anon.username = "";
  anon.password = "";
  return anon;
}