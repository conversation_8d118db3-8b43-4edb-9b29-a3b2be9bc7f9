
import { AuthFetch } from "../../../../../globals/auth0";
import { getPermission, inviteNewOwnerForPermission, acceptNewOwnerForPermission } from "../../actions";
import { PermissionRunner } from "../../types";
export const OwnerPermission: PermissionRunner<AuthFetch> = {
  key: "change owner",
  isAllowed: ()=>(true),
  async addPermissionForUser(test, { admin, user }, permissionUrl: string){
    const { doc: prev } = await getPermission(admin, permissionUrl);
    if(prev.ownerUser === user.userId){
      throw new Error("User already owner");
    }

    await inviteNewOwnerForPermission(admin, permissionUrl, user.email);
    await acceptNewOwnerForPermission(user, permissionUrl);

    const { doc: after } = await getPermission(user, permissionUrl);
    test.equal(after.ownerUser, user.userId, "New owner should be set");

    return { config: admin, admin: user, user };
  },
  removePermissionForUser: [{
    name: "Change Owner back to old Owner",
    runner: async (test, { admin }, permissionUrl: string, permission, originalAdmin)=>{
      test.not(admin.userId, originalAdmin.userId, "new Admin and old Admin should be different");

      const { doc: prev } = await getPermission(admin, permissionUrl);
      test.equal(prev.ownerUser, admin.userId, "current owner is expected to be the new admin");

      await inviteNewOwnerForPermission(admin, permissionUrl, originalAdmin.email);
      await acceptNewOwnerForPermission(originalAdmin, permissionUrl);

      const { doc: after } = await getPermission(originalAdmin, permissionUrl);
      test.equal(after.ownerUser, originalAdmin.userId, "New owner should be set");
      return { admin: originalAdmin, user: admin };
    }
  }]
};
