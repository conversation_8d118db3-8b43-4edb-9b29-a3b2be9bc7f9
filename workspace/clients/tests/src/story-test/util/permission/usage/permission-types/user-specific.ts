
import { Test } from "tap";
import { PermissionRunner, PermissionUser } from "../../types";

import {
  getPermission,
  addUserInviteToPermission, acceptUserInviteToPermission,
  updateUserPermission, removeUserPermission
} from "../../actions";

export const UserPermission: PermissionRunner<PermissionUser> = {
  key: "user",
  isAllowed: (doc, permission)=>(doc.userRestriction.includes(permission)),
  async addPermissionForUser(test, { admin, user }, permissionUrl, permission){
    const { doc: prev } = await getPermission(admin, permissionUrl);
    test.equal(
      Object.keys(prev.userInvitations).length, 0,
      "There should be no user invitations on the permission"
    );
    test.equal(
      Object.keys(prev.userPermissions).length, 0,
      "There should be no users on the permission"
    );

    await addUserInviteToPermission(admin, permissionUrl, user, [permission]);

    const { doc: invite } = await getPermission(admin, permissionUrl);
    ensureInviteHasPermission(test, invite, user, permission);
    test.equal(
      Object.keys(invite.userPermissions).length, 0,
      "There should be no users on the permission"
    );

    await acceptUserInviteToPermission(user, permissionUrl);

    const { doc: accept } = await getPermission(admin, permissionUrl);
    test.equal(
      Object.keys(accept.userInvitations).length, 0,
      "There should be no user invitations on the permission"
    );
    ensureUserHasPermission(test, accept, user, permission);

    return { config: user, admin, user };
  },
  removePermissionForUser: [
    {
      name: "Remove the whole user from the permission",
      runner: async (test, { admin, user }, permissionUrl, permission, addedUser)=>{
        const { doc: prev } = await getPermission(admin, permissionUrl);
        ensureUserHasPermission(test, prev, addedUser, permission);

        await removeUserPermission(admin, permissionUrl, addedUser);

        const { doc: after } = await getPermission(admin, permissionUrl);
        test.equal(
          Object.keys(after.userPermissions).length, 0,
          "There should be no users on the permission"
        );
        return { admin, user };
      }
    },
    {
      name: "Set User Permission to Empty",
      runner: async (test, { admin, user }, permissionUrl, permission, addedUser)=>{
        const { doc: prev } = await getPermission(admin, permissionUrl);
        ensureUserHasPermission(test, prev, addedUser, permission);

        await updateUserPermission(admin, permissionUrl, addedUser, []);

        const { doc: after } = await getPermission(admin, permissionUrl);
        test.equal(
          Object.keys(after.userPermissions).length, 1,
          "There should be a user on the permission"
        );
        const found = Object.values(after.userPermissions).find((docUser)=>docUser.userId === addedUser.userId);
        test.ok(
          found, "The user on the permission should be associated with the expected user"
        );
        if(typeof found === "undefined"){
          throw new Error("User not found");
        }
        test.notOk(
          found.permissions.includes(permission),
          "The user should not have the expected permission"
        );
        return { admin, user };
      }
    }
  ]
};
import { DocumentPermissionDoc } from "../../types";
function ensureInviteHasPermission(test: Test, doc: DocumentPermissionDoc, user: PermissionUser, permission: string){
  test.equal(
    Object.keys(doc.userInvitations).length, 1,
    "There should be an invite on the permission"
  );
  const found = Object.values(doc.userInvitations).find((invite)=>invite.email === user.email);
  test.ok(
    found, "The invite on the permission should be associated with the expected user"
  );
  if(typeof found === "undefined"){
    throw new Error("User not found");
  }
  test.ok(
    found.permissions.includes(permission),
    "The invite should have the expected permission"
  );
}

function ensureUserHasPermission(test: Test, doc: DocumentPermissionDoc, user: PermissionUser, permission: string){
  test.equal(
    Object.keys(doc.userPermissions).length, 1,
    "There should be a user on the permission"
  );
  const found = Object.values(doc.userPermissions).find((docUser)=>docUser.userId === user.userId);
  test.ok(
    found, "The user on the permission should be associated with the expected user"
  );
  if(typeof found === "undefined"){
    throw new Error("User not found");
  }
  test.ok(
    found.permissions.includes(permission),
    "The user should have the expected permission"
  );
}
