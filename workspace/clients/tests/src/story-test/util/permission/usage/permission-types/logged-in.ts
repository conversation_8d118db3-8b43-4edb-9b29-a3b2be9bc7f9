
import { getPermission, setLoggedInPermission } from "../../actions";
import { PermissionRunner } from "../../types";

export const LoggedInPermission: PermissionRunner<void> = {
  key: "logged in",
  isAllowed: (doc, permission)=>(doc.loggedInRestriction.includes(permission)),
  async addPermissionForUser(test, { admin, user }, permissionUrl: string, permission: string){
    const { doc: prev } = await getPermission(admin, permissionUrl);
    test.notOk(
      prev.loggedInPermissions.permissions.includes(permission),
      "Should not have permission"
    );

    await setLoggedInPermission(admin, permissionUrl, [permission]);

    const { doc: after } = await getPermission(admin, permissionUrl);
    test.ok(
      after.loggedInPermissions.permissions.includes(permission),
      "Should have permission"
    );

    return { config: void 0, admin, user: user };
  },
  removePermissionForUser: [
    {
      name: "Set Logged In Permission to Empty",
      runner: async (test, { admin, user }, permissionUrl: string, permission: string)=>{
        const { doc: prev } = await getPermission(admin, permissionUrl);
        test.ok(
          prev.loggedInPermissions.permissions.includes(permission),
          "Should have permission"
        );
        await setLoggedInPermission(
          admin, permissionUrl,
          prev.loggedInPermissions.permissions.filter((p)=>p !== permission)
        );
        const { doc: after } = await getPermission(admin, permissionUrl);
        test.notOk(
          after.loggedInPermissions.permissions.includes(permission),
          "Should not have permission"
        );
        return { admin, user };
      }
    }
  ]
};

