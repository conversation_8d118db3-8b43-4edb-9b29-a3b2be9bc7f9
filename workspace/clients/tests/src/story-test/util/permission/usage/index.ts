
import { Test } from "tap";
import { Doc } from "@divinci-ai/models";
import { getUsers } from "../../user";
import { DocConfig, TestFn, PermissionUsers } from "../types";

import { PERMISSION_RUNNERS } from "./permission-types";
import { UserPermission } from "./permission-types";

import { testDocPermissions } from "./test-doc-permissions";

import { getPermission } from "../actions";
import { getSkipPermission } from "../../../../globals/test-config";

export async function testPermissions<TestDoc extends Doc<any>>(
  docT: Test,
  docConfig: DocConfig<TestDoc>,
  tests: Array<TestFn<TestDoc>>,
){
  if(getSkipPermission()){
    return docT.comment("Skipping Permission Tests");
  }
  const [admin, runner] = await getUsers(2);
  const promises: Array<Promise<any>> = [];

  for(const runnableTest of tests){
    promises.push(docT.test(runnableTest.name, async (permissionTest)=>{
      for(const permissionRunner of PERMISSION_RUNNERS){
        permissionTest.test(`Using Permission ${permissionRunner.key}`, async (fnTest)=>{
          const doc = await docConfig.createDoc(admin);
          const { doc: permissions } = await getPermission(admin, docConfig.docToURLPrefix(doc));
          const isAllowed = permissionRunner.isAllowed(permissions, runnableTest.permission);
          if(!isAllowed){
            fnTest.comment("Can't add Permission for user");
          } else {
            fnTest.comment("Will add Permission for user");
          }
          return fnTest.test({ silent: true }, (childTest)=>(
            testDocPermissions(
              childTest, permissionRunner, docConfig, runnableTest,
              admin, runner,
              isAllowed
            )
          ));
        });
      }
      permissionTest.end();
    }));
  }

  await Promise.all(promises);
}

export async function runNormalTest(
  parentTest: Test, users: PermissionUsers,
  docConfig: DocConfig<any>, runnableTest: TestFn<any>,
){
  const permissionRunner = UserPermission;
  const doc = await docConfig.createDoc(users.admin);
  const permissionUrl = docConfig.docToURLPrefix(doc);

  await parentTest.test("Adding permission for user", { silent: true }, async (test)=>{
    await permissionRunner.addPermissionForUser(
      test, users, permissionUrl, runnableTest.permission
    );
  });
  await runnableTest.fn(parentTest, users.admin, users.user, doc);
}
