import { Test } from "tap";
import { AuthFetch, } from "../../../globals/auth0";
import { DocPermissionType_Client } from "@divinci-ai/models";

export type PermissionUser = { email: string, userId: string };

export type DocumentPermissionDoc = DocPermissionType_Client & { _id: string };

export type PermissionUsers = { admin: AuthFetch, user: AuthFetch };
export type PermissionConfig = { permissionUrl: string, permission: string };
export type Action = (test: Test, user: AuthFetch) => Promise<void>;

export type PermissionRunner<AddConfig> = {
  key: string,
  isAllowed: (doc: DocPermissionType_Client, permission: string) => boolean,
  addPermissionForUser(
    test: Test,
    users: PermissionUsers,
    permissionUrl: string, permission: string
  ): Promise<{ config: AddConfig, admin: AuthFetch, user: AuthFetch }>,
  removePermissionForUser: Array<
    {
      name: string,
      runner(
        test: Test,
        users: PermissionUsers,
        permissionUrl: string, permission: string,
        addConfig: AddConfig
      ): Promise<{ admin: AuthFetch, user: AuthFetch }>
    }
  >,
};

export type DocConfig<TestDoc extends Doc<any>> = {
  createDoc: (admin: AuthFetch)=>Promise<TestDoc>,
  docToURLPrefix: (doc: TestDoc)=>string,
};

import { Doc } from "@divinci-ai/models";

export type TestFn<TestDoc extends Doc<any>> = {
  name: string, permission: string,
  fn: (fnTest: Test, adminFetch: AuthFetch, runner: AuthFetch, doc: TestDoc)=>Promise<any>
};
