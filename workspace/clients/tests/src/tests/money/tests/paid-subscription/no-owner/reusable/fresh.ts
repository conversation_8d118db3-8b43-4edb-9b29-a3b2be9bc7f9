
import { AuthFetch } from "../../../../../../globals/auth0";

import {
  getTranscriptValueTransactions, getTranscriptValueWallet,
} from "../../../../../../test-prep";

import { freshTranscriptWallet } from "../../../../validate/wallets/fresh-wallet";
import { validateNoSubscription } from "../../../../validate/subscriptions/no-subscription";
import { freshTransactionList } from "../../../../validate/transactions/fresh-transactions";

import { noWithdrawables } from "../../../../validate/withdrawable/no-withdrawable";

import { Test } from "tap";
import { transactionsOfOwnerAreRunner } from "../../../../validate/transactions/owner-is-runner";
import { validatePaidSubscription } from "../../../../validate/subscriptions/active-renew-subscription";


export async function isFresh(test: Test, userFetch: AuthFetch){
  const [wallet, transactions] = await Promise.all([
    getTranscriptValueWallet(userFetch),
    getTranscriptValueTransactions(userFetch),
  ]);
  test.pass("able to findOrCreate fresh wallet");

  test.pass("able to get transactions of user");

  test.test("has fresh wallet", (t)=>{
    freshTranscriptWallet(t, wallet.wallet);
    t.end();
  });

  test.test("no transactions", (t)=>{
    transactionsOfOwnerAreRunner(t, transactions);
    freshTransactionList(t, userFetch, transactions.owner);
    t.end();
  });

  await test.test("no withdrawables", async (t)=>{
    await noWithdrawables(t, userFetch);
    t.end();
  });
}
