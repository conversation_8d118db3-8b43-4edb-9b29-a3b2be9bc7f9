/**
 * Resource Pool
 * 
 * This module provides a generic resource pool implementation for managing
 * test resources efficiently.
 */

/**
 * Interface for a pooled resource
 */
export interface PooledResource<T> {
  /** The actual resource */
  resource: T;
  /** Whether the resource is currently in use */
  inUse: boolean;
  /** Timestamp when the resource was last used */
  lastUsed: number;
  /** Timestamp when the resource was created */
  createdAt: number;
  /** Key used to identify the resource */
  key: string;
}

/**
 * Options for the resource pool
 */
export interface ResourcePoolOptions {
  /** Maximum number of resources to keep in the pool */
  maxSize?: number;
  /** Maximum age of resources in milliseconds before they are cleaned up */
  maxAge?: number;
  /** Interval in milliseconds for cleaning up expired resources */
  cleanupIntervalMs?: number;
  /** Whether to log debug information */
  debug?: boolean;
}

/**
 * Generic resource pool for managing test resources
 */
export class ResourcePool<T> {
  /** Map of resources by key */
  private resources: Map<string, PooledResource<T>> = new Map();
  /** Factory function for creating new resources */
  private factory: (key: string) => Promise<T>;
  /** Cleanup function for disposing of resources */
  private cleanup: (resource: T) => Promise<void>;
  /** Maximum number of resources to keep in the pool */
  private maxSize: number;
  /** Maximum age of resources in milliseconds before they are cleaned up */
  private maxAge: number;
  /** Interval for cleaning up expired resources */
  private cleanupInterval: NodeJS.Timeout | null = null;
  /** Whether to log debug information */
  private debug: boolean;

  /**
   * Create a new resource pool
   * @param factory Factory function for creating new resources
   * @param cleanup Cleanup function for disposing of resources
   * @param options Options for the resource pool
   */
  constructor(
    factory: (key: string) => Promise<T>,
    cleanup: (resource: T) => Promise<void>,
    options?: ResourcePoolOptions
  ) {
    this.factory = factory;
    this.cleanup = cleanup;
    this.maxSize = options?.maxSize ?? 10;
    this.maxAge = options?.maxAge ?? 30 * 60 * 1000; // 30 minutes
    this.debug = options?.debug ?? false;

    // Start the cleanup interval
    const cleanupIntervalMs = options?.cleanupIntervalMs ?? 5 * 60 * 1000; // 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredResources();
    }, cleanupIntervalMs);
  }

  /**
   * Get the number of resources in the pool
   * @returns The number of resources in the pool
   */
  size(): number {
    return this.resources.size;
  }

  /**
   * Get the number of resources currently in use
   * @returns The number of resources in use
   */
  inUse(): number {
    let count = 0;
    for (const resource of this.resources.values()) {
      if (resource.inUse) {
        count++;
      }
    }
    return count;
  }

  /**
   * Get a resource from the pool
   * @param key The key to identify the resource
   * @returns A promise that resolves to the resource
   */
  async get(key: string): Promise<T> {
    // Check if the resource already exists in the pool
    const existingResource = this.resources.get(key);
    if (existingResource) {
      // If the resource is already in use, wait for it to be released
      if (existingResource.inUse) {
        this.log(`Resource ${key} is already in use, waiting for it to be released`);
        
        // Wait for the resource to be released
        await this.waitForResource(key);
        
        // Try to get the resource again
        return this.get(key);
      }

      // Mark the resource as in use
      existingResource.inUse = true;
      existingResource.lastUsed = Date.now();
      
      this.log(`♻️ Reusing resource: ${key}`);
      
      return existingResource.resource;
    }

    // If the pool is full and all resources are in use, wait for a resource to be released
    if (this.resources.size >= this.maxSize && this.inUse() >= this.maxSize) {
      this.log(`⏳ Waiting for resource to become available (pool size: ${this.maxSize})`);
      
      // Wait for any resource to be released
      await this.waitForAnyResource();
      
      // Try to get the resource again
      return this.get(key);
    }

    // Create a new resource
    this.log(`🆕 Creating new resource: ${key}`);
    
    try {
      const resource = await this.factory(key);
      
      // Add the resource to the pool
      this.resources.set(key, {
        resource,
        inUse: true,
        lastUsed: Date.now(),
        createdAt: Date.now(),
        key
      });
      
      return resource;
    } catch (error) {
      this.log(`❌ Error creating resource ${key}: ${error}`);
      throw error;
    }
  }

  /**
   * Release a resource back to the pool
   * @param key The key of the resource to release
   */
  release(key: string): void {
    const resource = this.resources.get(key);
    if (resource) {
      resource.inUse = false;
      resource.lastUsed = Date.now();
      this.log(`🔄 Released resource: ${key}`);
    } else {
      this.log(`⚠️ Attempted to release non-existent resource: ${key}`);
    }
  }

  /**
   * Remove a resource from the pool
   * @param key The key of the resource to remove
   */
  async remove(key: string): Promise<void> {
    const resource = this.resources.get(key);
    if (resource) {
      try {
        // Clean up the resource
        await this.cleanup(resource.resource);
        
        // Remove the resource from the pool
        this.resources.delete(key);
        
        this.log(`🗑️ Removed resource: ${key}`);
      } catch (error) {
        this.log(`❌ Error removing resource ${key}: ${error}`);
        throw error;
      }
    }
  }

  /**
   * Clear all resources from the pool
   */
  async clear(): Promise<void> {
    // Clean up all resources
    for (const [key, resource] of this.resources.entries()) {
      try {
        await this.cleanup(resource.resource);
      } catch (error) {
        this.log(`❌ Error cleaning up resource ${key}: ${error}`);
      }
    }
    
    // Clear the pool
    this.resources.clear();
    
    this.log(`🧹 Cleared resource pool`);
  }

  /**
   * Dispose of the resource pool
   */
  async dispose(): Promise<void> {
    // Clear all resources
    await this.clear();
    
    // Stop the cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * Clean up expired resources
   */
  private async cleanupExpiredResources(): Promise<void> {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    // Find expired resources
    for (const [key, resource] of this.resources.entries()) {
      // Skip resources that are in use
      if (resource.inUse) {
        continue;
      }
      
      // Check if the resource has expired
      if (now - resource.lastUsed > this.maxAge) {
        expiredKeys.push(key);
      }
    }
    
    // Remove expired resources
    for (const key of expiredKeys) {
      try {
        await this.remove(key);
      } catch (error) {
        this.log(`❌ Error removing expired resource ${key}: ${error}`);
      }
    }
    
    if (expiredKeys.length > 0) {
      this.log(`🧹 Cleaned up ${expiredKeys.length} expired resources`);
    }
  }

  /**
   * Wait for a specific resource to be released
   * @param key The key of the resource to wait for
   * @returns A promise that resolves when the resource is released
   */
  private async waitForResource(key: string): Promise<void> {
    return new Promise<void>((resolve) => {
      const checkInterval = setInterval(() => {
        const resource = this.resources.get(key);
        if (resource && !resource.inUse) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
    });
  }

  /**
   * Wait for any resource to be released
   * @returns A promise that resolves when any resource is released
   */
  private async waitForAnyResource(): Promise<void> {
    return new Promise<void>((resolve) => {
      const checkInterval = setInterval(() => {
        for (const resource of this.resources.values()) {
          if (!resource.inUse) {
            clearInterval(checkInterval);
            resolve();
            return;
          }
        }
      }, 100);
    });
  }

  /**
   * Log a message if debug is enabled
   * @param message The message to log
   */
  private log(message: string): void {
    if (this.debug) {
      console.log(message);
    } else {
      // Always log certain messages
      if (
        message.startsWith('🆕') ||
        message.startsWith('♻️') ||
        message.startsWith('🔄') ||
        message.startsWith('🧹') ||
        message.startsWith('⏳')
      ) {
        console.log(message);
      }
    }
  }
}
