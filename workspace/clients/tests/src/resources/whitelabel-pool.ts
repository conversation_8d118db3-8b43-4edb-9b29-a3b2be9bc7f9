/**
 * Whitelabel Pool
 * 
 * This module provides a resource pool for whitelabels.
 */

import { ResourcePool } from './resource-pool';
import { Whitelabel, createWhitelabel, deleteWhitelabel } from '../api/whitelabel-api';
import { UserRole } from '../auth/auth-utils';
import { config } from '../config/config';
import { randomName } from '../utils/test-utils';
import { getUserGroup, releaseUserGroup } from './user-group-pool';

/**
 * Whitelabel pool
 */
export const whitelabelPool = new ResourcePool<Whitelabel>(
  async (key: string) => {
    // Parse the key to get the role, group ID, and name
    const [role, groupId, name] = key.split('::');
    
    // If no group ID is provided, get a group from the pool
    let actualGroupId = groupId;
    let usePooledGroup = false;
    
    if (!actualGroupId || actualGroupId === 'auto') {
      usePooledGroup = true;
      const group = await getUserGroup(role as User<PERSON>ole);
      actualGroupId = group._id;
    }
    
    try {
      // Create a whitelabel with the specified role, group ID, and name
      const whitelabel = await createWhitelabel(
        role as UserRole,
        {
          name: name || randomName('test-whitelabel'),
          groupId: actualGroupId
        }
      );
      
      return whitelabel;
    } finally {
      // Release the group back to the pool if we got it from there
      if (usePooledGroup) {
        releaseUserGroup(role as UserRole);
      }
    }
  },
  async (whitelabel: Whitelabel) => {
    // Delete the whitelabel
    try {
      // Use the admin role to ensure we have permission to delete
      await deleteWhitelabel('admin', whitelabel._id);
    } catch (error) {
      console.error(`Error deleting whitelabel ${whitelabel._id}: ${error}`);
    }
  },
  {
    maxSize: config.resourcePool.maxSize,
    maxAge: config.resourcePool.maxAge,
    cleanupIntervalMs: config.resourcePool.cleanupIntervalMs,
    debug: config.resourcePool.debug
  }
);

/**
 * Get a whitelabel from the pool
 * @param role User role
 * @param groupId Group ID (optional, if not provided a group will be created)
 * @param name Whitelabel name (optional)
 * @returns A promise that resolves with a whitelabel
 */
export async function getWhitelabel(
  role: UserRole,
  groupId?: string,
  name?: string
): Promise<Whitelabel> {
  const key = `${role}::${groupId || 'auto'}::${name || ''}`;
  return whitelabelPool.get(key);
}

/**
 * Release a whitelabel back to the pool
 * @param role User role
 * @param groupId Group ID (optional)
 * @param name Whitelabel name (optional)
 */
export function releaseWhitelabel(
  role: UserRole,
  groupId?: string,
  name?: string
): void {
  const key = `${role}::${groupId || 'auto'}::${name || ''}`;
  whitelabelPool.release(key);
}

/**
 * Remove a whitelabel from the pool
 * @param role User role
 * @param groupId Group ID (optional)
 * @param name Whitelabel name (optional)
 * @returns A promise that resolves when the whitelabel is removed
 */
export async function removeWhitelabel(
  role: UserRole,
  groupId?: string,
  name?: string
): Promise<void> {
  const key = `${role}::${groupId || 'auto'}::${name || ''}`;
  await whitelabelPool.remove(key);
}

/**
 * Clear all whitelabels from the pool
 * @returns A promise that resolves when all whitelabels are cleared
 */
export async function clearWhitelabels(): Promise<void> {
  await whitelabelPool.clear();
}
