/**
 * User Group Pool
 * 
 * This module provides a resource pool for user groups.
 */

import { ResourcePool } from './resource-pool';
import { UserGroup, createUserGroup, deleteUserGroup } from '../api/user-group-api';
import { UserRole } from '../auth/auth-utils';
import { config } from '../config/config';
import { randomName } from '../utils/test-utils';

/**
 * User group pool
 */
export const userGroupPool = new ResourcePool<UserGroup>(
  async (key: string) => {
    // Parse the key to get the role and name
    const [role, name] = key.split('::');
    
    // Create a user group with the specified role and name
    return createUserGroup(role as UserRole, name || randomName('test-group'));
  },
  async (userGroup: UserGroup) => {
    // Delete the user group
    try {
      // Use the admin role to ensure we have permission to delete
      await deleteUserGroup('admin', userGroup._id);
    } catch (error) {
      console.error(`Error deleting user group ${userGroup._id}: ${error}`);
    }
  },
  {
    maxSize: config.resourcePool.maxSize,
    maxAge: config.resourcePool.maxAge,
    cleanupIntervalMs: config.resourcePool.cleanupIntervalMs,
    debug: config.resourcePool.debug
  }
);

/**
 * Get a user group from the pool
 * @param role User role
 * @param name User group name (optional)
 * @returns A promise that resolves with a user group
 */
export async function getUserGroup(role: UserRole, name?: string): Promise<UserGroup> {
  const key = `${role}::${name || ''}`;
  return userGroupPool.get(key);
}

/**
 * Release a user group back to the pool
 * @param role User role
 * @param name User group name (optional)
 */
export function releaseUserGroup(role: UserRole, name?: string): void {
  const key = `${role}::${name || ''}`;
  userGroupPool.release(key);
}

/**
 * Remove a user group from the pool
 * @param role User role
 * @param name User group name (optional)
 * @returns A promise that resolves when the user group is removed
 */
export async function removeUserGroup(role: UserRole, name?: string): Promise<void> {
  const key = `${role}::${name || ''}`;
  await userGroupPool.remove(key);
}

/**
 * Clear all user groups from the pool
 * @returns A promise that resolves when all user groups are cleared
 */
export async function clearUserGroups(): Promise<void> {
  await userGroupPool.clear();
}
