import { test, expect } from '@playwright/test';
import { verifyLoggedIn, ensureLoggedIn, navigateToWhitelabelPage } from './utils/common';

/**
 * E2E test for creating a new whitelabel
 * This test verifies that a user can create a new whitelabel
 */
test.describe('Create Whitelabel', () => {
  test('should create a new whitelabel successfully', async ({ page }) => {
    console.log('Starting create whitelabel test');

    // Ensure the user is logged in
    await ensureLoggedIn(page);

    // Navigate to the whitelabel page
    await navigateToWhitelabelPage(page);

    // Take a screenshot of the whitelabel page
    await page.screenshot({ path: 'whitelabel-page.png' });
    console.log('Saved screenshot of whitelabel page');

    // Click on Create Whitelabel button
    console.log('Clicking on Create Whitelabel button');
    await page.click('text=Create Whitelabel');

    // Wait for the create whitelabel form to load
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the create whitelabel form
    await page.screenshot({ path: 'create-whitelabel-form.png' });
    console.log('Saved screenshot of create whitelabel form');

    // Generate a unique name for the whitelabel
    const timestamp = new Date().getTime();
    const whitelabelName = `Test Whitelabel ${timestamp}`;

    // Fill out the whitelabel form
    console.log('Filling out the whitelabel form');
    await page.fill('input.input[type="text"]', whitelabelName);
    await page.fill('textarea#description', 'This is a test whitelabel created by Playwright E2E test');

    // Check the terms and conditions checkbox
    await page.check('input[type="checkbox"]');

    // Take a screenshot of the filled form
    await page.screenshot({ path: 'filled-whitelabel-form.png' });
    console.log('Saved screenshot of filled whitelabel form');

    // Click the Create AI button
    console.log('Clicking Create AI button');
    await page.click('button.create-whitelabel-button');

    // Wait for the whitelabel to be created and redirected
    await page.waitForLoadState('networkidle');

    // Take a screenshot after creation
    await page.screenshot({ path: 'after-whitelabel-creation.png' });
    console.log('Saved screenshot after whitelabel creation');

    // Verify that the whitelabel was created successfully
    // We should be on the whitelabel detail page
    await expect(page.locator('h1:has-text("Whitelabel")')).toBeVisible();

    // The whitelabel name should be visible on the page
    await expect(page.locator(`text=${whitelabelName}`)).toBeVisible();

    console.log('Create whitelabel test completed successfully');

    // Return the whitelabel name for use in other tests
    return { whitelabelName };
  });
});
