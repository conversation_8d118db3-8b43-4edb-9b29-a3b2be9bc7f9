# E2E Test Screenshots

This directory contains End-to-End (E2E) tests for the application. All test screenshots are automatically organized into `screenshots` folders next to their respective test files.

## Screenshot Organization

### ✅ Correct Structure
```
src/e2e/
├── audio-rag-status.spec.ts
├── screenshots/
│   ├── login-page.png
│   ├── audio-transcript-rag-page.png
│   └── rag-file-content.png
├── resource-intensive/
│   ├── audio-transcript-to-fine-tune.spec.ts
│   └── screenshots/
│       ├── home-page.png
│       ├── fine-tune-page.png
│       └── after-creating-fine-tune.png
└── README.md
```

### ❌ Incorrect Structure (Avoid)
```
/ (root directory)
├── missing-audio-file-link.png  ❌ Don't save here!
├── rag-file-content.png         ❌ Don't save here!
└── workspace/
    └── clients/
        └── tests/
            └── src/
                └── e2e/
                    └── test.spec.ts
```

## How to Use Screenshots in Tests

### Option 1: Use the Screenshot Utility (Recommended)

```typescript
import { test, expect } from '@playwright/test';
import { createScreenshotPathFactory } from '../utils/screenshot';

// Create a screenshot path factory for this test file
const getScreenshotPath = createScreenshotPathFactory(__dirname);

test('My test', async ({ page }) => {
  // Screenshots will be saved in ./screenshots/ directory
  await page.screenshot({ path: getScreenshotPath('my-screenshot.png') });
});
```

### Option 2: Manual Path Construction

```typescript
import { test, expect } from '@playwright/test';
import { getScreenshotPath } from '../utils/screenshot';

test('My test', async ({ page }) => {
  // Screenshots will be saved in ./screenshots/ directory
  await page.screenshot({ path: getScreenshotPath(__dirname, 'my-screenshot.png') });
});
```

## Benefits of This Organization

1. **🗂️ Clean Repository**: No screenshot files cluttering the root directory
2. **📁 Easy to Find**: Screenshots are always next to their test files
3. **🔍 Better Debugging**: Clear relationship between tests and their artifacts
4. **🧹 Easy Cleanup**: Delete test directory to remove all related files
5. **👥 Team Friendly**: Consistent structure for all developers

## Screenshot Naming Conventions

Use descriptive names that indicate:
- **When** the screenshot was taken: `before-login.png`, `after-submit.png`
- **What** is shown: `login-form.png`, `error-message.png`, `success-page.png`
- **Which step**: `step-1-upload.png`, `step-2-process.png`

### Good Examples
- `login-page.png`
- `after-file-upload.png`
- `rag-file-creation-form.png`
- `audio-transcript-list-final-state.png`

### Avoid
- `screenshot1.png`
- `test.png`
- `image.png`
- `temp.png`

## Automatic Directory Creation

The screenshot utility automatically creates the `screenshots` directory if it doesn't exist, so you don't need to manually create it.

## Gitignore Considerations

Consider adding screenshot directories to `.gitignore` if they're only for local debugging:

```gitignore
# E2E Test Screenshots (if not needed in repo)
**/screenshots/*.png
```

Or keep them if they're useful for documentation and debugging in CI/CD.
