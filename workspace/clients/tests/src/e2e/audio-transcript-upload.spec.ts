import { test, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';
import {
  verifyLoggedIn,
  ensureLoggedIn,
  navigateToWhitelabelPage,
  getFirstWhitelabelId,
  navigateToAudioTranscriptPage,
  ensureWhitelabelExists
} from './utils/common';

/**
 * E2E test for uploading a zipped media file for audio transcription
 * This test verifies the flow of uploading a zipped media file
 * and checking that it's processed correctly
 */
test.describe('Audio Transcript Upload', () => {
  // Define the path to the test zip file (using a smaller file to reduce load on the Pyannote service)
  const zipFilePath = path.resolve(__dirname, '../../test-files/small-audio-test.zip');

  // Before running tests, check that the test file exists
  test.beforeAll(() => {
    // Verify that the test file exists
    expect(fs.existsSync(zipFilePath)).toBeTruthy();
  });

  // Increase the test timeout to 5 minutes (300000ms) since audio processing takes time
  test('should upload a zipped media file and process it correctly', async ({ page }) => {
    // Set a longer timeout for this test
    test.setTimeout(300000);
    console.log('Starting audio transcript upload test');

    // Ensure the user is logged in
    await ensureLoggedIn(page);

    console.log('Taking a more direct approach to navigate to audio transcript page');

    // Navigate directly to the whitelabel page
    console.log('Navigating directly to whitelabel page');
    await page.goto('http://localhost:8080/white-label');
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the whitelabel page
    await page.screenshot({ path: 'direct-whitelabel-page.png' });

    // Check if there are any whitelabels using the correct selector
    const existingWhitelabels = await page.locator('.whitelabel-ai-card').count();
    console.log(`Found ${existingWhitelabels} existing whitelabels`);

    let whitelabelId;

    if (existingWhitelabels === 0) {
      console.log('No whitelabels found. Creating a new one.');

      // We're already on the whitelabel page with the form
      console.log('We are on the whitelabel page with the form');

      // Take a screenshot of the current page
      await page.screenshot({ path: 'whitelabel-page.png' });
      console.log('Saved screenshot of whitelabel page');

      // Generate a unique name for the whitelabel
      const timestamp = new Date().getTime();
      const whitelabelName = `Test Whitelabel ${timestamp}`;

      // Fill out the form using the exact selectors from the HTML
      console.log('Filling out the whitelabel form');

      // Fill in the title field
      console.log('Filling title field');
      await page.fill('div.control > input.input[required]', whitelabelName);

      // Fill in the description field
      console.log('Filling description field');
      await page.fill('textarea#description.textarea', 'This is a test whitelabel created for audio transcript testing');

      // Check the terms checkbox
      console.log('Checking terms checkbox');
      await page.check('input[type="checkbox"][required]');

      // Take a screenshot before submission
      await page.screenshot({ path: 'filled-whitelabel-form.png' });
      console.log('Saved screenshot of filled whitelabel form');

      // Click the Create AI button
      console.log('Clicking Create AI button');
      await page.click('button.button.is-link.is-success.is-outlined.create-whitelabel-button');
      await page.waitForLoadState('networkidle');

      // Wait a moment for the server to process the whitelabel creation
      console.log('Waiting briefly for server to process whitelabel creation');
      await page.waitForTimeout(3000);

      // Navigate back to the whitelabel list
      console.log('Navigating back to whitelabel list');
      await page.goto('http://localhost:8080/white-label');
      await page.waitForLoadState('networkidle');

      // Take a screenshot of the whitelabel list
      await page.screenshot({ path: 'whitelabel-list-after-creation.png' });
      console.log('Saved screenshot of whitelabel list after creation');
    }

    // Get the first whitelabel ID using the correct selector
    const whitelabelHref = await page.locator('.whitelabel-ai-card .title a.clickable-text').first().getAttribute('href');
    whitelabelId = whitelabelHref.split('/').pop();
    console.log(`Using whitelabel ID: ${whitelabelId}`);

    // Navigate directly to the audio transcript page
    console.log('Navigating directly to audio transcript page');
    await page.goto(`http://localhost:8080/white-label/${whitelabelId}/audio-transcript`);
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the audio transcript page
    await page.screenshot({ path: 'direct-audio-transcript-page.png' });
    console.log('Saved screenshot of audio transcript page');

    // Click on Create Tools link
    console.log('Clicking on Create Tools link');
    await page.click('a:has-text("Create Tools")');

    // Wait for the create tools page to load
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the create tools page
    await page.screenshot({ path: 'create-tools-page.png' });
    console.log('Saved screenshot of create tools page');

    // Set the file input to our test zip file
    console.log('Uploading test zip file');
    const fileInput = await page.locator('input[type="file"]');
    await fileInput.setInputFiles(zipFilePath);

    // Wait for the file to be processed and displayed
    await page.waitForTimeout(2000); // Give it a moment to process the file
    console.log('File uploaded, waiting for processing');

    // Wait for the select elements to be visible
    console.log('Waiting for tool select elements to be visible');
    await page.waitForTimeout(2000); // Give the page time to load the selects

    // Take a screenshot to see the current state
    await page.screenshot({ path: 'before-select-tools.png' });
    console.log('Saved screenshot before selecting tools');

    // Select the specific tools required for local testing
    console.log('Selecting specific tools for local testing');

    // Get all select elements
    const selects = await page.locator('select').all();
    console.log(`Found ${selects.length} select elements`);

    if (selects.length < 2) {
      console.error('Could not find both diarization and transcription select elements');
      throw new Error('Required select elements not found - test cannot continue');
    }

    // Find and select Divinci Pyannote for diarization
    console.log('Selecting Divinci Pyannote for diarization');
    const divinciPyannoteDiarizationValue = '@divinci-ai/pyannote-segmentation';
    await selects[0].selectOption({ value: divinciPyannoteDiarizationValue });
    console.log('Selected Divinci Pyannote for diarization');

    // Find and select OpenAI Whisper for transcription
    console.log('Selecting OpenAI Whisper for transcription');

    // Log all available options to help with debugging
    const options = await selects[1].locator('option').all();
    console.log('Available transcription options:');
    for (const option of options) {
      const text = await option.textContent();
      const value = await option.getAttribute('value');
      console.log(`- Option: "${text}" (value: "${value}")`);
    }

    // Find OpenAI Whisper specifically
    let openaiWhisperFound = false;
    for (const option of options) {
      const text = await option.textContent();
      if (text.includes('OpenAI') && text.includes('Whisper')) {
        const value = await option.getAttribute('value');
        if (value) {
          await selects[1].selectOption({ value });
          console.log(`Selected OpenAI Whisper option with value: ${value}`);
          openaiWhisperFound = true;
          break;
        }
      }
    }

    if (!openaiWhisperFound) {
      console.error('Could not find OpenAI Whisper option');
      throw new Error('OpenAI Whisper option not found - test cannot continue');
    }

    // Take a screenshot after selecting tools
    await page.screenshot({ path: 'after-select-tools.png' });
    console.log('Saved screenshot after selecting tools');

    // Take a screenshot before submission
    await page.screenshot({ path: 'before-submit.png' });
    console.log('Saved screenshot before submission');

    // Set up a promise to wait for the 202 response
    let audioId: string | null = null;
    let statusUrl: string | null = null;
    const responsePromise = new Promise<void>((resolve) => {
      page.on('response', async (response) => {
        const url = response.url();
        if (url.includes('/white-label/') &&
            url.includes('/data-source/audio-transcript/file') &&
            response.status() === 202) {

          console.log(`Received 202 response from ${url}`);

          try {
            const responseBody = await response.json();
            console.log('API Response:', responseBody);

            // Extract the audioId from the response
            if (responseBody && responseBody.id) {
              audioId = responseBody.id;
              console.log(`Captured audioId from API response: ${audioId}`);

              // Also capture the status URL if available
              if (responseBody.statusUrl) {
                statusUrl = responseBody.statusUrl;
                console.log(`Status URL: ${responseBody.statusUrl}`);
              }

              // Resolve the promise once we have the audioId
              resolve();
            }
          } catch (error) {
            console.error('Error parsing API response:', error);
            // Still resolve the promise even if parsing fails
            resolve();
          }
        }
      });
    });

    // Click the Upload and Process button to start the upload
    console.log('Clicking Upload and Process button');
    await page.click('button:has-text("Upload and Process")');

    // Wait for the 202 response with a timeout
    console.log('Waiting for 202 Accepted response from the server...');
    const timeoutPromise = new Promise<void>((_, reject) => {
      setTimeout(() => reject(new Error('Timeout waiting for 202 response')), 30000);
    });

    try {
      await Promise.race([responsePromise, timeoutPromise]);
      console.log('Received 202 response with audioId:', audioId);
    } catch (error) {
      console.error('Error waiting for 202 response:', error);
      throw error;
    }

    // Wait for any network activity to settle
    await page.waitForLoadState('networkidle');
    console.log('Submitted file, waiting for processing');

    // Take a screenshot of the processing page
    await page.screenshot({ path: 'processing-page.png' });
    console.log('Saved screenshot of processing page');

    // Check if we're still on the create page
    const pageUrl = page.url();
    if (pageUrl.includes('/create/file')) {
      console.log('Still on the create page after submission, checking for tabs...');

      // Check if we're in the tabbed interface
      const tabsVisible = await page.locator('.header-tabs').count() > 0;

      if (tabsVisible) {
        console.log('Tabbed interface detected, checking for Transcripts tab');

        // Check if the Transcripts tab is available
        const transcriptsTabVisible = await page.locator('.header-tabs li a:has-text("Transcripts")').count() > 0;

        if (transcriptsTabVisible) {
          console.log('Transcripts tab found, clicking it');
          await page.click('.header-tabs li a:has-text("Transcripts")');
          await page.waitForTimeout(1000); // Wait for tab to switch
        } else {
          console.error('Transcripts tab not found');
        }
      } else {
        console.error('No tabbed interface found, form submission may have failed');

        // Check for error messages
        const errorMessage = await page.locator('.notification.is-danger').textContent();
        if (errorMessage) {
          console.error(`Error message found: ${errorMessage}`);
          throw new Error(`Form submission failed: ${errorMessage}`);
        } else {
          throw new Error('Form submission failed without visible error message');
        }
      }
    }

    // Wait a moment for any redirects to complete
    await page.waitForTimeout(2000);

    // Log the audioId if we captured it
    if (audioId) {
      console.log(`Using audioId from API response: ${audioId}`);
    } else {
      console.log('Could not capture audioId from API response, checking localStorage...');

      // Try to get the audioId from localStorage
      const localStorageAudioId = await page.evaluate(() => {
        return localStorage.getItem('lastUploadedAudioId');
      });

      if (localStorageAudioId) {
        console.log(`Found audioId in localStorage: ${localStorageAudioId}`);
        audioId = localStorageAudioId;
      } else {
        console.log('Could not find audioId in localStorage, checking transcript list...');

        // Check if we're on the transcript list page or in the tabbed interface
        const isOnListPage = pageUrl.includes('/audio-transcript') && !pageUrl.includes('/create');
        const isInTabsWithList = pageUrl.includes('/create/file') && await page.locator('.table-container').count() > 0;

        if (isOnListPage || isInTabsWithList) {
          console.log('On transcript list page or in tabbed interface with list visible');

          // Check if there's a selected row
          const hasSelectedRow = await page.locator('tr.is-selected').count() > 0;

          // Get either the selected row or the first row
          const highlightedRow = hasSelectedRow
            ? page.locator('tr.is-selected')
            : page.locator('tbody tr').first();

          if (await highlightedRow.count() > 0) {
            // Extract the audioId from the View button link
            const viewLink = await highlightedRow.locator('a.button.is-info').getAttribute('href');
            if (viewLink) {
              const idFromUrl = viewLink.split('/').pop();
              if (idFromUrl) {
                audioId = idFromUrl;
                console.log(`Extracted audioId from transcript list: ${audioId}`);
              }
            }
          }
        }
      }
    }

    // Wait for the processing to complete (this may take some time)
    console.log('Waiting for processing to complete (this may take up to 5 minutes)...');

    // Add a note about checking Docker logs for debugging
    console.log('NOTE: While waiting, you can check Docker logs in another terminal with:');
    console.log('docker compose -f docker/local.yml logs -f audio-transcription');
    console.log('This will help debug any issues with the audio processing.');

    // Also provide commands to check other relevant services
    console.log('You can also check other relevant services:');
    console.log('docker compose -f docker/local.yml logs -f web-client');
    console.log('docker compose -f docker/local.yml logs -f local-api');

    // We must have the audioId to proceed
    if (!audioId) {
      console.error('Failed to capture audioId from API response or localStorage');
      throw new Error('Could not capture audioId - test cannot continue');
    }

    console.log(`Using audioId from API response: ${audioId}`);

    // Take a screenshot before waiting
    await page.screenshot({ path: 'before-waiting.png' });
    console.log('Saved screenshot before waiting');

    // Poll for completion using the status endpoint if we have the audioId
    console.log('Polling for completion...');

    const startTime = Date.now();
    const timeout = 5 * 60 * 1000; // 5 minutes

    // If we have the audioId, make sure we're on the transcript view page
    if (audioId) {
      console.log(`Navigating to the transcript view page for audioId: ${audioId}`);
      // The correct URL format doesn't include "data-source" in the path
      await page.goto(`http://localhost:8080/white-label/${whitelabelId}/audio-transcript/${audioId}`);
      await page.waitForLoadState('networkidle');

      // Set up a listener for the status API calls
      await page.route(`**/white-label/${whitelabelId}/data-source/audio-transcript/${audioId}/status`, async (route) => {
        // Let the request continue
        await route.continue();

        // Wait for the response
        const response = await route.request().response();
        if (response) {
          try {
            // Check if the response is successful (not 404)
            if (response.status() === 200) {
              const responseBody = await response.json();
              console.log('Status API Response:', responseBody);

              // Log the status information
              if (responseBody && responseBody.status) {
                console.log(`Current status: ${responseBody.status}`);
                if (responseBody.message) {
                  console.log(`Status message: ${responseBody.message}`);
                }
                if (responseBody.progress) {
                  console.log(`Progress: ${responseBody.progress}%`);
                }
              }
            } else {
              console.log(`Status API returned ${response.status()}: ${response.statusText()}`);
            }
          } catch (error) {
            console.error('Error parsing status API response:', error);
          }
        }
      });
    }

    // We'll check the status in the UI instead of polling the API directly
    console.log('Checking status in the UI for completion...');
    let isCompleted = false;

    // Make sure we're on the transcript list page or in the tabbed interface
    const isOnListPage = page.url().includes('/audio-transcript') && !page.url().includes('/create');
    const isInTabsWithList = page.url().includes('/create/file');

    if (!isOnListPage && !isInTabsWithList) {
      console.log('Navigating to the audio transcript list page');
      // The correct URL format doesn't include "data-source" in the path
      await page.goto(`http://localhost:8080/white-label/${whitelabelId}/audio-transcript`);
      await page.waitForLoadState('networkidle');
    }

    while (Date.now() - startTime < timeout && !isCompleted) {
      // Take a screenshot to show current progress
      await page.screenshot({ path: `processing-progress-${Math.floor((Date.now() - startTime)/1000)}s.png` });

      try {
        // Refresh the page to get the latest status
        await page.reload();
        await page.waitForLoadState('networkidle');

        // Check if we're on the list page or the individual page
        const isOnListPage = page.url().includes('/audio-transcript') && !page.url().includes(`/${audioId}`);

        // Get the status tags based on the current page
        const statusTags = isOnListPage
          ? page.locator(`a[href*="${audioId}"]`).locator('xpath=ancestor::tr').locator('.tag')
          : page.locator('.tags .tag:not(:first-child)');

        if (await statusTags.count() > 0) {
          const statusText = await statusTags.first().textContent();
          console.log(`Current status from UI: ${statusText}`);

          // Check if processing is complete
          if (statusText && statusText.toLowerCase().includes('completed')) {
            console.log('UI reports processing is complete');
            isCompleted = true;
            break;
          } else if (statusText && statusText.toLowerCase().includes('failed')) {
            console.error('Processing failed according to UI');
            throw new Error('Processing failed according to UI');
          }
        } else {
          console.log('Status tag not found in the UI');
        }
      } catch (error) {
        console.error('Error checking status in UI:', error);
        // Don't throw here, just continue polling
      }

      // If not complete, wait before checking again
      console.log(`Still processing... (${Math.floor((Date.now() - startTime)/1000)} seconds elapsed)`);
      await page.waitForTimeout(5000); // Wait 5 seconds
    }

    // Check if we timed out
    if (Date.now() - startTime >= timeout) {
      console.error('Processing timeout exceeded');
      throw new Error('Processing timeout exceeded');
    }

    // Take a final screenshot after processing is complete
    await page.screenshot({ path: 'final-state.png' });
    console.log('Saved screenshot of final state');

    // Log success message
    console.log('Audio transcript processing completed successfully');

    // If we have the audioId, log it for reference
    if (audioId) {
      console.log(`Audio transcript ID: ${audioId}`);
      console.log(`You can access this transcript directly at: http://localhost:8080/white-label/${whitelabelId}/audio-transcript/${audioId}`);
    }

    // Now that we know the processing is complete, navigate to the transcript view page
    console.log('Navigating to the transcript view page to verify results');
    // The correct URL format doesn't include "data-source" in the path
    await page.goto(`http://localhost:8080/white-label/${whitelabelId}/audio-transcript/${audioId}`);
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the final transcript page
    await page.screenshot({ path: 'final-transcript-page.png' });

    // Verify that we're on the transcript view page
    const headerVisible = await page.locator('h1:has-text("Audio File:")').count() > 0;
    expect(headerVisible).toBeTruthy();
    console.log('Audio File header is visible');

    // Verify that the status tag is present and shows "completed"
    const statusTag = page.locator('.tag.is-medium.is-success:has-text("completed")');
    expect(await statusTag.count()).toBeGreaterThan(0);
    console.log('Status tag is visible and shows "completed"');

    // Verify that the audio player is present
    const audioPlayerVisible = await page.locator('audio').count() > 0;
    expect(audioPlayerVisible).toBeTruthy();
    console.log('Audio player is visible');

    // Verify that any transcript content is present - using a more general selector
    // The transcript might be in different containers depending on the UI implementation
    await page.waitForTimeout(2000); // Give the page time to fully render

    // Take a screenshot to see what's on the page
    await page.screenshot({ path: 'transcript-page-detail.png' });

    // Check for any element that might contain transcript text
    const hasContent = await page.locator('.content, .transcript, .transcript-text, pre, .card-content').count() > 0;
    expect(hasContent).toBeTruthy();
    console.log('Found content container on the page');

    // Log the page content for debugging
    const pageContent = await page.content();
    console.log('Page contains transcript view with status "completed" and audio player');

    console.log('Audio transcript upload test completed successfully');
  });
});
