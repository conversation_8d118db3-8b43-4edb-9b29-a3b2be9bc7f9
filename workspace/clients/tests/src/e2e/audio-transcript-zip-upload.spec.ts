import { test, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';
import { verifyLoggedIn } from '../utils/auth';

/**
 * E2E test for uploading a zipped media file for audio transcription
 * This test verifies the flow of uploading a zipped media file
 * and checking that it's processed correctly
 *
 * NOTE: This test assumes you are already logged in to the application.
 * Please log in manually before running this test.
 */
test.describe('Audio Transcript Zip Upload', () => {
  // Define the path to the test zip file
  const zipFilePath = path.resolve(__dirname, '../../test-files/homer-media-zipped.zip');

  // Before running tests, check that the test file exists
  test.beforeAll(() => {
    // Verify that the test file exists
    expect(fs.existsSync(zipFilePath)).toBeTruthy();
  });

  test('should upload a zipped media file and process it correctly', async ({ page }) => {
    // Verify that the user is logged in using the storage state
    console.log('Verifying user is logged in via storage state');
    await verifyLoggedIn(page);
    console.log('Confirmed user is logged in');

    // Hover over Organization menu in the header
    console.log('Hovering over Organization menu in the header');
    await page.hover('a.navbar-link[href="/user-group"]:has-text("Organization")');

    // Wait a moment for the dropdown to appear
    await page.waitForTimeout(1000);

    // Take a screenshot of the dropdown
    await page.screenshot({ path: 'organization-dropdown.png' });
    console.log('Saved screenshot of Organization dropdown');

    // Click on Whitelabel option in the dropdown
    console.log('Clicking on Whitelabel option in the dropdown');
    await page.click('a.dropdown-item[href="/white-label"]:has-text("Whitelabel")');

    // Wait for the whitelabel page to load
    await page.waitForLoadState('networkidle');

    // Check if we need to create a new whitelabel
    const existingWhitelabels = await page.locator('.whitelabel-ai-card').count();
    console.log(`Found ${existingWhitelabels} existing whitelabels`);

    if (existingWhitelabels === 0) {
      console.log('No whitelabels found, creating a new one');
      // Click on Create Whitelabel button
      
      // Fill out the whitelabel form
      await page.fill('input[name="name"]', 'Test Whitelabel');
      await page.fill('input[name="domain"]', 'test-whitelabel.com');
      await page.click('text=🌱 Create AI');

      // Submit the form
      await page.click('button[type="submit"]');

      // Wait for the whitelabel to be created
      await page.waitForLoadState('networkidle');
      console.log('Created new whitelabel');
    }

    // Take a screenshot of the whitelabel page
    await page.screenshot({ path: 'whitelabel-page.png' });
    console.log('Saved screenshot of whitelabel page to whitelabel-page.png');

    // Get the first whitelabel ID
    const whitelabelHref = await page.locator('.whitelabel-item a').first().getAttribute('href');
    const whitelabelId = whitelabelHref.split('/').pop();
    console.log(`Using whitelabel ID: ${whitelabelId}`);

    // Navigate to the audio transcript page using the UI
    console.log('Navigating to audio transcript page using the UI');

    // First, click on the whitelabel to enter it
    await page.click(`.whitelabel-item a[href*="${whitelabelId}"]`);
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the whitelabel detail page
    await page.screenshot({ path: 'whitelabel-detail-page.png' });
    console.log('Saved screenshot of whitelabel detail page');

    // Now find and click on the Audio Transcript option in the dropdown menu
    // First, find the dropdown trigger (usually a button or link with the chat title)
    console.log('Looking for the dropdown menu');

    // Hover over the chat-title dropdown trigger
    const dropdownTrigger = page.locator('.dropdown-trigger');
    await dropdownTrigger.hover();
    console.log('Hovering over dropdown trigger');

    // Wait a moment for the dropdown to appear
    await page.waitForTimeout(1000);

    // Click on the Audio Transcripts option in the dropdown
    console.log('Clicking on Audio Transcripts option');
    await page.click('a.dropdown-item:has-text("📝 Audio Transcripts")');
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the audio transcript page
    await page.screenshot({ path: 'audio-transcript-page.png' });
    console.log('Saved screenshot of audio transcript page');

    // Click on Create Tools link
    console.log('Clicking on Create Tools link');
    await page.click('a:has-text("Create Tools")');

    // Wait for the create tools page to load
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the create tools page
    await page.screenshot({ path: 'create-tools-page.png' });
    console.log('Saved screenshot of create tools page to create-tools-page.png');

    // Set the file input to our test zip file
    console.log('Uploading test zip file');
    const fileInput = await page.locator('input[type="file"]');
    await fileInput.setInputFiles(zipFilePath);

    // Wait for the file to be processed and displayed
    await page.waitForTimeout(2000); // Give it a moment to process the file
    console.log('File uploaded, waiting for processing');

    // Select the diarization tool (first option)
    console.log('Selecting diarization tool');
    await page.locator('select[name="diarizerTool"]').selectOption({ index: 0 });

    // Select the transcription tool (first option)
    console.log('Selecting transcription tool');
    await page.locator('select[name="transcriberTool"]').selectOption({ index: 0 });

    // Take a screenshot before submission
    await page.screenshot({ path: 'before-submit.png' });
    console.log('Saved screenshot before submission to before-submit.png');

    // Click the submit button to start the upload
    console.log('Clicking submit button');
    await page.click('button:has-text("Submit")');

    // Wait for the upload to complete and redirect to the processing page
    await page.waitForLoadState('networkidle');
    console.log('Submitted file, waiting for processing');

    // Take a screenshot of the processing page
    await page.screenshot({ path: 'processing-page.png' });
    console.log('Saved screenshot of processing page to processing-page.png');

    // Wait for the processing to complete (this may take some time)
    console.log('Waiting for processing to complete (this may take up to 2 minutes)...');
    await page.waitForSelector('text=Transcript Complete', { timeout: 120000 });
    console.log('Processing completed');

    // Take a screenshot of the completed transcript
    await page.screenshot({ path: 'transcript-complete.png' });
    console.log('Saved screenshot of completed transcript to transcript-complete.png');

    // Verify that we're on the transcript view page
    await expect(page.locator('h1:has-text("Audio Transcript")')).toBeVisible();

    // Verify that the transcript contains samples
    await page.waitForSelector('.transcript-row');
    const sampleCount = await page.locator('.transcript-row').count();
    expect(sampleCount).toBeGreaterThan(0);
    console.log(`Found ${sampleCount} transcript samples`);

    // Verify that the audio player is present
    await expect(page.locator('.audio-player')).toBeVisible();

    // Verify that the speaker labels are present
    await expect(page.locator('.speaker-label')).toBeVisible();

    // Verify that the transcript text is present
    await expect(page.locator('.transcript-text')).toBeVisible();

    console.log('Test completed successfully!');
  });
});
