import { test, expect } from '@playwright/test';
import {
  verifyLoggedIn,
  ensureLoggedIn,
  navigateToWhitelabelPage,
  getFirstWhitelabelId,
  ensureWhitelabelExists
} from './utils/common';

/**
 * E2E test for creating a RAG Vector configuration
 * This test verifies the flow of creating a new RAG Vector configuration
 * with CloudFlare V2 as the vector tool
 */
test.describe('RAG Vector Creation', () => {
  // Increase the test timeout to 2 minutes (120000ms)
  test('should create a RAG Vector configuration with CloudFlare V2', async ({ page }) => {
    // Set a longer timeout for this test
    test.setTimeout(120000);
    console.log('Starting RAG Vector creation test');

    // Ensure the user is logged in
    await ensureLoggedIn(page);

    // Ensure at least one whitelabel exists
    await ensureWhitelabelExists(page);

    // Navigate directly to the whitelabel page
    console.log('Navigating directly to the whitelabel page');
    await page.goto('http://localhost:8080/white-label');
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the whitelabel page
    await page.screenshot({ path: 'whitelabel-page.png' });
    console.log('Saved screenshot of whitelabel page');

    // Check if we're on the whitelabel page
    console.log('Current URL:', page.url());

    // Get all whitelabel cards
    const whitelabelCards = await page.locator('.whitelabel-ai-card, .card.whitelabel-ai-card').count();
    console.log(`Found ${whitelabelCards} whitelabel cards`);

    // Get the first whitelabel ID from the href attribute
    let whitelabelId;
    const whitelabelHref = await page.locator('.whitelabel-ai-card .title a.clickable-text, .card.whitelabel-ai-card .title a.clickable-text').first().getAttribute('href');

    if (whitelabelHref) {
      whitelabelId = whitelabelHref.split('/').pop();
      console.log(`Using whitelabel ID: ${whitelabelId}`);
    } else {
      console.error('Could not find whitelabel ID');
      throw new Error('Could not find whitelabel ID');
    }

    // Click on the first whitelabel in the list
    console.log('Clicking on first whitelabel');
    try {
      await page.click(`.whitelabel-ai-card .title a.clickable-text[href*="${whitelabelId}"], .card.whitelabel-ai-card .title a.clickable-text[href*="${whitelabelId}"]`);
      console.log('Successfully clicked on whitelabel');
    } catch (error) {
      console.error('Error clicking on whitelabel:', error.message);

      // Try a more general selector
      try {
        await page.click('.whitelabel-ai-card .title a, .card.whitelabel-ai-card .title a');
        console.log('Successfully clicked on whitelabel with general selector');
      } catch (error) {
        console.error('Error clicking on whitelabel with general selector:', error.message);

        // Navigate directly to the whitelabel page
        console.log('Navigating directly to the whitelabel page');
        await page.goto(`http://localhost:8080/white-label/${whitelabelId}`);
      }
    }
    await page.waitForTimeout(1000);

    // Check if we need to click on the Setup button
    const setupButton = page.locator('a.button:has-text("Setup")');
    if (await setupButton.count() > 0) {
      console.log('Clicking on Setup button');
      await setupButton.click();
      await page.waitForTimeout(1000);
    } else {
      console.log('Already on the setup page or Setup button not found');
    }

    // Navigate directly to the RAG Vector page
    console.log('Navigating directly to the RAG Vector page');
    await page.goto(`http://localhost:8080/white-label/${whitelabelId}/rag-vector`);
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the RAG Vector page
    await page.screenshot({ path: 'rag-vector-page.png' });
    console.log('Saved screenshot of RAG Vector page');

    // Check if we need to create a RAG vector configuration
    console.log('Checking if we need to create a RAG vector configuration');

    // Take a screenshot of the current page
    await page.screenshot({ path: 'rag-vector-page-initial.png' });

    // Check if there's a message about creating a RAG vector first
    const createRagMessage = page.locator('text="No RAG vector configuration found. Please create a RAG vector first."');
    const noConfigMessage = page.locator('text="No configuration found"');
    const createButton = page.locator('.create-rag-vector-drodown, button:has-text("Create RAG Vector")');

    // If we need to create a RAG vector or the create button is visible
    if (await createRagMessage.count() > 0 || await noConfigMessage.count() > 0 || await createButton.count() > 0) {
      console.log('Need to create a RAG vector configuration');

      // Look for the Create RAG Vector dropdown button
      if (await createButton.count() > 0) {
        console.log('Found Create RAG Vector button, clicking it');
        await createButton.click();
        await page.waitForTimeout(1000); // Wait for dropdown to expand

        // Take a screenshot after clicking the Create RAG Vector button
        await page.screenshot({ path: 'after-create-rag-vector-click.png' });

        // Check if the form is visible
        const createForm = page.locator('div.create-rag-vector-form');

        if (await createForm.count() > 0) {
          console.log('Create RAG Vector form is visible');

          // Fill in the title field
          const titleInput = createForm.locator('input.input[placeholder="Enter a title"]');
          if (await titleInput.count() > 0) {
            console.log('Found title input, filling it');
            await titleInput.fill('Test RAG Vector');
          } else {
            console.error('Title input not found');
            await page.screenshot({ path: 'title-input-not-found.png' });
          }

          // Fill in the description field
          const descriptionInput = createForm.locator('textarea.textarea[placeholder="Enter a description"]');
          if (await descriptionInput.count() > 0) {
            console.log('Found description input, filling it');
            await descriptionInput.fill('Test RAG Vector for automated testing');
          } else {
            console.error('Description input not found');
            await page.screenshot({ path: 'description-input-not-found.png' });
          }

          // Select CloudFlare V2 option
          console.log('Selecting CloudFlare V2 option');

          // Look for the CloudFlare V2 option
          const cloudflareOption = page.locator('p:has-text("CloudFlare V2"), p.ToolSelectShared-module__title___TT_l4:has-text("CloudFlare V2")');

          if (await cloudflareOption.count() > 0) {
            console.log('Found CloudFlare V2 option, clicking it');
            await cloudflareOption.click();
            await page.waitForTimeout(1000);

            // Take a screenshot after selecting CloudFlare V2
            await page.screenshot({ path: 'after-cloudflare-selection.png' });

            // Debug: Log all buttons in the form
            const formButtons = await createForm.locator('button').all();
            console.log(`Found ${formButtons.length} buttons in the form:`);
            for (let i = 0; i < formButtons.length; i++) {
              const buttonText = await formButtons[i].textContent();
              const buttonClass = await formButtons[i].getAttribute('class');
              console.log(`Button ${i+1}: Text="${buttonText}", Class="${buttonClass}"`);
            }

            // Look for the Create button in the form
            const createConfigButton = createForm.locator('button.rag-form-button, button:has-text("Create RAG Vector"), button.button.is-primary');

            if (await createConfigButton.count() > 0) {
              console.log('Found Create button in form, clicking it');

              // Take a screenshot before clicking the button
              await page.screenshot({ path: 'before-create-button-click.png' });

              // Try clicking the button
              try {
                await createConfigButton.click();
                console.log('Successfully clicked the Create RAG Vector button');
              } catch (error) {
                console.error('Error clicking the Create RAG Vector button:', error.message);

                // Try using JavaScript to click the button
                console.log('Trying to click the button using JavaScript');
                await page.evaluate(() => {
                  const button = document.querySelector('.rag-form-button');
                  if (button) {
                    (button as HTMLButtonElement).click();
                    return true;
                  }
                  return false;
                });
              }

              await page.waitForLoadState('networkidle');

              // Take a screenshot after creating the RAG Vector
              await page.screenshot({ path: 'after-rag-vector-creation.png' });
            } else {
              console.error('Create button in form not found');
              await page.screenshot({ path: 'create-config-button-not-found.png' });
            }
          } else {
            console.error('CloudFlare V2 option not found');
            await page.screenshot({ path: 'cloudflare-option-not-found.png' });

            // Log all available options
            const allOptions = await page.locator('p').allTextContents();
            console.log('Available options:', allOptions);
          }
        } else {
          console.error('Create RAG Vector form not visible');
          await page.screenshot({ path: 'create-form-not-visible.png' });
        }
      } else {
        console.error('Create RAG Vector button not found');
        await page.screenshot({ path: 'create-button-not-found.png' });

        // Log all buttons on the page
        const allButtons = await page.locator('button').all();
        console.log(`Found ${allButtons.length} buttons on the page:`);
        for (let i = 0; i < allButtons.length; i++) {
          const buttonText = await allButtons[i].textContent();
          const buttonClass = await allButtons[i].getAttribute('class');
          console.log(`Button ${i+1}: Text="${buttonText}", Class="${buttonClass}"`);
        }
      }
    } else {
      console.log('RAG vector configuration already exists');
      await page.screenshot({ path: 'rag-vector-already-exists.png' });
    }

    // Verify that the RAG Vector configuration was created successfully
    // We should no longer see the "No RAG vector configuration found" message
    const noRagVectorMessage = page.locator('text="No RAG vector configuration found. Please create a RAG vector first."');
    expect(await noRagVectorMessage.count()).toBe(0);
    console.log('RAG Vector configuration created successfully');

    // Take a final screenshot
    await page.screenshot({ path: 'final-state.png' });
    console.log('RAG Vector creation test completed successfully');
  });
});