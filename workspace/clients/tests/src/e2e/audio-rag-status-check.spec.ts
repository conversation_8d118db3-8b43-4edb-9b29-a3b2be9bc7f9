import { test, expect, Page } from '@playwright/test';
import { verifyLoggedIn } from '../utils/auth';
import path from 'path';
import fs from 'fs';

/**
 * Helper function to remove webpack-dev-server-client-overlay that might be blocking interactions
 * @param page The Playwright page
 * @returns A promise that resolves when the overlay has been removed
 */
async function removeWebpackOverlay(page: Page): Promise<void> {
  await page.evaluate(() => {
    try {
      // Find and remove the webpack overlay
      const overlay = document.getElementById('webpack-dev-server-client-overlay');
      if (overlay) {
        console.log('Found webpack-dev-server-client-overlay, removing it');
        overlay.remove();
        return 'Removed webpack overlay';
      }

      // Also check for iframes that might contain the overlay
      const iframes = document.querySelectorAll('iframe');
      for (const iframe of iframes) {
        if (iframe.id === 'webpack-dev-server-client-overlay') {
          console.log('Found webpack overlay iframe, removing it');
          iframe.remove();
          return 'Removed webpack overlay iframe';
        }
      }

      return 'No webpack overlay found';
    } catch (error) {
      console.error('Error removing webpack overlay:', error);
      return `Error: ${error.message}`;
    }
  });
}

/**
 * Test for Audio-RAG status completion checking
 *
 * This test verifies that the system correctly processes an audio file to RAG
 * and checks for completion status.
 */
test('Audio to RAG status completion check', async ({ page }): Promise<void> => {
  // Increase the test timeout to 5 minutes
  test.setTimeout(300000);

  // 1. Verify that the user is logged in
  await verifyLoggedIn(page);

  // 2. Use a hardcoded whitelabel ID from an existing whitelabel
  const whitelabelId = '68271db1cda19ea411e422b0'; // ID of an existing "Audio RAG Extension Test" whitelabel
  console.log(`Using hardcoded whitelabel ID: ${whitelabelId}`);

  // 3. Use the Dr. Fuhrman - TikTok Nutrition Advice-faststart.mp4 file directly
  let testAudioFile = path.join("/Users/<USER>/Documents/server/workspace/clients/test-files", 'Dr. Fuhrman - TikTok Nutrition Advice-faststart.mp4');

  // Check if the file exists
  if (!fs.existsSync(testAudioFile)) {
    console.log(`Test file not found: ${testAudioFile}. Skipping test.`);
    test.skip();
    return;
  }

  console.log(`💥🥠 Using audio file: ${testAudioFile}`);

  // 4. Upload the file
  console.log('Using UI method to upload the file...');

  // Navigate to the audio transcript upload page
  await page.goto(`${process.env.WEB_URL || 'http://localhost:8080'}/white-label/${whitelabelId}/audio-transcript/create/file`);
  await page.waitForLoadState('networkidle');

  // Upload the file using the file input
  const fileInput = page.locator('input[type="file"]');
  await fileInput.setInputFiles(testAudioFile);

  // Wait for the file to be processed
  await page.waitForTimeout(2000);

  // Select the diarizer and transcriber tools
  console.log("Selecting 'Pyannote from Divinci' from the diarizer dropdown");

  // First, let's take a screenshot before selection to verify the dropdown is visible
  await page.screenshot({ path: 'before-diarizer-selection.png' });

  // Try to find the diarizer dropdown using multiple selectors
  const diarizerDropdown = page.locator('select[name="diarizerTool"], .diarizeTool-module__selectWidth___SgojY, .diarizeTool select');

  // Log the number of matching elements
  const dropdownCount = await diarizerDropdown.count();
  console.log(`Found ${dropdownCount} diarizer dropdown elements`);

  if (dropdownCount > 0) {
    // Get all available options
    const options = await diarizerDropdown.first().locator('option').all();
    console.log(`Found ${options.length} options in the diarizer dropdown`);

    // Log all options for debugging
    for (let i = 0; i < options.length; i++) {
      const value = await options[i].getAttribute('value');
      const text = await options[i].textContent();
      console.log(`Option ${i}: value="${value}", text="${text}"`);
    }

    // Select the Divinci Pyannote option (index 1 based on the HTML)
    await diarizerDropdown.first().selectOption('@divinci-ai/pyannote-segmentation');
    console.log("Selected '@divinci-ai/pyannote-segmentation' option");
  } else {
    console.error("Could not find diarizer dropdown!");
  }

  // Take a screenshot after selection
  await page.screenshot({ path: 'after-diarizer-selection.png' });

  // Set up console log capture
  await page.evaluate(() => {
    // @ts-ignore - Custom property added at runtime
    window.__consoleLogs = [];

    // Store original console methods
    // @ts-ignore - Custom property added at runtime
    window.__originalConsoleLog = console.log;
    // @ts-ignore - Custom property added at runtime
    window.__originalConsoleError = console.error;
    // @ts-ignore - Custom property added at runtime
    window.__originalConsoleWarn = console.warn;
    // @ts-ignore - Custom property added at runtime
    window.__originalConsoleInfo = console.info;

    // Override console methods to capture logs
    console.log = function(...args: any[]) {
      // @ts-ignore - Custom property added at runtime
      window.__consoleLogs.push(args.map(arg => String(arg)).join(' '));
      // @ts-ignore - Custom property added at runtime
      return window.__originalConsoleLog.apply(this, args);
    };

    console.error = function(...args: any[]) {
      // @ts-ignore - Custom property added at runtime
      window.__consoleLogs.push('ERROR: ' + args.map(arg => String(arg)).join(' '));
      // @ts-ignore - Custom property added at runtime
      return window.__originalConsoleError.apply(this, args);
    };

    console.warn = function(...args: any[]) {
      // @ts-ignore - Custom property added at runtime
      window.__consoleLogs.push('WARN: ' + args.map(arg => String(arg)).join(' '));
      // @ts-ignore - Custom property added at runtime
      return window.__originalConsoleWarn.apply(this, args);
    };

    console.info = function(...args: any[]) {
      // @ts-ignore - Custom property added at runtime
      window.__consoleLogs.push('INFO: ' + args.map(arg => String(arg)).join(' '));
      // @ts-ignore - Custom property added at runtime
      return window.__originalConsoleInfo.apply(this, args);
    };
  });

  // Log any console messages
  page.on('console', msg => {
    console.log(`Browser console ${msg.type()}: ${msg.text()}`);
  });

  // Select the transcriber tool
  console.log("Selecting '@cf/openai/whisper-large-v3-turbo' from the transcriber dropdown");

  // First, let's take a screenshot before selection to verify the dropdown is visible
  await page.screenshot({ path: 'before-transcriber-selection.png' });

  // Try to find the transcriber dropdown using multiple selectors
  const transcriberDropdown = page.locator('select[name="transcriberTool"], select:not(.diarizeTool-module__selectWidth___SgojY)');

  // Log the number of matching elements
  const transcriberCount = await transcriberDropdown.count();
  console.log(`Found ${transcriberCount} transcriber dropdown elements`);

  if (transcriberCount > 0) {
    // Get all available options
    const options = await transcriberDropdown.first().locator('option').all();
    console.log(`Found ${options.length} options in the transcriber dropdown`);

    // Log all options for debugging
    for (let i = 0; i < options.length; i++) {
      const value = await options[i].getAttribute('value');
      const text = await options[i].textContent();
      console.log(`Option ${i}: value="${value}", text="${text}"`);
    }

    // Select the Official OpenAI Whisper v2 option
    await transcriberDropdown.first().selectOption('openai/whisper-1');
    console.log("Selected 'openai/whisper-1' option (Official OpenAI Whisper v2)");
  } else {
    console.error("Could not find transcriber dropdown!");
  }

  // Take a screenshot after selecting the tools
  await page.screenshot({ path: 'after-select-tools.png' });

  // Click the upload button
  console.log("Clicking the 'Upload and Process' button");

  // Take a screenshot before clicking the button
  await page.screenshot({ path: 'before-upload-button-click.png' });

  // Log any console errors
  page.on('console', msg => {
    console.log(`Browser console ${msg.type()}: ${msg.text()}`);
  });

  // Find all buttons on the page
  const buttons = page.locator('button');
  const buttonCount = await buttons.count();
  console.log(`Found ${buttonCount} buttons on the page`);

  for (let i = 0; i < buttonCount; i++) {
    const buttonText = await buttons.nth(i).textContent();
    const buttonClass = await buttons.nth(i).getAttribute('class');
    console.log(`Button ${i}: text="${buttonText}", class="${buttonClass}"`);
  }

  // Try to find the upload button using multiple selectors
  const uploadButton = page.locator('button.is-primary.is-large, button:has-text("Upload and Process")');
  const uploadButtonCount = await uploadButton.count();
  console.log(`Found ${uploadButtonCount} upload buttons`);

  if (uploadButtonCount > 0) {
    await uploadButton.first().click();
    console.log("Clicked the upload button");
  } else {
    console.error("Could not find the upload button!");
  }

  // Wait for the upload response in the browser console
  await page.waitForTimeout(2000);

  // Extract the audio ID from the browser console logs
  let audioId: string | null = null;

  // Get the browser console logs
  const logs = await page.evaluate(() => {
    // @ts-ignore - Custom property added at runtime
    return window.__consoleLogs || [];
  });

  // Look for the upload response log
  console.log('Searching for audio ID in browser console logs...');
  for (const log of logs) {
    if (typeof log === 'string' && log.includes('Local mode upload response:')) {
      console.log(`Found upload response log: ${log}`);
      const match = log.match(/id:\s*([a-f0-9]+)/i);
      if (match && match[1]) {
        audioId = match[1];
        console.log(`Extracted audio ID from console log: ${audioId}`);
        break;
      }
    }
  }

  // If we couldn't get the ID from the console logs, navigate to the list page and get the most recent ID
  if (!audioId) {
    console.log('Could not extract audio ID from console logs, navigating to audio transcript list page');

    // Navigate to the audio transcript list page
    await page.goto(`${process.env.WEB_URL || 'http://localhost:8080'}/white-label/${whitelabelId}/audio-transcript`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Look for the first row in the table (most recent upload)
    const firstRow = page.locator('table tbody tr').first();
    const idElement = firstRow.locator('.is-family-monospace');

    if (await idElement.count() > 0) {
      const idText = await idElement.textContent();
      if (idText) {
        audioId = idText.trim();
        console.log(`Found most recent audio ID in table: ${audioId}`);
      }
    }
  }

  // Check if we got a valid audio ID
  if (audioId === null || !audioId || audioId === 'create' || audioId === 'file' || audioId === 'unknown-file-id') {
    console.log(`Failed to get a valid audio ID: ${audioId}. This could be due to an error in the file upload process.`);
    console.log('Skipping the test.');

    // Take a screenshot to help debug
    await page.screenshot({ path: 'audio-upload-failed.png' });

    // Skip the test
    test.skip();
    return;
  }

  // 5. Check if the audio file processing failed and wait for it to complete
  console.log(`💥🥠 Audio->RAG: Checking status and waiting for completion for audioId: ${audioId}`);

  // Navigate to the audio transcript page to check the status
  await page.goto(`${process.env.WEB_URL || 'http://localhost:8080'}/white-label/${whitelabelId}/audio-transcript`);
  await page.waitForLoadState('networkidle');

  // Set up a polling mechanism to check for the completed status
  const maxAttempts = 120; // Maximum number of polling attempts (10 minutes at 5 second intervals)
  const pollingInterval = 5000; // 5 seconds between polls
  let attempts = 0;
  let isCompleted = false;
  let hasFailed = false;
  let lastStatus = '';
  let lastStatusClass = '';

  console.log(`Starting to poll for completed status of audio transcript ${audioId}`);

  while (attempts < maxAttempts && !isCompleted && !hasFailed) {
    console.log(`UI polling attempt ${attempts + 1}/${maxAttempts}`);

    // Refresh the page to get the latest status
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Take a screenshot every 10 attempts
    if (attempts % 10 === 0) {
      await page.screenshot({ path: `audio-transcript-list-polling-${attempts}.png` });
    }

    // Look for the row with our file ID
    const fileRow = page.locator(`tr:has(.is-family-monospace:text("${audioId}"))`);
    const fileRowCount = await fileRow.count();

    if (fileRowCount > 0) {
      console.log(`Found row for file ID: ${audioId}`);

      // Check for the status element with data-testid
      const statusElement = page.locator(`[data-testid="status-${audioId}"]`);
      const hasStatusElement = await statusElement.count() > 0;

      if (hasStatusElement) {
        // Check the status text
        const statusText = await statusElement.textContent() || '';
        lastStatus = statusText;
        console.log(`Current UI status (from data-testid): ${statusText}`);

        // Check for progress bar
        const progressBar = page.locator(`[data-testid="progress-bar-${audioId}"]`);
        const hasProgressBar = await progressBar.count() > 0;

        if (hasProgressBar) {
          const progressValue = await progressBar.getAttribute('value');
          const progressMax = await progressBar.getAttribute('max');
          console.log(`Processing progress: ${progressValue}/${progressMax}`);
        }

        // Check for completed status
        if (statusText && statusText.toLowerCase().includes('completed')) {
          console.log(`Audio transcript ${audioId} processing completed according to UI (data-testid)!`);
          isCompleted = true;
          break;
        } else if (statusText && statusText.toLowerCase().includes('failed')) {
          console.log(`Audio transcript ${audioId} processing failed according to UI (data-testid)!`);
          hasFailed = true;
          break;
        }
      } else {
        // Fall back to the old method
        const statusTag = fileRow.locator('.tag');
        const statusTagCount = await statusTag.count();

        if (statusTagCount > 0) {
          const statusText = await statusTag.textContent() || '';
          const statusClass = await statusTag.getAttribute('class') || '';
          lastStatus = statusText;
          lastStatusClass = statusClass;
          console.log(`Current UI status (fallback): ${statusText}, class: ${statusClass}`);

          // Check for success tag (green tag with "completed" text)
          if (statusClass && statusClass.includes('is-success')) {
            console.log(`Audio transcript ${audioId} processing completed according to UI (success tag)!`);
            isCompleted = true;
            break;
          } else if (statusText && (statusText.trim().toLowerCase() === 'completed' || statusText.trim().toLowerCase() === 'complete')) {
            console.log(`Audio transcript ${audioId} processing completed according to UI (text)!`);
            isCompleted = true;
            break;
          } else if (statusClass && statusClass.includes('is-danger')) {
            console.log(`Audio transcript ${audioId} processing failed according to UI (danger tag)!`);
            hasFailed = true;
            break;
          } else if (statusText && statusText.trim().toLowerCase() === 'failed') {
            console.log(`Audio transcript ${audioId} processing failed according to UI (text)!`);
            hasFailed = true;
            break;
          }
        } else {
          // Try to get any text from the row to help with debugging
          const rowText = await fileRow.textContent() || '';
          console.log(`Row text for file ID ${audioId}: ${rowText.substring(0, 100)}...`);
        }
      }
    } else {
      console.log(`Row for file ID ${audioId} not found yet in UI`);
    }

    // Wait before the next polling attempt
    console.log(`Waiting ${pollingInterval/1000} seconds before next UI check...`);
    await page.waitForTimeout(pollingInterval);
    attempts++;
  }

  // Take a screenshot of the final state
  await page.screenshot({ path: `audio-transcript-list-final-state-${audioId}.png` });

  // If the status is "failed", skip the test
  if (hasFailed) {
    console.log(`Audio file processing failed. Skipping the test.`);
    test.skip();
    return;
  }

  // If we reached the maximum attempts without completion, fail the test
  if (!isCompleted) {
    console.log(`Reached maximum polling attempts without completion for file ID: ${audioId}`);
    console.log(`Last status: ${lastStatus}, class: ${lastStatusClass}`);
    console.log(`The audio file must be in "completed" status before proceeding to RAG creation.`);

    // Take a final screenshot
    await page.screenshot({ path: `audio-transcript-timeout-${audioId}.png` });

    // Fail the test
    throw new Error(`Audio file processing did not complete within the timeout period. Last status: ${lastStatus}`);
  } else {
    console.log(`💥🥠 Audio->RAG: Transcription completed successfully with audioId: ${audioId}`);
  }

  // 7. Navigate to the audio transcript RAG creation page
  console.log("💥🥠 Audio->RAG: Navigating to the audio transcript RAG creation page");
  await page.goto(`${process.env.WEB_URL || 'http://localhost:8080'}/white-label/${whitelabelId}/audio-transcript/${audioId}/rag`);

  // Wait for the page to load
  console.log("💥🥠 Audio->RAG: Waiting for the page to load");
  await page.waitForTimeout(2000);

  // Remove any webpack overlay
  await removeWebpackOverlay(page);

  // Take a screenshot to help debug
  await page.screenshot({ path: 'audio-transcript-rag-page.png' });

  // 8. Select all speakers
  console.log("💥🥠 Audio->RAG: Selecting all speakers");

  // Get all speaker options
  const speakerOptions = page.locator('option.speaker-checkbox');
  const count = await speakerOptions.count();
  console.log(`🗣️💥 Found ${count} speaker options`);

  if (count > 0) {
    // Get all speaker values
    const speakerValues: string[] = [];
    for (let i = 0; i < count; i++) {
      const value = await speakerOptions.nth(i).getAttribute('value');
      if (value) speakerValues.push(value);
    }
    console.log(`🗣️💥Speaker values: ${speakerValues.join(', ')}`);

    // Use evaluate to set the select element's value
    if (speakerValues.length > 0) {
      await page.evaluate((values) => {
        const selectElement = document.querySelector('select.is-multiple');
        if (selectElement) {
          // Cast to HTMLSelectElement to access options
          const select = selectElement as HTMLSelectElement;

          // Select all options
          for (let i = 0; i < select.options.length; i++) {
            select.options[i].selected = values.includes(select.options[i].value);
          }

          // Dispatch change event
          select.dispatchEvent(new Event('change', { bubbles: true }));
        }
      }, speakerValues);
      console.log("Selected all speakers");
    }
  } else {
    console.log("No speaker options found, continuing without selection");
  }

  // 9. Click the "Run" button
  console.log("💥🥠 Audio->RAG: Clicking the Run button");
  await removeWebpackOverlay(page);
  await page.screenshot({ path: 'before-run-button-click.png' });

  try {
    await page.locator('button:has-text("Run")').click({ force: true });
    console.log("Clicked the 'Run' button with force option");
  } catch (error) {
    console.log(`Error clicking Run button: ${error instanceof Error ? error.message : String(error)}`);

    // Try an alternative approach using JavaScript click
    console.log("Trying to click the button using JavaScript");
    await page.evaluate(() => {
      const runButton = Array.from(document.querySelectorAll('button')).find(
        button => button.textContent?.includes('Run')
      );
      if (runButton) {
        console.log('Found Run button via JavaScript, clicking it');
        runButton.click();
        return 'Clicked Run button via JavaScript';
      }
      return 'Could not find Run button via JavaScript';
    });
  }

  // 10. Wait for the form submission to complete
  await page.waitForTimeout(2000);
  await page.screenshot({ path: 'after-run-button-click.png' });

  // 11. Check if we're redirected to the list view
  const isListView = await page.locator('.create-audio-rag-file-list').count() > 0;

  if (!isListView) {
    console.log("Not on the list view yet, clicking the 'File List' tab");
    await page.locator('a:text("File List")').click();
    await page.waitForTimeout(1000);
  }

  // Take a screenshot of the list view
  await page.screenshot({ path: 'rag-file-list-view.png' });

  // 12. Wait for the RAG file to be created and check its status
  console.log("Waiting for RAG file to be created and processed...");

  // Set up a polling mechanism to check for the completed RAG file
  const ragMaxAttempts = 120; // Maximum number of polling attempts (10 minutes at 5 second intervals)
  const ragPollingInterval = 5000; // 5 seconds between polls
  let ragAttempts = 0;
  let ragFileId = '';
  let ragIsCompleted = false;
  let ragHasFailed = false;
  let lastRagStatus = '';
  let lastRagStatusClass = '';

  console.log(`Starting to poll for RAG file creation and completion`);

  while (ragAttempts < ragMaxAttempts && !ragIsCompleted && !ragHasFailed) {
    console.log(`RAG file polling attempt ${ragAttempts + 1}/${ragMaxAttempts}`);

    // Refresh the page to get the latest status
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Take a screenshot every 10 attempts
    if (ragAttempts % 10 === 0) {
      await page.screenshot({ path: `rag-file-list-polling-${ragAttempts}.png` });
    }

    // Check for RAG files in the list
    const ragFileItems = page.locator('.create-audio-rag-file-list li');
    const ragFileCount = await ragFileItems.count();
    console.log(`Found ${ragFileCount} RAG files in the list`);

    if (ragFileCount > 0) {
      // Look for a RAG file that contains the audio ID in its description
      let targetRagFileItem = ragFileItems.first(); // Default to first item
      let foundMatchingRagFile = false;

      // First, try to find a RAG file that contains the audio ID in its description
      for (let i = 0; i < ragFileCount; i++) {
        const item = ragFileItems.nth(i);
        const itemText = await item.textContent() || '';

        // Check if the item text contains the audio ID
        if (itemText && itemText.includes(audioId)) {
          console.log(`Found RAG file item that contains the audio ID: ${audioId}`);
          targetRagFileItem = item;
          foundMatchingRagFile = true;
          break;
        }
      }

      // If we couldn't find a RAG file with the audio ID, log that we're using the first one
      if (!foundMatchingRagFile) {
        console.log(`Could not find a RAG file that contains the audio ID: ${audioId}. Using the first RAG file in the list.`);

        // Log all RAG file items for debugging
        for (let i = 0; i < ragFileCount; i++) {
          const item = ragFileItems.nth(i);
          const itemText = await item.textContent() || '';
          console.log(`RAG file ${i + 1} text: ${itemText.substring(0, 100)}...`);
        }
      }

      // Check if the item has a status indicator
      const statusElement = targetRagFileItem.locator('.tag, [data-testid^="status-"]');
      const hasStatusElement = await statusElement.count() > 0;

      if (hasStatusElement) {
        const statusText = await statusElement.textContent() || '';
        const statusClass = await statusElement.getAttribute('class') || '';
        lastRagStatus = statusText;
        lastRagStatusClass = statusClass;
        console.log(`RAG file status: ${statusText}, class: ${statusClass}`);

        // Extract the RAG file ID
        const ragFileLinks = targetRagFileItem.locator('a');
        if (await ragFileLinks.count() > 0) {
          const href = await ragFileLinks.first().getAttribute('href');
          if (href) {
            const matches = href.match(/\/rag-vector\/files\/([a-f0-9]+)/i);
            if (matches && matches[1]) {
              ragFileId = matches[1];
              console.log(`Found RAG file ID: ${ragFileId}`);
            }
          }
        }

        // Check for completed status
        if (statusText && statusText.toLowerCase().includes('completed')) {
          console.log(`RAG file ${ragFileId} processing completed!`);
          ragIsCompleted = true;
          break;
        } else if (statusClass && statusClass.includes('is-success')) {
          console.log(`RAG file ${ragFileId} processing completed (success tag)!`);
          ragIsCompleted = true;
          break;
        } else if (statusText && statusText.toLowerCase().includes('failed')) {
          console.log(`RAG file ${ragFileId} processing failed!`);
          ragHasFailed = true;
          break;
        } else if (statusClass && statusClass.includes('is-danger')) {
          console.log(`RAG file ${ragFileId} processing failed (danger tag)!`);
          ragHasFailed = true;
          break;
        }
      } else {
        // If no status element is found, try to extract the RAG file ID anyway
        const ragFileLinks = targetRagFileItem.locator('a');
        if (await ragFileLinks.count() > 0) {
          const href = await ragFileLinks.first().getAttribute('href');
          if (href) {
            const matches = href.match(/\/rag-vector\/files\/([a-f0-9]+)/i);
            if (matches && matches[1]) {
              ragFileId = matches[1];
              console.log(`Found RAG file ID: ${ragFileId}, but no status element. Assuming it's still processing.`);
            }
          }
        }

        // Try to get any text from the item to help with debugging
        const itemText = await targetRagFileItem.textContent() || '';
        console.log(`RAG file item text: ${itemText.substring(0, 100)}...`);
      }
    } else {
      console.log(`No RAG files found in the list yet. Waiting for RAG file creation...`);
    }

    // Wait before the next polling attempt
    console.log(`Waiting ${ragPollingInterval/1000} seconds before next RAG file check...`);
    await page.waitForTimeout(ragPollingInterval);
    ragAttempts++;
  }

  // Take a screenshot of the final state
  await page.screenshot({ path: 'rag-file-list-final-state.png' });

  // If the status is "failed", skip the test
  if (ragHasFailed) {
    console.log(`RAG file processing failed. Skipping the test.`);
    test.skip();
    return;
  }

  // If we reached the maximum attempts without completion, warn but continue
  if (!ragIsCompleted) {
    console.log(`Reached maximum polling attempts without RAG file completion`);
    console.log(`Last status: ${lastRagStatus}, class: ${lastRagStatusClass}`);
    console.log(`The RAG file may still be processing in the background. Continuing with the test anyway.`);
  } else {
    console.log(`💥🥠 Audio->RAG: RAG file created and processed successfully with ID: ${ragFileId}`);
  }

  // 13. Verify that at least one RAG file is present
  const currentRagFileItems = page.locator('.create-audio-rag-file-list li');
  const currentRagFileCount = await currentRagFileItems.count();
  console.log(`Found ${currentRagFileCount} RAG files in the list`);
  expect(currentRagFileCount).toBeGreaterThan(0);
  console.log("Verified that at least one RAG file is present in the list");

  // 14. If we don't have a RAG file ID yet, try to find it in the list
  if (!ragFileId) {
    console.log("No RAG file ID found during polling, trying to extract it from the list");

    // Try to find the RAG file ID in the list
    const currentRagFileLinks = page.locator('.create-audio-rag-file-list li a');
    const linkCount = await currentRagFileLinks.count();
    console.log(`Found ${linkCount} RAG file links`);

    if (linkCount > 0) {
      // Get the href of the first link
      const href = await currentRagFileLinks.first().getAttribute('href');
      if (href) {
        // Extract the RAG file ID from the href
        const matches = href.match(/\/rag-vector\/files\/([a-f0-9]+)/i);
        if (matches && matches[1]) {
          ragFileId = matches[1];
          console.log(`Extracted RAG file ID from href: ${ragFileId}`);
        }
      }
    }

    // If we still couldn't find a RAG file ID, try to find it in the HTML
    if (!ragFileId) {
      console.log("Couldn't extract RAG file ID from links, trying to find it in the HTML");
      const pageContent = await page.content();
      const matches = pageContent.match(/\/rag-vector\/files\/([a-f0-9]+)/i);
      if (matches && matches[1]) {
        ragFileId = matches[1];
        console.log(`Extracted RAG file ID from HTML: ${ragFileId}`);
      }
    }
  }

  // 16. Verify that we found a valid RAG file ID
  expect(ragFileId).toBeTruthy();
  console.log(`Verified that we found a valid RAG file ID: ${ragFileId}`);

  // 17. Navigate to the RAG file page
  console.log(`💥🥠 Audio->RAG: Navigating to the RAG file page for ID: ${ragFileId}`);
  await page.goto(`${process.env.WEB_URL || 'http://localhost:8080'}/white-label/${whitelabelId}/rag-vector/files/${ragFileId}`);
  await page.waitForTimeout(2000);
  await page.screenshot({ path: 'rag-file-page.png' });

  // 18. Verify that the RAG file page loaded successfully
  const pageContent = await page.content();
  expect(pageContent).toContain(ragFileId);
  console.log("Verified that the RAG file page contains the RAG file ID");

  // 19. Verify that the RAG file content is present
  const contentElement = page.locator('.rag-file-content, .file-content, pre, .content');
  const elementCount = await contentElement.count();
  expect(elementCount).toBeGreaterThan(0);
  console.log("Verified that the RAG file content element is present");

  // 20. Verify that the RAG file content contains text
  if (elementCount > 0) {
    const ragFileContent = await contentElement.first().textContent();
    expect(ragFileContent).not.toBeNull();
    if (ragFileContent) {
      expect(ragFileContent.length).toBeGreaterThan(0);
      console.log(`RAG file content length: ${ragFileContent.length} characters`);
      const sample = ragFileContent.substring(0, 100) + (ragFileContent.length > 100 ? '...' : '');
      console.log(`Content sample: ${sample}`);
    }
  }

  console.log("Audio to RAG status completion check test completed successfully");
});
