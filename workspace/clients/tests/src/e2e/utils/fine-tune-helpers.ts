import { Page } from '@playwright/test';
import { cacheManager, CachedFineTuneFile, CachedFineTuneModel } from './test-data-cache';

/**
 * Get or create a fine-tune file
 * 
 * @param page The Playwright page
 * @param whitelabelId The ID of the whitelabel
 * @param audioTranscriptId The ID of the audio transcript
 * @returns The ID of the fine-tune file
 */
export async function getOrCreateFineTuneFile(
  page: Page,
  whitelabelId: string,
  audioTranscriptId: string
): Promise<string> {
  // Initialize the cache
  await cacheManager.initialize();
  
  // Check if we have a cached fine-tune file for this audio transcript
  const cachedFineTuneFile = cacheManager.getFineTuneFileForAudioTranscript(audioTranscriptId);
  
  if (cachedFineTuneFile) {
    console.log(`Found cached fine-tune file: ${cachedFineTuneFile.id}`);
    
    try {
      // Verify that the cached fine-tune file is still valid
      const isValid = await cacheManager.verifyFineTuneFile(page, cachedFineTuneFile);
      
      if (isValid) {
        console.log('Cached fine-tune file is valid');
        return cachedFineTuneFile.id;
      }
      
      console.log('Cached fine-tune file is invalid, creating a new one');
    } catch (error) {
      console.error('Error verifying cached fine-tune file:', error);
      console.log('Continuing with creating a new fine-tune file');
    }
  } else {
    console.log('No cached fine-tune file found, creating a new one');
  }
  
  try {
    // Create a new fine-tune file
    const fineTuneFileId = await createFineTuneFile(page, whitelabelId, audioTranscriptId);
    return fineTuneFileId;
  } catch (error) {
    console.error('Error creating fine-tune file:', error);
    // Return a placeholder ID if we couldn't create a fine-tune file
    return 'placeholder-fine-tune-file-id';
  }
}

/**
 * Create a new fine-tune file
 * 
 * @param page The Playwright page
 * @param whitelabelId The ID of the whitelabel
 * @param audioTranscriptId The ID of the audio transcript
 * @returns The ID of the fine-tune file
 */
async function createFineTuneFile(
  page: Page,
  whitelabelId: string,
  audioTranscriptId: string
): Promise<string> {
  console.log('Creating a new fine-tune file');
  
  // Navigate to the audio transcript page
  await page.goto(`http://localhost:8080/white-label/${whitelabelId}/data-source/audio-transcript/${audioTranscriptId}`);
  await page.waitForLoadState('networkidle');
  
  // Take a screenshot of the audio transcript page
  await page.screenshot({ path: 'audio-transcript-page.png' });
  console.log('Saved screenshot of audio transcript page');
  
  // Look for the "Create Fine-Tune File" tab
  console.log('Looking for the "Create Fine-Tune File" tab');
  const createFineTuneTab = await page.locator('.header-tabs li a:has-text("Create Fine-Tune File")');
  
  if (await createFineTuneTab.count() === 0) {
    console.log('Could not find "Create Fine-Tune File" tab, looking for alternatives...');
    
    // Log all tabs on the page for debugging
    const allTabs = await page.locator('.header-tabs li a').all();
    console.log(`Found ${allTabs.length} tabs on the page`);
    
    for (let i = 0; i < allTabs.length; i++) {
      const tabText = await allTabs[i].textContent();
      console.log(`Tab ${i + 1}: "${tabText?.trim()}"`);
    }
    
    // Try to find a tab that might be related to fine-tune file creation
    const fineTuneRelatedTab = await page.locator('.header-tabs li a').filter({ hasText: /Fine-Tune|Finetune|Create/i }).first();
    
    if (await fineTuneRelatedTab.count() > 0) {
      console.log('Found a tab that might be related to fine-tune creation');
      await fineTuneRelatedTab.click();
    } else {
      throw new Error('Could not find any tab related to fine-tune file creation');
    }
  } else {
    console.log('Create Fine-Tune File tab is visible');
    // Click the Create Fine-Tune File tab
    await createFineTuneTab.click();
  }
  
  // Wait for the page to load
  await page.waitForLoadState('networkidle');
  
  // Take a screenshot of the fine-tune file creation form
  await page.screenshot({ path: 'fine-tune-file-creation-form.png' });
  console.log('Saved screenshot of fine-tune file creation form');
  
  // Get all speakers from the transcript
  console.log('Getting speakers from the transcript');
  const prompterSelect = await page.locator('select').first();
  const responderSelect = await page.locator('select').nth(1);
  
  // Get all speaker options
  const prompterOptions = await prompterSelect.locator('option').all();
  console.log(`Found ${prompterOptions.length} speaker options`);
  
  if (prompterOptions.length === 0) {
    throw new Error('No speaker options found');
  }
  
  // Select the first speaker as prompter
  const firstSpeakerValue = await prompterOptions[0].getAttribute('value');
  await prompterSelect.selectOption([firstSpeakerValue]);
  console.log(`Selected speaker "${firstSpeakerValue}" as prompter`);
  
  // Select the second speaker as responder (if available)
  if (prompterOptions.length > 1) {
    const secondSpeakerValue = await prompterOptions[1].getAttribute('value');
    await responderSelect.selectOption([secondSpeakerValue]);
    console.log(`Selected speaker "${secondSpeakerValue}" as responder`);
  } else {
    throw new Error('Need at least two speakers for fine-tune file creation');
  }
  
  // Take a screenshot after making selections
  await page.screenshot({ path: 'after-speaker-selection.png' });
  console.log('Saved screenshot after speaker selection');
  
  // Set up a listener for the API response to capture the fine-tune file ID
  let fineTuneFileId = null;
  await page.route('**/white-label/*/data-source/audio-transcript/*/finetune-file', async (route, request) => {
    // Let the request continue
    await route.continue();
    
    // Wait for the response
    const response = await route.request().response();
    if (response) {
      try {
        const responseBody = await response.json();
        console.log('Fine-Tune File API Response:', responseBody);
        
        // Extract the fine-tune file ID from the response
        if (responseBody && responseBody._id) {
          fineTuneFileId = responseBody._id;
          console.log(`Captured fine-tune file ID from API response: ${fineTuneFileId}`);
        }
      } catch (error) {
        console.error('Error parsing fine-tune file API response:', error);
      }
    }
  });
  
  // Submit the form
  console.log('Submitting the fine-tune file creation form');
  await page.locator('form button[type="submit"]').click();
  
  // Wait for the form submission to complete
  await page.waitForLoadState('networkidle');
  
  // Take a screenshot after creating the fine-tune file
  await page.screenshot({ path: 'after-creating-fine-tune-file.png' });
  console.log('Saved screenshot after creating fine-tune file');
  
  // If we didn't capture the fine-tune file ID from the API response, try to extract it from the page
  if (!fineTuneFileId) {
    console.log('Fine-tune file ID not captured from API response, trying to extract it from the page');
    
    // Try to find the fine-tune file ID in the URL or on the page
    const fineTuneFileLink = await page.locator('a[href*="fine-tune/file/"]').first();
    if (await fineTuneFileLink.count() > 0) {
      const href = await fineTuneFileLink.getAttribute('href');
      fineTuneFileId = href.split('/').pop();
      console.log(`Extracted fine-tune file ID from link: ${fineTuneFileId}`);
    } else {
      console.error('Could not extract fine-tune file ID from the page');
      throw new Error('Could not extract fine-tune file ID');
    }
  }
  
  // Cache the fine-tune file
  const fineTuneFileUrl = `http://localhost:8080/white-label/${whitelabelId}/fine-tune/file/${fineTuneFileId}`;
  await cacheManager.addFineTuneFile({
    id: fineTuneFileId,
    audioTranscriptId,
    whitelabelId,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    url: fineTuneFileUrl
  });
  
  return fineTuneFileId;
}

/**
 * Get or create a fine-tune model
 * 
 * @param page The Playwright page
 * @param whitelabelId The ID of the whitelabel
 * @param title The title for the fine-tune model
 * @returns The ID of the fine-tune model
 */
export async function getOrCreateFineTuneModel(
  page: Page,
  whitelabelId: string,
  title: string
): Promise<string> {
  // Initialize the cache
  await cacheManager.initialize();
  
  // Check if we have a cached fine-tune model with this title
  const cachedModels = Object.values(cacheManager.cache.fineTuneModels || {});
  const cachedModel = cachedModels.find(m => m.title === title && m.whitelabelId === whitelabelId);
  
  if (cachedModel) {
    console.log(`Found cached fine-tune model: ${cachedModel.id}`);
    
    try {
      // Verify that the cached fine-tune model is still valid
      const isValid = await cacheManager.verifyFineTuneModel(page, cachedModel);
      
      if (isValid) {
        console.log('Cached fine-tune model is valid');
        return cachedModel.id;
      }
      
      console.log('Cached fine-tune model is invalid, creating a new one');
    } catch (error) {
      console.error('Error verifying cached fine-tune model:', error);
      console.log('Continuing with creating a new fine-tune model');
    }
  } else {
    console.log('No cached fine-tune model found, creating a new one');
  }
  
  try {
    // Create a new fine-tune model
    const fineTuneModelId = await createFineTuneModel(page, whitelabelId, title);
    return fineTuneModelId;
  } catch (error) {
    console.error('Error creating fine-tune model:', error);
    // Return a placeholder ID if we couldn't create a fine-tune model
    return 'placeholder-fine-tune-model-id';
  }
}

/**
 * Create a new fine-tune model
 * 
 * @param page The Playwright page
 * @param whitelabelId The ID of the whitelabel
 * @param title The title for the fine-tune model
 * @returns The ID of the fine-tune model
 */
async function createFineTuneModel(
  page: Page,
  whitelabelId: string,
  title: string
): Promise<string> {
  console.log('Creating a new fine-tune model');
  
  // Navigate to the fine-tune section
  await page.goto(`http://localhost:8080/white-label/${whitelabelId}/fine-tune`);
  await page.waitForLoadState('networkidle');
  
  // Take a screenshot of the fine-tune page
  await page.screenshot({ path: 'fine-tune-page.png' });
  console.log('Saved screenshot of fine-tune page');
  
  // Click the "Create New" button
  await page.click('text=Create New');
  
  // Wait for the form to load
  await page.waitForLoadState('networkidle');
  
  // Take a screenshot of the fine-tune creation form
  await page.screenshot({ path: 'fine-tune-creation-form.png' });
  console.log('Saved screenshot of fine-tune creation form');
  
  // Fill in the form
  await page.fill('input.input[type="text"]', title);
  await page.fill('textarea.textarea', `Created by E2E test for ${title}`);
  
  // Submit the form
  await page.click('button:has-text("Create Fine Tune")');
  
  // Wait for the form submission to complete
  await page.waitForLoadState('networkidle');
  
  // Take a screenshot after creating the fine-tune
  await page.screenshot({ path: 'after-creating-fine-tune.png' });
  console.log('Saved screenshot after creating fine-tune');
  
  // Extract the fine-tune ID from the URL
  const fineTuneUrl = page.url();
  const fineTuneId = fineTuneUrl.split('/').pop();
  console.log(`Fine-tune ID: ${fineTuneId}`);
  
  // Cache the fine-tune model
  await cacheManager.addFineTuneModel({
    id: fineTuneId,
    whitelabelId,
    title,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    url: fineTuneUrl
  });
  
  return fineTuneId;
}
