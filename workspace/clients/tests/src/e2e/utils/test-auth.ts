import { Page } from '@playwright/test';
import { getItem, setItem } from '../../utils/storage';

/**
 * Bypass Auth0 authentication for testing purposes by directly setting the auth state in localStorage
 * This approach is ONLY for testing and should never be used in production
 *
 * @param page The Playwright page object
 */
export async function bypassAuth0Login(page: Page): Promise<void> {
  console.log('Bypassing Auth0 login for testing');

  // Navigate to the application
  await page.goto('http://localhost:8080');

  // Create a mock Auth0 user and token
  const mockUser = {
    email: '<EMAIL>',
    email_verified: true,
    name: 'Test User',
    nickname: 'testuser',
    picture: 'https://example.com/avatar.png',
    sub: 'auth0|test123456',
    updated_at: new Date().toISOString()
  };

  // Create a mock token (this is just for testing, not a real token)
  const mockToken = {
    access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************.fake_signature',
    expires_in: 86400,
    token_type: 'Bearer',
    decodedToken: {
      claims: {
        sub: 'auth0|test123456',
        name: 'Test User',
        email: '<EMAIL>',
        exp: Math.floor(Date.now() / 1000) + 86400 // 24 hours from now
      }
    }
  };

  // Set the Auth0 state in localStorage
  await page.evaluate(({ user, token }) => {
    // Auth0 stores data with specific keys
    localStorage.setItem('auth0.is.authenticated', 'true');
    localStorage.setItem('auth0.user', JSON.stringify(user));

    // Store the token
    const tokenKey = Object.keys(localStorage).find(key =>
      key.startsWith('@@auth0spajs@@') && key.includes('default::')
    ) || '@@auth0spajs@@::default::audience::https://api.divinci.app';

    if (tokenKey) {
      localStorage.setItem(tokenKey, JSON.stringify(token));
    }
  }, { user: mockUser, token: mockToken });

  // Reload the page to apply the auth state
  await page.reload();
  await page.waitForLoadState('networkidle');

  console.log('Auth0 login bypassed successfully');
}

/**
 * Check if the user is already authenticated
 *
 * @param page The Playwright page object
 * @returns True if authenticated, false otherwise
 */
export async function isAuthenticated(page: Page): Promise<boolean> {
  return await page.evaluate(() => {
    return localStorage.getItem('auth0.is.authenticated') === 'true';
  });
}
