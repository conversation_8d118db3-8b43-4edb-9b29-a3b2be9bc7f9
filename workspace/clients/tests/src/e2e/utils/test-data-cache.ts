import { Page } from '@playwright/test';
import fs from 'fs';
import path from 'path';

/**
 * Interface for cached audio transcript data
 */
interface CachedAudioTranscript {
  id: string;
  whitelabelId: string;
  fileName: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  url: string;
}

/**
 * Interface for cached RAG file data
 */
interface CachedRagFile {
  id: string;
  audioTranscriptId: string;
  whitelabelId: string;
  fileName: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  url: string;
}

/**
 * Interface for cached fine-tune file data
 */
export interface CachedFineTuneFile {
  id: string;
  audioTranscriptId: string;
  whitelabelId: string;
  createdAt: string;
  updatedAt: string;
  url: string;
}

/**
 * Interface for cached fine-tune model data
 */
export interface CachedFineTuneModel {
  id: string;
  whitelabelId: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  url: string;
}

/**
 * Interface for the test data cache
 */
interface TestDataCache {
  audioTranscripts: Record<string, CachedAudioTranscript>;
  ragFiles: Record<string, CachedRagFile>;
  fineTuneFiles: Record<string, CachedFineTuneFile>;
  fineTuneModels: Record<string, CachedFineTuneModel>;
  lastUpdated: string;
}

/**
 * Class for managing test data cache
 */
export class TestDataCacheManager {
  private cachePath: string;
  private cache: TestDataCache;
  private initialized: boolean = false;

  constructor(cachePath?: string) {
    this.cachePath = cachePath || path.resolve(__dirname, '../../../.test-data-cache.json');
    this.cache = {
      audioTranscripts: {},
      ragFiles: {},
      fineTuneFiles: {},
      fineTuneModels: {},
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Initialize the cache
   * @returns A promise that resolves when the cache is initialized
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Check if the cache file exists
      if (fs.existsSync(this.cachePath)) {
        // Read the cache file
        const cacheData = fs.readFileSync(this.cachePath, 'utf8');
        
        // Parse the cache data
        this.cache = JSON.parse(cacheData);
        
        // Update the last updated timestamp
        this.cache.lastUpdated = new Date().toISOString();
        
        // Initialize empty objects if they don't exist
        this.cache.audioTranscripts = this.cache.audioTranscripts || {};
        this.cache.ragFiles = this.cache.ragFiles || {};
        this.cache.fineTuneFiles = this.cache.fineTuneFiles || {};
        this.cache.fineTuneModels = this.cache.fineTuneModels || {};
      } else {
        // Create a new cache if the file doesn't exist
        this.cache = {
          audioTranscripts: {},
          ragFiles: {},
          fineTuneFiles: {},
          fineTuneModels: {},
          lastUpdated: new Date().toISOString()
        };
      }
    } catch (error) {
      console.error('Error initializing cache:', error);
      
      // Create a new cache if there was an error
      this.cache = {
        audioTranscripts: {},
        ragFiles: {},
        fineTuneFiles: {},
        fineTuneModels: {},
        lastUpdated: new Date().toISOString()
      };
    }
    
    this.initialized = true;
  }

  /**
   * Save the cache to disk
   * @returns A promise that resolves when the cache is saved
   */
  public async saveCache(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      // Update the last updated timestamp
      this.cache.lastUpdated = new Date().toISOString();
      
      // Write the cache to disk
      fs.writeFileSync(this.cachePath, JSON.stringify(this.cache, null, 2));
    } catch (error) {
      console.error('Error saving cache:', error);
    }
  }

  /**
   * Get a cached audio transcript by ID
   * @param id The ID of the audio transcript
   * @returns The cached audio transcript or null if not found
   */
  public getAudioTranscript(id: string): CachedAudioTranscript | null {
    if (!this.initialized) {
      console.warn('Cache not initialized, call initialize() first');
      return null;
    }
    
    return this.cache.audioTranscripts[id] || null;
  }

  /**
   * Get a cached audio transcript by file name
   * @param fileName The name of the audio file
   * @returns The cached audio transcript or null if not found
   */
  public getAudioTranscriptByFileName(fileName: string): CachedAudioTranscript | null {
    if (!this.initialized) {
      console.warn('Cache not initialized, call initialize() first');
      return null;
    }
    
    const audioTranscript = Object.values(this.cache.audioTranscripts).find(a => a.fileName === fileName);
    return audioTranscript || null;
  }

  /**
   * Add an audio transcript to the cache
   * @param audioTranscript The audio transcript to cache
   */
  public async addAudioTranscript(audioTranscript: CachedAudioTranscript): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
    
    this.cache.audioTranscripts[audioTranscript.id] = audioTranscript;
    await this.saveCache();
  }

  /**
   * Get a cached RAG file by ID
   * @param id The ID of the RAG file
   * @returns The cached RAG file or null if not found
   */
  public getRagFile(id: string): CachedRagFile | null {
    if (!this.initialized) {
      console.warn('Cache not initialized, call initialize() first');
      return null;
    }
    
    return this.cache.ragFiles[id] || null;
  }

  /**
   * Get a cached RAG file by audio transcript ID
   * @param audioTranscriptId The ID of the audio transcript
   * @returns The cached RAG file or null if not found
   */
  public getRagFileForAudioTranscript(audioTranscriptId: string): CachedRagFile | null {
    if (!this.initialized) {
      console.warn('Cache not initialized, call initialize() first');
      return null;
    }
    
    const ragFile = Object.values(this.cache.ragFiles).find(r => r.audioTranscriptId === audioTranscriptId);
    return ragFile || null;
  }

  /**
   * Add a RAG file to the cache
   * @param ragFile The RAG file to cache
   */
  public async addRagFile(ragFile: CachedRagFile): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
    
    this.cache.ragFiles[ragFile.id] = ragFile;
    await this.saveCache();
  }

  /**
   * Get a cached fine-tune file by ID
   * @param id The ID of the fine-tune file
   * @returns The cached fine-tune file or null if not found
   */
  public getFineTuneFile(id: string): CachedFineTuneFile | null {
    if (!this.initialized) {
      console.warn('Cache not initialized, call initialize() first');
      return null;
    }
    
    return this.cache.fineTuneFiles[id] || null;
  }

  /**
   * Get a cached fine-tune file by audio transcript ID
   * @param audioTranscriptId The ID of the audio transcript
   * @returns The cached fine-tune file or null if not found
   */
  public getFineTuneFileForAudioTranscript(audioTranscriptId: string): CachedFineTuneFile | null {
    if (!this.initialized) {
      console.warn('Cache not initialized, call initialize() first');
      return null;
    }
    
    const fineTuneFile = Object.values(this.cache.fineTuneFiles).find(f => f.audioTranscriptId === audioTranscriptId);
    return fineTuneFile || null;
  }

  /**
   * Add a fine-tune file to the cache
   * @param fineTuneFile The fine-tune file to cache
   */
  public async addFineTuneFile(fineTuneFile: CachedFineTuneFile): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
    
    this.cache.fineTuneFiles[fineTuneFile.id] = fineTuneFile;
    await this.saveCache();
  }

  /**
   * Get a cached fine-tune model by ID
   * @param id The ID of the fine-tune model
   * @returns The cached fine-tune model or null if not found
   */
  public getFineTuneModel(id: string): CachedFineTuneModel | null {
    if (!this.initialized) {
      console.warn('Cache not initialized, call initialize() first');
      return null;
    }
    
    return this.cache.fineTuneModels[id] || null;
  }

  /**
   * Add a fine-tune model to the cache
   * @param fineTuneModel The fine-tune model to cache
   */
  public async addFineTuneModel(fineTuneModel: CachedFineTuneModel): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
    
    this.cache.fineTuneModels[fineTuneModel.id] = fineTuneModel;
    await this.saveCache();
  }

  /**
   * Verify that a cached audio transcript is still valid
   * @param page The Playwright page
   * @param audioTranscript The cached audio transcript
   * @returns True if the audio transcript is still valid, false otherwise
   */
  public async verifyAudioTranscript(page: Page, audioTranscript: CachedAudioTranscript): Promise<boolean> {
    try {
      console.log(`Verifying cached audio transcript: ${audioTranscript.id}`);
      
      // Navigate to the audio transcript URL
      await page.goto(audioTranscript.url, { timeout: 10000 });
      await page.waitForLoadState('networkidle', { timeout: 10000 });
      
      // Check if the audio transcript exists
      const audioTranscriptExists = await page.locator('h1:has-text("Audio Transcript")').count() > 0 ||
                                   await page.locator('.audio-transcript').count() > 0;
      
      return audioTranscriptExists;
    } catch (error) {
      console.error('Error verifying audio transcript:', error);
      return false;
    }
  }

  /**
   * Verify that a cached RAG file is still valid
   * @param page The Playwright page
   * @param ragFile The cached RAG file
   * @returns True if the RAG file is still valid, false otherwise
   */
  public async verifyRagFile(page: Page, ragFile: CachedRagFile): Promise<boolean> {
    try {
      console.log(`Verifying cached RAG file: ${ragFile.id}`);
      
      // Navigate to the RAG file URL
      await page.goto(ragFile.url, { timeout: 10000 });
      await page.waitForLoadState('networkidle', { timeout: 10000 });
      
      // Check if the RAG file exists
      const ragFileExists = await page.locator('h1:has-text("RAG")').count() > 0 ||
                           await page.locator('.rag-content').count() > 0;
      
      return ragFileExists;
    } catch (error) {
      console.error('Error verifying RAG file:', error);
      return false;
    }
  }

  /**
   * Verify that a cached fine-tune file is still valid
   * @param page The Playwright page
   * @param fineTuneFile The cached fine-tune file
   * @returns True if the fine-tune file is still valid, false otherwise
   */
  public async verifyFineTuneFile(page: Page, fineTuneFile: CachedFineTuneFile): Promise<boolean> {
    try {
      console.log(`Verifying cached fine-tune file: ${fineTuneFile.id}`);
      
      // Navigate to the fine-tune file URL
      await page.goto(fineTuneFile.url, { timeout: 10000 });
      await page.waitForLoadState('networkidle', { timeout: 10000 });
      
      // Check if the fine-tune file exists
      const fineTuneFileExists = await page.locator('h1:has-text("Fine Tune File")').count() > 0 ||
                                await page.locator('.fine-tune-file').count() > 0;
      
      return fineTuneFileExists;
    } catch (error) {
      console.error('Error verifying fine-tune file:', error);
      return false;
    }
  }

  /**
   * Verify that a cached fine-tune model is still valid
   * @param page The Playwright page
   * @param fineTuneModel The cached fine-tune model
   * @returns True if the fine-tune model is still valid, false otherwise
   */
  public async verifyFineTuneModel(page: Page, fineTuneModel: CachedFineTuneModel): Promise<boolean> {
    try {
      console.log(`Verifying cached fine-tune model: ${fineTuneModel.id}`);
      
      // Navigate to the fine-tune model URL
      await page.goto(fineTuneModel.url, { timeout: 10000 });
      await page.waitForLoadState('networkidle', { timeout: 10000 });
      
      // Check if the fine-tune model exists
      const fineTuneModelExists = await page.locator('text=Base Model:').count() > 0 ||
                                 await page.locator('text=Currently Tuning:').count() > 0;
      
      return fineTuneModelExists;
    } catch (error) {
      console.error('Error verifying fine-tune model:', error);
      return false;
    }
  }
}

// Export a singleton instance of the cache manager
export const cacheManager = new TestDataCacheManager();
