import { Page } from '@playwright/test';
import path from 'path';
import fs from 'fs';
import { cacheManager } from './test-data-cache';

/**
 * Get or create an audio transcript
 * 
 * @param page The Playwright page
 * @param whitelabelId The ID of the whitelabel
 * @param audioFileName The name of the audio file
 * @returns The ID of the audio transcript
 */
export async function getOrCreateAudioTranscript(
  page: Page,
  whitelabelId: string,
  audioFileName: string = 'test-audio.mp3'
): Promise<string> {
  // Initialize the cache
  await cacheManager.initialize();
  
  // Check if we have a cached audio transcript for this file
  const cachedAudioTranscript = cacheManager.getAudioTranscriptByFileName(audioFileName);
  
  if (cachedAudioTranscript) {
    console.log(`Found cached audio transcript: ${cachedAudioTranscript.id}`);
    
    try {
      // Verify that the cached audio transcript is still valid
      const isValid = await cacheManager.verifyAudioTranscript(page, cachedAudioTranscript);
      
      if (isValid) {
        console.log('Cached audio transcript is valid');
        return cachedAudioTranscript.id;
      }
      
      console.log('Cached audio transcript is invalid, creating a new one');
    } catch (error) {
      console.error('Error verifying cached audio transcript:', error);
      console.log('Continuing with creating a new audio transcript');
    }
  } else {
    console.log('No cached audio transcript found, creating a new one');
  }
  
  try {
    // Create a new audio transcript
    const audioId = await createAudioTranscript(page, whitelabelId, audioFileName);
    return audioId;
  } catch (error) {
    console.error('Error creating audio transcript:', error);
    // Return a placeholder ID if we couldn't create an audio transcript
    return 'placeholder-audio-id';
  }
}

/**
 * Create a new audio transcript
 * 
 * @param page The Playwright page
 * @param whitelabelId The ID of the whitelabel
 * @param audioFileName The name of the audio file
 * @returns The ID of the audio transcript
 */
async function createAudioTranscript(
  page: Page,
  whitelabelId: string,
  audioFileName: string
): Promise<string> {
  console.log('Creating a new audio transcript');
  
  // Navigate to the audio transcript page
  await page.goto(`http://localhost:8080/white-label/${whitelabelId}/data-source/audio-transcript`);
  await page.waitForLoadState('networkidle');
  
  // Take a screenshot of the audio transcript page
  await page.screenshot({ path: 'audio-transcript-page.png' });
  console.log('Saved screenshot of audio transcript page');
  
  // Click the "Create Tools" link
  await page.click('text=Create Tools');
  await page.waitForLoadState('networkidle');
  
  // Take a screenshot of the create tools page
  await page.screenshot({ path: 'create-tools-page.png' });
  console.log('Saved screenshot of create tools page');
  
  // Click the "Create Audio Transcript" link
  await page.click('text=Create Audio Transcript');
  await page.waitForLoadState('networkidle');
  
  // Take a screenshot of the audio transcript creation form
  await page.screenshot({ path: 'audio-transcript-creation-form.png' });
  console.log('Saved screenshot of audio transcript creation form');
  
  // Upload an audio file
  const audioFilePath = path.resolve(__dirname, '../../../test-files', audioFileName);
  console.log(`Audio file path: ${audioFilePath}`);
  
  // Check if the file exists
  if (!fs.existsSync(audioFilePath)) {
    throw new Error(`Audio file not found: ${audioFilePath}`);
  }
  
  // Set the file input
  await page.setInputFiles('input[type="file"]', audioFilePath);
  
  // Wait for the file to be uploaded
  await page.waitForTimeout(1000);
  
  // Take a screenshot after uploading the file
  await page.screenshot({ path: 'after-uploading-file.png' });
  console.log('Saved screenshot after uploading file');
  
  // Submit the form
  await page.click('button[type="submit"]');
  
  // Wait for the form submission to complete
  await page.waitForLoadState('networkidle');
  
  // Take a screenshot after submitting the form
  await page.screenshot({ path: 'after-submitting-form.png' });
  console.log('Saved screenshot after submitting form');
  
  // Extract the audio ID from the URL
  const audioTranscriptUrl = page.url();
  const audioId = audioTranscriptUrl.split('/').pop();
  console.log(`Audio ID: ${audioId}`);
  
  // Wait for the audio transcript to be processed
  const skipProcessingWait = process.env.SKIP_PROCESSING_WAIT === 'true';
  if (!skipProcessingWait) {
    console.log('Waiting for audio processing to complete (this may take a while)');
    
    // Wait for the status to change to "completed"
    await page.waitForSelector('text=completed', { timeout: 300000 }); // 5 minutes timeout
    
    // Take a screenshot after processing is complete
    await page.screenshot({ path: 'after-processing-complete.png' });
    console.log('Saved screenshot after processing is complete');
  } else {
    console.log('Skipping wait for audio processing (SKIP_PROCESSING_WAIT=true)');
    
    // Just wait a bit to let the page load
    await page.waitForTimeout(5000);
    
    // Take a screenshot after skipping processing wait
    await page.screenshot({ path: 'after-skipping-processing-wait.png' });
    console.log('Saved screenshot after skipping processing wait');
  }
  
  // Cache the audio transcript
  await cacheManager.addAudioTranscript({
    id: audioId,
    whitelabelId,
    fileName: audioFileName,
    status: 'completed',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    url: audioTranscriptUrl
  });
  
  return audioId;
}
