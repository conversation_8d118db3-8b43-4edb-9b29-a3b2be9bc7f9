import { Page, expect } from '@playwright/test';

/**
 * Utility functions for E2E tests
 */

/**
 * Verifies that the user is logged in
 * This is a simple check that can be used at the start of tests
 * The actual login is handled by the storage state in the Playwright config
 * @param page The Playwright page object
 */
export async function verifyLoggedIn(page: Page): Promise<void> {
  console.log('Verifying user is logged in');

  // Navigate to the home page if not already there
  if (!page.url().includes('localhost:8080')) {
    console.log('Navigating to home page');
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
  }

  // Check if we're logged in - look for any user menu or profile indicator
  console.log('Checking for organization menu or user menu');
  try {
    const organizationMenu = page.locator('a.navbar-link[href="/user-group"]:has-text("Organization"), .user-menu, .profile-menu, .navbar-item.has-dropdown').first();
    await expect(organizationMenu).toBeVisible({ timeout: 15000 });
    console.log('Organization menu detected, login confirmed');
  } catch (error) {
    console.log('Could not detect organization menu, trying alternative verification');
    // Alternative verification - check if we're not on the login page
    const loginForm = await page.locator('form.login-form, input[name="username"]').count();
    if (loginForm === 0) {
      console.log('Login form not visible, assuming logged in');
    } else {
      throw new Error('Login verification failed - user appears to be logged out. Check the auth.setup.ts file and auth.json storage state.');
    }
  }
}

/**
 * Alias for verifyLoggedIn for backward compatibility
 * @param page The Playwright page object
 */
export const ensureLoggedIn = verifyLoggedIn;

/**
 * Navigates to the whitelabel page
 * @param page The Playwright page object
 */
export async function navigateToWhitelabelPage(page: Page): Promise<void> {
  console.log('Navigating to whitelabel page');

  // Try to navigate directly to the whitelabel page first
  console.log('Navigating directly to the whitelabel page');
  await page.goto('http://localhost:8080/white-label');

  // If that doesn't work, try using the menu
  if (await page.locator('a.navbar-link[href="/user-group"]:has-text("Organization")').count() > 0) {
    // Hover over Organization menu in the header
    console.log('Hovering over Organization menu in the header');
    await page.hover('a.navbar-link[href="/user-group"]:has-text("Organization")');

    // Wait a moment for the dropdown to appear
    await page.waitForTimeout(1000);

    // Click on Whitelabel option in the dropdown
    console.log('Clicking on Whitelabel option in the dropdown');
    await page.click('a.dropdown-item[href="/white-label"]:has-text("Whitelabel")');
  }

  // Wait for the whitelabel page to load
  await page.waitForLoadState('networkidle');

  // Check if we're on the whitelabel list page or the create whitelabel page
  const createHeader = page.locator('h1:has-text("Create A New WhiteLabel Language Model")');
  const listHeader = page.locator('h1:has-text("Whitelabel")');

  // Wait for either header to be visible
  await Promise.race([
    createHeader.waitFor({ state: 'visible', timeout: 5000 }).catch(() => {}),
    listHeader.waitFor({ state: 'visible', timeout: 5000 }).catch(() => {})
  ]);

  // Check which page we landed on
  if (await createHeader.count() > 0) {
    console.log('Navigated to create whitelabel page');
  } else if (await listHeader.count() > 0) {
    console.log('Navigated to whitelabel list page');
  } else {
    console.log('Warning: Could not confirm whitelabel page loaded by finding expected headers');
  }
}

/**
 * Gets the ID of the first whitelabel in the list
 * @param page The Playwright page object
 * @returns The ID of the first whitelabel
 */
export async function getFirstWhitelabelId(page: Page): Promise<string> {
  const whitelabelHref = await page.locator('.whitelabel-item a').first().getAttribute('href');
  if (!whitelabelHref) {
    throw new Error('Could not find whitelabel href');
  }
  const whitelabelId = whitelabelHref.split('/').pop() || '';
  if (!whitelabelId) {
    throw new Error('Could not extract whitelabel ID from href');
  }
  console.log(`Using whitelabel ID: ${whitelabelId}`);
  return whitelabelId;
}

/**
 * Ensures that at least one whitelabel exists
 * If no whitelabels exist, it creates a new one
 * @param page The Playwright page object
 */
export async function ensureWhitelabelExists(page: Page): Promise<void> {
  console.log('Ensuring at least one whitelabel exists');

  // Navigate to the whitelabel page
  await navigateToWhitelabelPage(page);

  // Check if we're already on the create whitelabel page
  const createHeader = page.locator('h1:has-text("Create A New WhiteLabel Language Model")');
  if (await createHeader.count() > 0) {
    console.log('Already on create whitelabel page, proceeding with creation');
  } else {
    // Check if there are any whitelabels
    const existingWhitelabels = await page.locator('.whitelabel-item').count();
    console.log(`Found ${existingWhitelabels} existing whitelabels`);

    if (existingWhitelabels === 0) {
      console.log('No whitelabels found. Creating a new whitelabel for testing.');

      // Look for any button that might be the Create Whitelabel button
      console.log('Looking for Create Whitelabel button');

      // Take a screenshot to see what's on the page
      await page.screenshot({ path: 'whitelabel-page.png' });

      // Try different selectors for the Create Whitelabel button
      const createWhitelabelButton = page.locator('text=Create Whitelabel, button:has-text("Create Whitelabel"), .create-whitelabel-button, button.is-primary');

      if (await createWhitelabelButton.count() > 0) {
        console.log('Found Create Whitelabel button, clicking it');
        await createWhitelabelButton.first().click();
      } else {
        console.log('Create Whitelabel button not found, trying to navigate directly to create form');
        await page.goto('http://localhost:8080/white-label/create');
      }

      // Wait for the create whitelabel form to load
      await page.waitForLoadState('networkidle');

      // Wait for the create header to be visible
      await createHeader.waitFor({ state: 'visible', timeout: 5000 }).catch(() => {
        console.log('Warning: Create whitelabel header not found after clicking Create Whitelabel button');
      });
    } else {
      console.log('Existing whitelabel found, proceeding with test');
      return;
    }
  }

  // Generate a unique name for the whitelabel
  const timestamp = new Date().getTime();
  const whitelabelName = `Test Whitelabel ${timestamp}`;

  // Fill out the whitelabel form
  console.log('Filling out the whitelabel form');

  // Fill in the title field
  console.log('Filling in title field');
  await page.fill('input.input[required]', whitelabelName);

  // Fill in the description field
  console.log('Filling in description field');
  await page.fill('textarea#description', 'This is a test whitelabel created for E2E testing');

  // Check the terms and conditions checkbox
  await page.check('input[type="checkbox"]');

  // Take a screenshot of the filled form
  await page.screenshot({ path: 'filled-whitelabel-form.png' });
  console.log('Saved screenshot of filled whitelabel form');

  // Click the Create AI button
  console.log('Clicking Create AI button');
  await page.click('button.create-whitelabel-button');

  // Wait for the whitelabel to be created and redirected
  await page.waitForLoadState('networkidle');

  // Wait a moment for the server to process the whitelabel creation
  console.log('Waiting briefly for server to process whitelabel creation');
  await page.waitForTimeout(3000);

  // Navigate back to the whitelabel page
  console.log('Navigating back to whitelabel list page');
  await page.goto('http://localhost:8080/white-label');
  await page.waitForLoadState('networkidle');

  // Verify we're on the whitelabel list page
  const listHeader = page.locator('h1:has-text("Whitelabel")');
  if (await listHeader.count() > 0) {
    console.log('Successfully returned to whitelabel list page');
  } else {
    console.log('Warning: Could not confirm return to whitelabel list page');
    // Take a screenshot to help diagnose the issue
    await page.screenshot({ path: 'after-whitelabel-creation.png' });
  }

  console.log('Created new whitelabel successfully');
}

/**
 * Navigates to the audio transcript page for a specific whitelabel
 * @param page The Playwright page object
 * @param whitelabelId The ID of the whitelabel
 */
export async function navigateToAudioTranscriptPage(page: Page, whitelabelId: string): Promise<void> {
  console.log('Navigating to audio transcript page');

  try {
    // Click on the whitelabel to enter it
    console.log(`Clicking on whitelabel with ID: ${whitelabelId}`);
    await page.click(`.whitelabel-item a[href*="${whitelabelId}"]`);
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the whitelabel detail page
    await page.screenshot({ path: 'whitelabel-detail-page.png' });
    console.log('Saved screenshot of whitelabel detail page');

    // Hover over the chat-title dropdown trigger
    const dropdownTrigger = page.locator('.dropdown-trigger');
    console.log('Looking for dropdown trigger');

    // Wait for the dropdown trigger to be visible
    await dropdownTrigger.waitFor({ state: 'visible', timeout: 5000 });

    // Hover over the dropdown trigger
    await dropdownTrigger.hover();
    console.log('Hovering over dropdown trigger');

    // Wait a moment for the dropdown to appear
    await page.waitForTimeout(1000);

    // Take a screenshot of the dropdown
    await page.screenshot({ path: 'dropdown-menu.png' });
    console.log('Saved screenshot of dropdown menu');

    // Click on the Audio Transcripts option in the dropdown
    console.log('Clicking on Audio Transcripts option');
    await page.click('a.dropdown-item:has-text("📝 Audio Transcripts")');
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the audio transcript page
    await page.screenshot({ path: 'audio-transcript-page.png' });
    console.log('Saved screenshot of audio transcript page');

    console.log('Navigated to audio transcript page');
  } catch (error) {
    console.error('Error navigating to audio transcript page:', error);
    // Take a screenshot to help diagnose the issue
    await page.screenshot({ path: 'navigation-error.png' });
    throw error;
  }
}
