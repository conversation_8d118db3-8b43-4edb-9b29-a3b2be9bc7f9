# Resource-Intensive Tests

This directory contains tests that are resource-intensive and should be run sequentially rather than in parallel.

## What makes a test resource-intensive?

A test is considered resource-intensive if it:

1. Takes a long time to run (more than 30 seconds)
2. Uses significant system resources (CPU, memory, network)
3. Interacts with external services that have rate limits
4. Performs operations that could conflict with other tests if run in parallel

## Tests in this directory

- `audio-transcript-upload.spec.ts`: Tests uploading an audio file and creating an audio transcript
- `audio-transcript-to-rag.spec.ts`: Tests creating a RAG file from an audio transcript
- `audio-transcript-to-fine-tune.spec.ts`: Tests creating a fine-tune file from an audio transcript and then creating a fine-tuned AI model

## Running these tests

These tests should be run sequentially to avoid conflicts. You can run them using the following command:

```bash
npx playwright test src/e2e/resource-intensive/ --workers=1
```

Or use the provided script:

```bash
./run-optimized-tests.sh
```

## Skipping long waits

Some tests in this directory involve waiting for long-running processes to complete, such as audio processing. You can skip these waits by setting the `SKIP_PROCESSING_WAIT` environment variable to `true`:

```bash
SKIP_PROCESSING_WAIT=true npx playwright test src/e2e/resource-intensive/ --workers=1
```

Or use the provided script:

```bash
./run-fast-tests.sh
```

## Test data caching

These tests use a caching mechanism to avoid recreating resources that already exist. This helps reduce test execution time and resource usage.

The cache is stored in the `.test-data-cache.json` file in the root of the tests directory.
