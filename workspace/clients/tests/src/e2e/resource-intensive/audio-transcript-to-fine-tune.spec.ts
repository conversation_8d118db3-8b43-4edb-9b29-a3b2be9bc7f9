import { test, expect, Page } from '@playwright/test';
import path from 'path';
import fs from 'fs';
import { createScreenshotPathFactory } from '../../utils/screenshot';

// Create a screenshot path factory for this test file
const getScreenshotPath = createScreenshotPathFactory(__dirname);

/**
 * Test for creating a fine-tune file from an audio transcript and then creating a fine-tuned AI model
 *
 * This test:
 * 1. Creates an audio transcript from an audio file
 * 2. Creates a fine-tune file from the audio transcript
 * 3. Creates a fine-tuned AI model using the fine-tune file
 * 4. Verifies that the fine-tuned AI model was created successfully
 * 5. Cleans up by deleting the fine-tuned AI model
 */
test('Create fine-tune file from audio transcript and create fine-tuned AI model', async ({ page }) => {
  // Step 0: Login and navigate to the whitelabel page
  console.log('Logging in and navigating to the whitelabel page');

  // Navigate to the home page
  await page.goto('http://localhost:8080');
  await page.waitForLoadState('networkidle');

  // Take a screenshot of the home page
  await page.screenshot({ path: getScreenshotPath('home-page.png') });
  console.log('Saved screenshot of home page');

  // Check if we're already logged in
  const isLoggedIn = await page.locator('text=Organization').count() > 0;

  if (!isLoggedIn) {
    console.log('Not logged in, logging in now');

    // Click the login button
    await page.click('a:has-text("Login")');
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the login form
    await page.screenshot({ path: getScreenshotPath('login-form.png') });
    console.log('Saved screenshot of login form');

    // Fill in the login form
    await page.fill('input[name="username"]', process.env.E2E_TEST_USERNAME || '<EMAIL>');
    await page.fill('input[name="password"]', process.env.E2E_TEST_PASSWORD || 'password');

    // Take a screenshot of the filled login form
    await page.screenshot({ path: getScreenshotPath('filled-login-form.png') });
    console.log('Saved screenshot of filled login form');

    // Click the login button
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');

    // Take a screenshot after login attempt
    await page.screenshot({ path: getScreenshotPath('after-login-attempt.png') });
    console.log('Saved screenshot after login attempt');

    // Wait for the Organization link to appear, indicating successful login
    await page.waitForSelector('text=Organization', { timeout: 10000 });

    // Take a screenshot after successful login
    await page.screenshot({ path: getScreenshotPath('after-login-navigation.png') });
    console.log('Saved screenshot after successful login');
  } else {
    console.log('Already logged in');
  }

  // Navigate to the whitelabel page
  console.log('Navigating to the whitelabel page');

  // Click the Organization link
  await page.click('text=Organization');

  // Click the Whitelabel link in the dropdown
  await page.click('a:has-text("Whitelabel")');
  await page.waitForLoadState('networkidle');

  // Take a screenshot of the whitelabel page
  await page.screenshot({ path: getScreenshotPath('whitelabel-page.png') });
  console.log('Saved screenshot of whitelabel page');

  // Get the first whitelabel ID from the list
  const whitelabelLink = await page.locator('a[href^="/white-label/"]').first();
  const whitelabelHref = await whitelabelLink.getAttribute('href');
  const whitelabelId = whitelabelHref.split('/').pop();
  console.log(`Using whitelabel ID: ${whitelabelId}`);

  // Navigate to the audio transcript page
  console.log('Navigating to the audio transcript page');
  await page.goto(`http://localhost:8080/white-label/${whitelabelId}/data-source/audio-transcript`);
  await page.waitForLoadState('networkidle');

  // Take a screenshot of the audio transcript page
  await page.screenshot({ path: getScreenshotPath('audio-transcript-page.png') });
  console.log('Saved screenshot of audio transcript page');

  // Step 1: Create an audio transcript from an audio file
  console.log('Creating an audio transcript from an audio file');

  // Click the "Create Tools" link
  await page.click('text=Create Tools');
  await page.waitForLoadState('networkidle');

  // Take a screenshot of the create tools page
  await page.screenshot({ path: getScreenshotPath('create-tools-page.png') });
  console.log('Saved screenshot of create tools page');

  // Click the "Create Audio Transcript" link
  await page.click('text=Create Audio Transcript');
  await page.waitForLoadState('networkidle');

  // Take a screenshot of the audio transcript creation form
  await page.screenshot({ path: getScreenshotPath('audio-transcript-creation-form.png') });
  console.log('Saved screenshot of audio transcript creation form');

  // Upload an audio file
  const audioFilePath = path.resolve(__dirname, '../../../test-files/test-audio.mp3');
  console.log(`Audio file path: ${audioFilePath}`);

  // Check if the file exists
  if (!fs.existsSync(audioFilePath)) {
    throw new Error(`Audio file not found: ${audioFilePath}`);
  }

  // Set the file input
  await page.setInputFiles('input[type="file"]', audioFilePath);

  // Wait for the file to be uploaded
  await page.waitForTimeout(1000);

  // Take a screenshot after uploading the file
  await page.screenshot({ path: getScreenshotPath('after-uploading-file.png') });
  console.log('Saved screenshot after uploading file');

  // Submit the form
  await page.click('button[type="submit"]');

  // Wait for the form submission to complete
  await page.waitForLoadState('networkidle');

  // Take a screenshot after submitting the form
  await page.screenshot({ path: getScreenshotPath('after-submitting-form.png') });
  console.log('Saved screenshot after submitting form');

  // Wait for the audio transcript to be processed
  console.log('Waiting for the audio transcript to be processed');

  // Extract the audio ID from the URL
  const audioTranscriptUrl = page.url();
  const audioId = audioTranscriptUrl.split('/').pop();
  console.log(`Audio ID: ${audioId}`);

  // Wait for the audio transcript to be processed
  const skipProcessingWait = process.env.SKIP_PROCESSING_WAIT === 'true';
  if (!skipProcessingWait) {
    console.log('Waiting for audio processing to complete (this may take a while)');

    // Wait for the status to change to "completed"
    await page.waitForSelector('text=completed', { timeout: 300000 }); // 5 minutes timeout

    // Take a screenshot after processing is complete
    await page.screenshot({ path: getScreenshotPath('after-processing-complete.png') });
    console.log('Saved screenshot after processing is complete');
  } else {
    console.log('Skipping wait for audio processing (SKIP_PROCESSING_WAIT=true)');

    // Just wait a bit to let the page load
    await page.waitForTimeout(5000);

    // Take a screenshot after skipping processing wait
    await page.screenshot({ path: getScreenshotPath('after-skipping-processing-wait.png') });
    console.log('Saved screenshot after skipping processing wait');
  }

  // Step 2: Create a fine-tune file from the audio transcript
  console.log('Creating a fine-tune file from the audio transcript');

  // Look for the "Create Fine-Tune File" tab
  console.log('Looking for the "Create Fine-Tune File" tab');
  const createFineTuneTab = await page.locator('.header-tabs li a:has-text("Create Fine-Tune File")');

  if (await createFineTuneTab.count() === 0) {
    console.log('Could not find "Create Fine-Tune File" tab, looking for alternatives...');

    // Log all tabs on the page for debugging
    const allTabs = await page.locator('.header-tabs li a').all();
    console.log(`Found ${allTabs.length} tabs on the page`);

    for (let i = 0; i < allTabs.length; i++) {
      const tabText = await allTabs[i].textContent();
      console.log(`Tab ${i + 1}: "${tabText?.trim()}"`);
    }

    // Try to find a tab that might be related to fine-tune file creation
    const fineTuneRelatedTab = await page.locator('.header-tabs li a').filter({ hasText: /Fine-Tune|Finetune|Create/i }).first();

    if (await fineTuneRelatedTab.count() > 0) {
      console.log('Found a tab that might be related to fine-tune creation');
      await fineTuneRelatedTab.click();
    } else {
      throw new Error('Could not find any tab related to fine-tune file creation');
    }
  } else {
    console.log('Create Fine-Tune File tab is visible');
    // Click the Create Fine-Tune File tab
    await createFineTuneTab.click();
  }

  // Wait for the page to load
  await page.waitForLoadState('networkidle');

  // Take a screenshot of the fine-tune file creation form
  await page.screenshot({ path: getScreenshotPath('fine-tune-file-creation-form.png') });
  console.log('Saved screenshot of fine-tune file creation form');

  // Get all speakers from the transcript
  console.log('Getting speakers from the transcript');
  const prompterSelect = await page.locator('select').first();
  const responderSelect = await page.locator('select').nth(1);

  // Get all speaker options
  const prompterOptions = await prompterSelect.locator('option').all();
  console.log(`Found ${prompterOptions.length} speaker options`);

  if (prompterOptions.length === 0) {
    throw new Error('No speaker options found');
  }

  // Select the first speaker as prompter
  const firstSpeakerValue = await prompterOptions[0].getAttribute('value');
  await prompterSelect.selectOption([firstSpeakerValue]);
  console.log(`Selected speaker "${firstSpeakerValue}" as prompter`);

  // Select the second speaker as responder (if available)
  if (prompterOptions.length > 1) {
    const secondSpeakerValue = await prompterOptions[1].getAttribute('value');
    await responderSelect.selectOption([secondSpeakerValue]);
    console.log(`Selected speaker "${secondSpeakerValue}" as responder`);
  } else {
    throw new Error('Need at least two speakers for fine-tune file creation');
  }

  // Take a screenshot after making selections
  await page.screenshot({ path: getScreenshotPath('after-speaker-selection.png') });
  console.log('Saved screenshot after speaker selection');

  // Set up a listener for the API response to capture the fine-tune file ID
  let fineTuneFileId = null;
  await page.route('**/white-label/*/data-source/audio-transcript/*/finetune-file', async (route, request) => {
    // Let the request continue
    await route.continue();

    // Wait for the response
    const response = await route.request().response();
    if (response) {
      try {
        const responseBody = await response.json();
        console.log('Fine-Tune File API Response:', responseBody);

        // Extract the fine-tune file ID from the response
        if (responseBody && responseBody._id) {
          fineTuneFileId = responseBody._id;
          console.log(`Captured fine-tune file ID from API response: ${fineTuneFileId}`);
        }
      } catch (error) {
        console.error('Error parsing fine-tune file API response:', error);
      }
    }
  });

  // Submit the form
  console.log('Submitting the fine-tune file creation form');
  await page.locator('form button[type="submit"]').click();

  // Wait for the form submission to complete
  await page.waitForLoadState('networkidle');

  // Take a screenshot after creating the fine-tune file
  await page.screenshot({ path: getScreenshotPath('after-creating-fine-tune-file.png') });
  console.log('Saved screenshot after creating fine-tune file');

  // Verify that the fine-tune file was created successfully
  console.log('Verifying that the fine-tune file was created successfully');

  // Check if we're on the file list tab
  const fileListVisible = await page.locator('text=No Fine tune files have been created with this transcript').count() === 0;
  expect(fileListVisible).toBeTruthy();
  console.log('Fine-tune file list is visible');

  // Step 3: Create a fine-tuned AI model using the fine-tune file
  console.log('Creating a fine-tuned AI model using the fine-tune file');

  // Navigate to the fine-tune section
  await page.goto(`http://localhost:8080/white-label/${whitelabelId}/fine-tune`);
  await page.waitForLoadState('networkidle');

  // Take a screenshot of the fine-tune page
  await page.screenshot({ path: getScreenshotPath('fine-tune-page.png') });
  console.log('Saved screenshot of fine-tune page');

  // Click the "Create New" button
  await page.click('text=Create New');

  // Wait for the form to load
  await page.waitForLoadState('networkidle');

  // Take a screenshot of the fine-tune creation form
  await page.screenshot({ path: getScreenshotPath('fine-tune-creation-form.png') });
  console.log('Saved screenshot of fine-tune creation form');

  // Fill in the form
  await page.fill('input.input[type="text"]', 'E2E Test Fine-Tune');
  await page.fill('textarea.textarea', 'Created by E2E test for audio transcript to fine-tune');

  // Submit the form
  await page.click('button:has-text("Create Fine Tune")');

  // Wait for the form submission to complete
  await page.waitForLoadState('networkidle');

  // Take a screenshot after creating the fine-tune
  await page.screenshot({ path: getScreenshotPath('after-creating-fine-tune.png') });
  console.log('Saved screenshot after creating fine-tune');

  // Extract the fine-tune ID from the URL
  const fineTuneUrl = page.url();
  const fineTuneId = fineTuneUrl.split('/').pop();
  console.log(`Fine-tune ID: ${fineTuneId}`);

  // Verify that we're on the fine-tune detail page
  const fineTuneDetailVisible = await page.locator('text=Currently Tuning: false').count() > 0 ||
                               await page.locator('text=Base Model:').count() > 0;
  expect(fineTuneDetailVisible).toBeTruthy();
  console.log('Fine-tune detail page is visible');

  // Clean up: Delete the fine-tune
  console.log('Cleaning up by deleting the fine-tune');

  // Click the Delete button
  await page.click('button:has-text("Delete Fine Tune")');

  // Wait for the confirmation dialog and confirm
  await page.waitForTimeout(1000);
  await page.click('.modal button:has-text("Delete")');

  // Wait for the deletion to complete
  await page.waitForLoadState('networkidle');

  // Take a screenshot after deleting the fine-tune
  await page.screenshot({ path: getScreenshotPath('after-deleting-fine-tune.png') });
  console.log('Saved screenshot after deleting fine-tune');

  // Verify that we were redirected to the fine-tune list page
  const isOnFineTuneListPage = page.url().includes('/fine-tune') && !page.url().includes(`/${fineTuneId}`);
  expect(isOnFineTuneListPage).toBeTruthy();
  console.log('Redirected to fine-tune list page after deletion');
});
