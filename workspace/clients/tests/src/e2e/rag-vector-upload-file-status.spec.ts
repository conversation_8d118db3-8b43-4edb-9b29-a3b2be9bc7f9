import { test, expect } from '@playwright/test';
import { verifyLoggedIn, ensureLoggedIn, navigateToWhitelabelPage, getFirstWhitelabelId, ensureWhitelabelExists } from './utils/common';
import path from 'path';

test.describe('RAG Vector Upload File and Check Status', () => {
  test('should upload a file to RAG Vector and check status', async ({ page }) => {
    // Set a longer timeout for this test
    test.setTimeout(120000);
    console.log('Starting RAG Vector file upload test');

    // Ensure the user is logged in
    await ensureLoggedIn(page);

    // Ensure at least one whitelabel exists
    await ensureWhitelabelExists(page);

    // Navigate directly to the whitelabel page
    console.log('Navigating directly to the whitelabel page');
    await page.goto('http://localhost:8080/white-label');
    await page.waitForLoadState('networkidle');

    // Get the first whitelabel ID from the href attribute
    let whitelabelId;
    const whitelabelHref = await page.locator('.whitelabel-ai-card .title a.clickable-text, .card.whitelabel-ai-card .title a.clickable-text').first().getAttribute('href');

    if (whitelabelHref) {
      whitelabelId = whitelabelHref.split('/').pop();
      console.log(`Using whitelabel ID: ${whitelabelId}`);
    } else {
      console.error('Could not find whitelabel ID');
      throw new Error('Could not find whitelabel ID');
    }

    // Navigate directly to the RAG Vector page
    console.log('Navigating directly to the RAG Vector page');
    await page.goto(`http://localhost:8080/white-label/${whitelabelId}/rag-vector`);
    await page.waitForLoadState('networkidle');

    // Wait for the page to load
    await page.waitForTimeout(2000);

    // Look for any upload button
    console.log('Looking for upload button');
    const uploadButton = page.locator('.rag-upload-files-dropdown, button:has-text("Upload File"), button:has-text("Upload Files"), button.button:has-text("Upload")');

    if (await uploadButton.count() > 0) {
      console.log('Found upload button, clicking it');
      await uploadButton.first().click();

      // Wait for the dropdown menu to appear if it's a dropdown
      await page.waitForTimeout(1000);

      // Try to click on "Upload Files" option if it exists
      const uploadFilesOption = page.getByText('Upload Files');
      if (await uploadFilesOption.count() > 0) {
        console.log('Found Upload Files option, clicking it');
        await uploadFilesOption.click();
      } else {
        console.log('No Upload Files option found, continuing with current UI');
      }
    } else {
      console.error('Upload button not found');
      throw new Error('Upload button not found - test cannot continue');
    }

    // Wait for the upload dialog to appear
    await page.waitForTimeout(1000);

    // Get the file input element
    const fileInput = page.locator('input[type="file"]');

    // Set the file to upload
    const filePath = path.join(__dirname, '../../test-files/raw-rag-example.pdf');
    console.log(`Using file path: ${filePath}`);
    await fileInput.setInputFiles(filePath);

    // Wait for the file to be added to the list
    await page.waitForTimeout(1000);

    // Click the "Add File" button to submit
    await page.getByRole('button', { name: 'Add File' }).click();

    // Wait for the upload to complete (15 seconds)
    console.log('Waiting for file upload to complete...');
    await page.waitForTimeout(15000);

    // Navigate to the files page to check status
    console.log('Navigating to files page to check status');
    await page.goto(`http://localhost:8080/white-label/${whitelabelId}/rag-vector/files`);
    await page.waitForLoadState('networkidle');

    // Wait for the page to load
    await page.waitForTimeout(2000);

    // Look for any table that might contain the file list
    console.log('Looking for file list table');
    const fileListWrapper = page.locator('.rag-vector-file-list-wrapper, table, .file-list, .files-table');

    // Take a screenshot to see what's on the page
    await page.screenshot({ path: 'rag-vector-file-list.png' });

    if (await fileListWrapper.count() > 0) {
      console.log('Found file list table');
      await expect(fileListWrapper.first()).toBeVisible();

      // Try to get the status cell from the table
      const statusCell = fileListWrapper.first().locator('tbody tr:first-child td:nth-child(3)');

      if (await statusCell.count() > 0) {
        // Check if the status is "Complete" or similar
        const statusText = await statusCell.textContent();
        console.log(`File status: ${statusText}`);

        // Verify the file was processed successfully
        // This might be "Complete", "Processed", or another status indicating success
        if (statusText) {
          expect(statusText).not.toContain('Error');
          expect(statusText).not.toContain('Failed');
        } else {
          console.log('Status cell was empty');
        }
      } else {
        console.log('Status cell not found in table');
      }
    } else {
      console.log('File list table not found, test may need updating for current UI');
    }

    // Take a screenshot of the file list
    await page.screenshot({ path: 'rag-vector-file-upload-status.png' });

    console.log('Test completed successfully');
  });
});