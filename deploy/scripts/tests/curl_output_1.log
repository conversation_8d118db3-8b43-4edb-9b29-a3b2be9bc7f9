* Host chat.stage.divinci.app:443 was resolved.
* IPv6: (none)
* IPv4: ***********, **************
*   Trying ***********:443...
* Connected to chat.stage.divinci.app (***********) port 443
* ALPN: curl offers h2,http/1.1
* (304) (OUT), TLS handshake, Client hello (1):
} [327 bytes data]
*  CAfile: /etc/ssl/cert.pem
*  CApath: none
* (304) (IN), TLS handshake, Server hello (2):
{ [122 bytes data]
* (304) (IN), TLS handshake, Unknown (8):
{ [19 bytes data]
* (304) (IN), TLS handshake, Certificate (11):
{ [2537 bytes data]
* (304) (IN), TLS handshake, CERT verify (15):
{ [79 bytes data]
* (304) (IN), TLS handshake, Finished (20):
{ [36 bytes data]
* (304) (OUT), TLS handshake, Finished (20):
} [36 bytes data]
* SSL connection using TLSv1.3 / AEAD-CHACHA20-POLY1305-SHA256 / [blank] / UNDEF
* ALPN: server accepted h2
* Server certificate:
*  subject: CN=chat.stage.divinci.app
*  start date: Apr 23 03:36:59 2025 GMT
*  expire date: Jul 22 04:36:56 2025 GMT
*  subjectAltName: host "chat.stage.divinci.app" matched cert's "chat.stage.divinci.app"
*  issuer: C=US; O=Google Trust Services; CN=WE1
*  SSL certificate verify ok.
* using HTTP/2
* [HTTP/2] [1] OPENED stream for https://chat.stage.divinci.app/
* [HTTP/2] [1] [:method: GET]
* [HTTP/2] [1] [:scheme: https]
* [HTTP/2] [1] [:authority: chat.stage.divinci.app]
* [HTTP/2] [1] [:path: /]
* [HTTP/2] [1] [user-agent: curl/8.7.1]
* [HTTP/2] [1] [accept: */*]
* [HTTP/2] [1] [cf-access-client-id: f13e5d39e1997a5ddb674362e73199c5.access]
* [HTTP/2] [1] [cf-access-client-secret: c2190b8f11da9b3e8244e18e0a29225f23e70784561ef244867bbc4a2f925752]
> GET / HTTP/2
> Host: chat.stage.divinci.app
> User-Agent: curl/8.7.1
> Accept: */*
> CF-Access-Client-Id: f13e5d39e1997a5ddb674362e73199c5.access
> CF-Access-Client-Secret: c2190b8f11da9b3e8244e18e0a29225f23e70784561ef244867bbc4a2f925752
> 
* Request completely sent off
< HTTP/2 526 
< date: Wed, 23 Apr 2025 18:17:28 GMT
< content-type: text/plain; charset=UTF-8
< content-length: 15
< server: cloudflare
< report-to: {"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v4?s=KbaVfVXwcIJGx2xFIej01%2FxxUqzSTSPRCgqgkEVlymEl2Urv0KjS0eaVhAxzMLSUuPJmesk%2FPMgIq52MFfaI2AheXdoLpMRprFcZKIW5qT4HDvCsxIF8JpNmE34vSeAuh0Orai9Rou1v"}],"group":"cf-nel","max_age":604800}
< nel: {"success_fraction":0,"report_to":"cf-nel","max_age":604800}
< strict-transport-security: max-age=0; includeSubDomains; preload
< cache-control: private, max-age=0, no-store, no-cache, must-revalidate, post-check=0, pre-check=0
< expires: Thu, 01 Jan 1970 00:00:01 GMT
< cf-ray: 934f5ba31e4027d7-SAN
< expect-ct: max-age=86400, enforce
< referrer-policy: same-origin
< x-content-type-options: nosniff
< x-frame-options: SAMEORIGIN
< x-xss-protection: 1; mode=block
< alt-svc: h3=":443"; ma=86400
< server-timing: cfL4;desc="?proto=TCP&rtt=0&min_rtt=0&rtt_var=0&sent=0&recv=0&lost=0&retrans=0&sent_bytes=0&recv_bytes=0&delivery_rate=0&cwnd=0&unsent_bytes=0&cid=4d42f93815868131&ts=82&x=0"
< 
{ [15 bytes data]
* Connection #0 to host chat.stage.divinci.app left intact
